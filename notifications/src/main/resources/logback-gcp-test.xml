<configuration>
    <appender name="CLOUD" class="com.google.cloud.logging.logback.LoggingAppender">
        <log>application.log</log>
        <resourceType>gae_app</resourceType>
        <loggingEventEnhancer>com.moregames.base.util.LogEnhancer</loggingEventEnhancer>
    </appender>

    <appender name="CRON" class="com.google.cloud.logging.logback.LoggingAppender">
        <log>playtime_cron.log</log>
        <resourceType>gae_app</resourceType>
        <loggingEventEnhancer>com.moregames.base.util.LogEnhancer</loggingEventEnhancer>
    </appender>

    <root level="DEBUG">
        <appender-ref ref="CLOUD"/>
    </root>
    <logger name="io.grpc.netty" level="info"/>
    <logger name="io.netty" level="warn"/>
    <logger name="sun.net" level="info"/>
    <logger name="com.zaxxer.hikari" level="info"/>
    <logger name="cron" level="debug">
        <appender-ref ref="CRON"/>
    </logger>
</configuration>
