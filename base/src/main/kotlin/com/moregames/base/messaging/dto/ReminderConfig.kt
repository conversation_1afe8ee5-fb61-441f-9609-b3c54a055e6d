package com.moregames.base.messaging.dto

import com.moregames.base.util.getUtcOffsetMinutes
import com.moregames.base.util.localUtcDateTime
import com.moregames.base.util.utcInstant
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalAdjusters

data class ReminderConfig(
  val eventTime: Instant,
  val nextNotifyAtType: NextNotifyAtType,
  val daysOfInactivity: Int?,
  val firstNotifyAtType: NextNotifyAtType? = null,
) {

  enum class NextNotifyAtType {
    DAY_1, DAY_2, DAY_4, DAY_6, FRIDAY, FRIDAY_SUNDAY, FRIDAY_SATURDAY_SUNDAY, DAY1_DAY2_FRIDAY_SUNDAY
  }

  companion object {

    const val EMAIL_NOTIFY_AFTER_DAYS_OF_INACTIVITY = 6

    /**
     * @from          - moment of time relatively to which we need next moment.
     *                  e.g.: if from is Monday, 14:30, then next with DAY_2 will be nearest Wednesday 14:30
     * @withRule      - defines how to calculate result
     * @noEarlierThan - if it's known beforehead, that we need to skip few dates - this parameter can be used
     * @beginningAt   - for tricky rules when we need to know whether it's 1st, 2nd or any else further day.
     *                  for example - rule DAY1_DAY2_FRIDAY_SUNDAY. if beginningAt is Monday and we're asking "which is
     *                  the next day for this rule if now ("from") is Tuesday?", then answer will be Wednesday, as it's
     *                  possible to compute Wednesday is the 2nd day from Monday. And few weeks later from beginningAt
     *                  for this rule answers could be only Friday or Sunday.
     * @countryCode   - country to respect timezones for correct day (e.g. Sunday) computation.
     *
     */
    fun nextDailyShifted(
      from: Instant, withRule: NextNotifyAtType, noEarlierThan: Instant?, beginningAt: Instant, countryCode: String
    ): Instant {
      /* server works in UTC.
         for example now it's 13 UTC
         we are in Japan, it's 22 in Japan. let's pretend we're in fact have 22 utc ( = 13 + 9)
         make calculation and revert back our 9 hours */

      val timezoneUtcMinutesShift = getUtcOffsetMinutes(countryCode)
      val fromTimezoneShifted = from.plus(timezoneUtcMinutesShift, ChronoUnit.MINUTES)
      val beginningAtTimezoneShifted = beginningAt.plus(timezoneUtcMinutesShift, ChronoUnit.MINUTES)
      val noEarlierThanTimezoneShifted = noEarlierThan?.plus(timezoneUtcMinutesShift, ChronoUnit.MINUTES)

      var dailyShifted = nextDailyShifted(fromTimezoneShifted, withRule, beginningAtTimezoneShifted)
      if (noEarlierThanTimezoneShifted != null) {
        while (dailyShifted.isBefore(noEarlierThanTimezoneShifted))
          dailyShifted = nextDailyShifted(dailyShifted, withRule, beginningAtTimezoneShifted)
      }
      return dailyShifted.minus(timezoneUtcMinutesShift, ChronoUnit.MINUTES)
    }

    private fun nextDailyShifted(from: Instant, withRule: NextNotifyAtType, beginningAt: Instant): Instant {
      val localDate = from.localUtcDateTime()
      return when (withRule) {
        NextNotifyAtType.DAY_1 -> localDate.plusDays(1).utcInstant()
        NextNotifyAtType.DAY_2 -> localDate.plusDays(2).utcInstant()
        NextNotifyAtType.DAY_4 -> localDate.plusDays(4).utcInstant()
        NextNotifyAtType.DAY_6 -> localDate.plusDays(6).utcInstant()
        NextNotifyAtType.FRIDAY -> localDate.with(TemporalAdjusters.next(java.time.DayOfWeek.FRIDAY)).utcInstant()
        NextNotifyAtType.FRIDAY_SUNDAY -> minOf(
          localDate.with(TemporalAdjusters.next(java.time.DayOfWeek.FRIDAY)),
          localDate.with(TemporalAdjusters.next(java.time.DayOfWeek.SUNDAY)),
        )
          .utcInstant()

        NextNotifyAtType.FRIDAY_SATURDAY_SUNDAY -> minOf(
          localDate.with(TemporalAdjusters.next(java.time.DayOfWeek.FRIDAY)),
          localDate.with(TemporalAdjusters.next(java.time.DayOfWeek.SATURDAY)),
          localDate.with(TemporalAdjusters.next(java.time.DayOfWeek.SUNDAY)),
        )
          .utcInstant()

        NextNotifyAtType.DAY1_DAY2_FRIDAY_SUNDAY -> computeDay1Day2FridaySunday(from, beginningAt)
      }
    }

    private fun computeDay1Day2FridaySunday(from: Instant, beginningAt: Instant): Instant {
      val diff = Duration.between(beginningAt, from).toDays()

      return when {
        diff < 6 -> nextDailyShifted(from, NextNotifyAtType.DAY_1, beginningAt)
        diff < 13 -> nextDailyShifted(from, NextNotifyAtType.DAY_2, beginningAt)
        else -> nextDailyShifted(from, NextNotifyAtType.FRIDAY_SUNDAY, beginningAt)
      }
    }
  }
}