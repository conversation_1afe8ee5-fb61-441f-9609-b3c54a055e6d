package com.moregames.base.abtesting

import com.moregames.base.abtesting.variations.*
import com.moregames.base.abtesting.variations.gamescelebration.AndroidGamesCelebrationVariation
import com.moregames.base.abtesting.variations.gamescelebration.IosGamesCelebrationVariation
import com.moregames.base.abtesting.variations.gamescoinsbooster.IosGamesCoinsBoosterVariation
import com.moregames.base.exceptions.InvalidEnumKey

sealed interface ExperimentBase {
  val key: String
  val base: TypedVariationBase<*>?

  companion object {
    fun byKey(key: String): ExperimentBase {
      return when (key) {
        in ClientExperiment.keyMap -> ClientExperiment.byKey(key)
        in GamesExperiment.keyMap -> GamesExperiment.byKey(key)
        else -> throw InvalidEnumKey(key, ExperimentBase::class)
      }
    }
  }
}

enum class ClientExperiment(override val key: String, override val base: TypedVariationBase<*>? = null) : ExperimentBase {
  ANDROID_GAMES_ORDER("androidGamesOrder"),
  TREMENDOUS_GIFT_CARDS("tremendousGiftCards"),
  VIDEO_OFFER_COOL_DOWN("videoOfferCoolDown"),
  SHOW_VIDEO_PREVIEW("showVideoPreview"),
  SHOW_TOP_RUNNING_BAR("showTopRunningBar"),
  EARNINGS_MODEL_V2("earningsModelV2"),
  SHOW_TIMER_IN_COIN_GOAL("showTimerInCoinGoal"),
  SEON_EMAIL_VERIFICATION("seonEmailVerification"),
  USE_AMPLITUDE_ANALYTICS("useAmplitudeAnalytics"),
  GAMES_PLAY_INTEGRITY("gamesPlayIntegrity"),
  ENABLE_VENMO("enableVenmo"),
  EM2_INSTALL_BONUS_SIZE("installBonusSize"),
  ANDROID_GPS_VERIFICATION("androidGpsVerification"),
  LESS_ADS_GAMES_ON_BOARDING("lessAdsGamesOnboarding"),
  IOS_USERS_INTERVIEW("iosUsersInterview"),
  IOS_GAMES_ATT_CONSENT("iosGamesAttConsent"),
  IOS_GAME_COIN_GOALS("iosCoinGoalsV2"),
  BEST_COINS_BADGE("bestCoinsBadge"),
  ANDROID_CASHOUT_FOR_COINS("androidCashoutForCoins"),
  ANDROID_CASHOUT_SURVEY("androidCashoutSurvey"),
  ANDROID_HIDE_EARNINGS("androidHideEarnings"),
  ANDROID_NOTIFY_TO_PLAY("androidNotifyToPlay"),
  ANDROID_DIGITAL_TURBINE_IGNITE_INSTALL("androidDtiInstall"),
  ANDROID_PAYMENT_PROVIDER_SURVEY("androidPaymentProviderSurvey", base = PaymentProviderSurveyVariation),
  ANDROID_CASHOUT_PROGRESS_BAR("androidCashoutProgressBar"),
  ANDROID_INCOMPLETE_CASHOUT_RESTORING("androidIncompleteCashoutRestoring"),
  COIN_NOTIFICATION_COOLDOWN("coinNotificationCoolDown", base = CooldownCoinNotificationVariation),
  ANDROID_SHOW_OFW_AFTER_FIRST_SUCCESSFUL_CASHOUT("androidShowOfwAfterFirstSC"),
  COINS_DO_NOT_RESET_ANDROID("coinsDoNotResetAndroid", base = CoinsDoNotResetVariation),
  UNIFIED_ID("unifiedId"),
  RAMP_ID("rampId"),
  ANDROID_PAYPAL_HINTS("androidPaypalHints"),
  ANDROID_FACE_SCAN_PRE_SCREEN("androidFaceScanPreScreen", base = AndroidFaceScanPreScreenVariation),
  AA_TEST("aaTest"),
  ANDROID_PAYPAL_LIMITS("androidPaypalLimits"),
  ANDROID_GAMES_BALANCE_UPDATE_NOTIFICATIONS("androidGamesBalanceUpdateNotifications", base = GameBalanceUpdateNotificationVariation),
  IOS_GAMES_BALANCE_UPDATE_NOTIFICATIONS("iosGamesBalanceUpdateNotifications", base = GameBalanceUpdateNotificationVariation),
  ANDROID_INSTALL_GAMES_REMINDERS("androidInstallGamesReminders", base = InstallGamesRemindersVariation),
  HIGHER_DAILY_QUOTAS("higherDailyQuotas", base = HigherDailyQuotasVariation),
  MODIFIED_UNCLAIMED_EARNINGS_NOTIFICATION("modifiedUnclaimedEarningsNotification", base = ModifiedUnclaimedEarningsNotificationVariation),

  // IMPORTANT OFFBOARDING NOTE: do not send freshly introduced (unknown) tutorial steps to 66th Android version (66th version regression bug).
  TUTORIAL_FULL_SCREEN("tutorialFullScreen"),
  MULTI_OFFERWALL("multiOfferwall", base = MultiOfferwallVariation),
  ANDROID_FULLSCREEN_CASHOUT_FORM_STYLE("androidFullscreenCashout", base = AndroidFullscreenCashoutVariation),
  ANDROID_CHALLENGES("androidChallenges"),
  ANDROID_DEMO_GAMES("androidDemoGames", base = AndroidDemoGamesVariation),
  ANDROID_COIN_GOAL_TEXT("androidCoinGoalText"),
  ANDROID_COIN_GOAL_VARIATIONS("androidCoinGoalVariations"),
  ANDROID_TUTORIAL_TEXT("androidTutorialText"),
  IOS_GAMES_COINS_BOOSTER("iosGamesCoinsBooster", base = IosGamesCoinsBoosterVariation),
  ANDROID_EARN_PLAYING_GAMES_TEXT("androidEarnPlayingGamesText"),
  ANDROID_BEGINNER_FRIENDLY_GAMES("androidBeginnerFriendlyGames"),
  ANDROID_TASKS_IN_PRE_GAME("androidTasksInPreGame"),
  ANDROID_PLAY_AT_LEAST_BADGES("androidPlayAtLeastBadges"),
  ANDROID_CASHOUT_2X_OFFER("androidCashout2xOffer", base = AndroidCashout2xOfferVariation),
  SPECIAL_CASHOUT_OFFERS("specialCashoutOffers", base = SpecialCashoutOffersVariation),
  ANDROID_COINS_RENAMING("androidCoinsRenaming"),
  ANDROID_CASHOUT_READY_NOTIFICATION("androidCashoutReadyNotification", base = AndroidCashoutReadyNotificationVariation),
  ANDROID_HIGHLIGHT_GAMES_ON_LOW_EARNINGS("androidHighlightGamesOnLowEarnings"),
  ANDROID_ONBOARDING_PROGRESS_BAR("androidOnboardingProgressBar", base = AndroidOnboardingProgressBarVariation),
  ANDROID_ANIMATION_TO_CELEBRATE_EARNINGS("androidAnimationToCelebrateEarnings"),
  ANDROID_SHOW_PAYPAL_LOGO("androidShowPaypalLogo"),
  ANDROID_CASH_STREAK("androidCashStreak", base = AndroidCashStreakVariation),
  ANDROID_NEW_SOLITAIRE("androidNewSolitaire"),
  ANDROID_GAMES_CELEBRATION("androidGamesCelebration", base = AndroidGamesCelebrationVariation),
  IOS_GAMES_CELEBRATION("iosGamesCelebration", base = IosGamesCelebrationVariation),
  ANDROID_GAME_STORIES("androidGameStories"),
  ANDROID_HIDE_OFW("androidHideOfw"),
  FTUE("ftueEvents"),
  NO_PROMOS_DURING_CHALLENGES("noPromosDuringChallenges"),
  OFFERWALL_CAMPAIGNS("offerwallCampaigns"),
  BIG_OFW_GB("bigOfwGb"),
  HIGHLIGHT_OFW_LOW_ECPM("highlightOfwLowEcpm"),
  ANDROID_SHOW_COINS_CONVERSION_RATIO("androidShowCoinsConversionRatio"),
  TAPJOY_ADDITIONAL_OFW("tapjoyAdditionalOfw", base = TapjoyAdditionalOfwVariation),
  FYBER_ADDITIONAL_OFW("fyberAdditionalOfw", base = FyberAdditionalOfwVariation),
  DISABLE_OFW_MX("disableOfwMx"),
  BALANCE_UPDATED_NOTIFICATION_WITH_AMOUNT("balanceUpdatedNotificationWithAmount", base = BalanceUpdatedNotificationWithAmountVariation),
  ANDROID_LUCKY_HOUR_2("androidLuckyHour2"),
  BUBBLE_CHEF_VS_BUBBLE_POP("bubbleChefVSbubblePop"),
  ANDROID_SILENT_COINS_UPDATE_NOTIFICATION("androidSilentCoinsNotification", base = AndroidSilentCoinsUpdateNotificationVariation),

  //  BONUS_BANK("bonusBank"),
  ANDROID_PRE_GAME_SCREEN("androidPreGameScreen", base = AndroidPreGameScreenVariation),
  IOS_BOOSTED_GAMES("iosBoostedGames"),
  ANDROID_CUSTOM_GAME_PAGES_V2("androidCustomGamePagesV2"),
  SPECIAL_OFFER_AFTER_CHALLENGE_CLAIM("specialOfferAfterChallengeClaim", base = SpecialOfferAfterChallengeClaimVariation),
  ANDROID_NEW_OFFER_BUTTON("androidNewOfferButton", base = AndroidNewOfferButtonVariation),
  ANDROID_USER_GAME_RANKING("androidUserGameRanking")
  ;

  companion object {
    val keyMap = entries.associateBy { it.key }
    fun byKey(key: String) = keyMap[key] ?: throw InvalidEnumKey(key, ClientExperiment::class)
  }
}
