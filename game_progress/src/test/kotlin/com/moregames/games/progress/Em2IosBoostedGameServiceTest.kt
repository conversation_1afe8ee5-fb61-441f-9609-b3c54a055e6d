package com.moregames.games.progress

import assertk.assertThat
import assertk.assertions.isEqualByComparingTo
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment.IOS_BOOSTED_GAMES
import com.moregames.base.app.BuildVariant
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.base.util.redis.SafeJedisClient
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.mockito.kotlin.argThat
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import java.math.BigDecimal
import java.time.LocalDate
import kotlin.test.Test

class Em2IosBoostedGameServiceTest {
  private val safeJedisClient: SafeJedisClient = mock()
  private val iosBoostedGamesPersistenceService: IosBoostedGamesPersistenceService = mock()
  private val abTestingService: AbTestingService = mock()
  private val timeService: TimeService = mock()

  private val service = Em2IosBoostedGameService(
    safeJedisClient = safeJedisClient,
    iosBoostedGamesPersistenceService = iosBoostedGamesPersistenceService,
    abTestingService = abTestingService,
    timeService = timeService,
    buildVariant = BuildVariant.PRODUCTION
  )

  companion object {
    const val USER_ID = "userId"
    const val GAME_ID = 12345
    const val OTHER_GAME_ID = 54321
    val today: LocalDate = LocalDate.now()
  }

  @BeforeEach
  fun init() {
    abTestingService.mock({ isUserExperimentParticipant(USER_ID, IOS_BOOSTED_GAMES) }, true)
    safeJedisClient.mock({ get(Em2IosBoostedGameService.CACHE_KEY) }, GAME_ID.toString())
    timeService.mock({ today() }, today)
  }

  @Test
  fun `SHOULD return 1 ON getCoeff WHEN user is not experiment participant`() {
    abTestingService.mock({ isUserExperimentParticipant(USER_ID, IOS_BOOSTED_GAMES) }, false)

    runBlocking {
      service.getCoeff(USER_ID, GAME_ID)
    }.let { assertThat(it).isEqualByComparingTo(BigDecimal.ONE) }
  }

  @Test
  fun `SHOULD return 1 ON getCoeff WHEN current game is not boosted`() {
    safeJedisClient.mock({ get(Em2IosBoostedGameService.CACHE_KEY) }, OTHER_GAME_ID.toString())

    runBlocking {
      service.getCoeff(USER_ID, GAME_ID)
    }.let { assertThat(it).isEqualByComparingTo(BigDecimal.ONE) }
  }

  @Test
  fun `SHOULD return 1p2 ON getCoeff WHEN current game is boosted`() {
    runBlocking {
      service.getCoeff(USER_ID, GAME_ID)
    }.let { assertThat(it).isEqualByComparingTo(BigDecimal("1.2")) }
  }

  @Test
  fun `SHOULD fetch boosted game id from persistence WHEN cache is empty`() {
    safeJedisClient.mock({ get(Em2IosBoostedGameService.CACHE_KEY) }, null)
    iosBoostedGamesPersistenceService.mock({ findTodayIosBoostedGameId(today) }, GAME_ID)

    runBlocking {
      service.getCoeff(USER_ID, GAME_ID)
    }.let { assertThat(it).isEqualByComparingTo(BigDecimal("1.2")) }

    verifyBlocking(iosBoostedGamesPersistenceService) { findTodayIosBoostedGameId(today) }
    verifyBlocking(safeJedisClient) {
      set(
        key = eq(Em2IosBoostedGameService.CACHE_KEY),
        value = eq(GAME_ID.toString()),
        params = argThat { this.toString() == "[ex, 600]" }
      )
    }
  }

  @Test
  fun `SHOULD use NO_BOOSTED_GAME_ID_STUB WHEN no boosted game is found`() {
    safeJedisClient.mock({ get(Em2IosBoostedGameService.CACHE_KEY) }, null)
    iosBoostedGamesPersistenceService.mock({ findTodayIosBoostedGameId(today) }, null)

    runBlocking {
      service.getCoeff(USER_ID, GAME_ID)
    }.let { assertThat(it).isEqualByComparingTo(BigDecimal.ONE) }

    verifyBlocking(iosBoostedGamesPersistenceService) { findTodayIosBoostedGameId(today) }
    verifyBlocking(safeJedisClient) {
      set(
        key = eq(Em2IosBoostedGameService.CACHE_KEY),
        value = eq("-1"),
        params = argThat { this.toString() == "[ex, 600]" }
      )
    }
  }
}