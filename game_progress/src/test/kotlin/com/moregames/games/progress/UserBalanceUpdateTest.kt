package com.moregames.games.progress

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.games.progress.UserGameBalancePersistenceService.UserGameCoinsDataEm2
import org.junit.jupiter.api.Test
import java.math.BigDecimal

class UserBalanceUpdateTest {

  @Test
  fun `SHOULD return coins ON applyReplaceMethodEm2 WHEN add method`() {
    val existingCoins = UserGameCoinsDataEm2(
      coins = BigDecimal("42"),
      calculatedCoins = 1860,
      fiveMinIntervalCoins = 9880,
      fiveMinIntervalCoinsTransactionsCount = 7149,
      lastPlayedAt = null,
      firstPlayedAt = null,
      otherGameLastPlayedAt = null
    )
    val update = UserBalanceUpdate(
      coins = 1870, method = Method.ADD, isDemoGame = false
    )
    val expected = 1870

    val actual = update.applyReplaceMethodEm2(existingCoins)

    assertThat(actual).isEqualTo(expected)
  }

  @Test
  fun `SHOULD calculate coins ON applyReplaceMethodEm2 WHEN replace method`() {
    val existingCoins = UserGameCoinsDataEm2(
      coins = BigDecimal("42"),
      calculatedCoins = 1860,
      fiveMinIntervalCoins = 9880,
      fiveMinIntervalCoinsTransactionsCount = 7149,
      lastPlayedAt = null,
      firstPlayedAt = null,
      otherGameLastPlayedAt = null
    )
    val update = UserBalanceUpdate(
      coins = 1870, method = Method.REPLACE, isDemoGame = false
    )
    val expected = 10

    val actual = update.applyReplaceMethodEm2(existingCoins)

    assertThat(actual).isEqualTo(expected)
  }

}