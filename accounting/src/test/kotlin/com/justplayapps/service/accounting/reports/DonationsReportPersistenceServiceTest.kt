package com.justplayapps.service.accounting.reports

import assertk.assertThat
import com.google.inject.Inject
import com.justplayapps.service.accounting.reports.donation.DonationsReportPersistenceService
import com.justplayapps.service.accounting.subscribers.PaymentPersistenceService
import com.justplayapps.service.accounting.subscribers.PaymentPersistenceServiceTest.Companion.cashoutRequest
import com.justplayapps.service.accounting.tables.PaymentTable
import com.moregames.base.app.PaymentProviderType
import com.moregames.base.app.PaymentProviderType.*
import com.moregames.base.table.DatabaseExtension
import com.moregames.base.util.TimeService
import com.moregames.base.util.localUtcDate
import kotlinx.coroutines.runBlocking
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.deleteAll
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mockito.`when`
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDateTime
import java.time.YearMonth
import java.time.ZoneOffset.UTC
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.*
import kotlin.test.assertEquals

@ExtendWith(DatabaseExtension::class)
class DonationsReportPersistenceServiceTest(
  private val database: Database
) {
  @Inject
  private val timeService: TimeService = mock()
  private lateinit var paymentPersistenceService: PaymentPersistenceService
  private lateinit var underTest: DonationsReportPersistenceService

  @BeforeEach
  fun before() {
    `when`(timeService.now()).thenReturn(Instant.now())
    paymentPersistenceService = PaymentPersistenceService(database, timeService)
    underTest = DonationsReportPersistenceService(database)
    transaction(database) {
      PaymentTable.deleteAll()
    }
  }

  @Test
  fun `SHOULD load donations report on loadMonthlyDonationsData`() {
    createPaymentsRecord()
    createPaymentsRecord(market = "CA")
    createPaymentsRecord(provider = DOCTORS_WITHOUT_BORDERS)
    createPaymentsRecord(provider = PAYPAL)

    val period = YearMonth.now()
    val (periodStart, periodEnd) = period.atDay(1).atStartOfDay()
      .let { it to it.plus(1, ChronoUnit.MONTHS) }

    val result = runBlocking {
      underTest.loadMonthlyDonationsData(periodStart, periodEnd)
    }

    assertThat { result.size == 2 }

    assertThat { result[0].donationsCount == 1 }
    assertThat { result[0].donationsAmountUsd == BigDecimal("5.00") }
    assertThat { result[0].charity == DOCTORS_WITHOUT_BORDERS.key }

    assertThat { result[1].donationsCount == 2 }
    assertThat { result[1].donationsAmountUsd == BigDecimal.TEN }
    assertThat { result[1].charity == UKRAINE.key }
  }

  @Test
  fun `SHOULD NOT load donations report data on loadMonthlyDonationsData WHEN createdAt is greater than periodEnd`() {
    createPaymentsRecord()
    createPaymentsRecord(market = "CA")
    createPaymentsRecord(provider = DOCTORS_WITHOUT_BORDERS)

    val now = YearMonth.now()
    val period = YearMonth.of(now.year, now.month - 1)
    val (periodStart, periodEnd) = period.atDay(1).atStartOfDay()
      .let { it to it.plus(1, ChronoUnit.MONTHS) }

    val result = runBlocking {
      underTest.loadMonthlyDonationsData(periodStart, periodEnd)
    }

    assertThat { result.isEmpty() }
  }

  @Test
  fun `SHOULD NOT load donations report data on loadMonthlyDonationsData WHEN createdAt is less than periodStart`() {
    createPaymentsRecord()
    createPaymentsRecord(market = "CA")
    createPaymentsRecord(provider = DOCTORS_WITHOUT_BORDERS)

    val now = YearMonth.now()
    val period = YearMonth.of(now.year, now.month + 1)
    val (periodStart, periodEnd) = period.atDay(1).atStartOfDay()
      .let { it to it.plus(1, ChronoUnit.MONTHS) }


    val result = runBlocking {
      underTest.loadMonthlyDonationsData(periodStart, periodEnd)
    }
    assertThat { result.isEmpty() }
  }

  @Test
  fun `SHOULD create correct donations PDF report data ON loadMonthlyDonationsPdfData`() {
    createPaymentsRecord(count = 2)
    val firstPeriod = LocalDateTime.now(UTC).minusMonths(1)
    whenever(timeService.now()).thenReturn(firstPeriod.toInstant(UTC))
    createPaymentsRecord(amount = BigDecimal("11.11"), count = 3)
    createPaymentsRecord(provider = DOCTORS_WITHOUT_BORDERS, count = 2)
    createPaymentsRecord(provider = THE_HUNGER_PROJECT)
    createPaymentsRecord(provider = PAYPAL, count = 11)
    val secondPeriod = LocalDateTime.now(UTC).minusMonths(4)
    whenever(timeService.now()).thenReturn(secondPeriod.toInstant(UTC))
    createPaymentsRecord(count = 1)
    createPaymentsRecord(provider = DOCTORS_WITHOUT_BORDERS, count = 7)
    createPaymentsRecord(provider = THE_HUNGER_PROJECT, amount = BigDecimal("0.02"), count = 2)
    createPaymentsRecord(provider = PAYPAL)
    whenever(timeService.now()).thenReturn(LocalDateTime.now(UTC).minusMonths(13).toInstant(UTC))
    createPaymentsRecord()
    createPaymentsRecord(provider = DOCTORS_WITHOUT_BORDERS)
    createPaymentsRecord(provider = THE_HUNGER_PROJECT)
    createPaymentsRecord(provider = PAYPAL)

    val periodStart = YearMonth.from(Instant.now().localUtcDate())
      .minusMonths(12)
      .atDay(1)
      .atStartOfDay()
      .atOffset(UTC)
      .atZoneSameInstant(ZonedDateTime.now().zone)
      .toLocalDateTime()
    val periodEnd = periodStart
      .plusMonths(12)

    val reportEntries = runBlocking {
      underTest.loadMonthlyDonationsPdfData(periodStart, periodEnd)
    }

    assertEquals(6, reportEntries.size)
    var expectedPeriod = firstPeriod.format(DateTimeFormatter.ofPattern("yyyy MMMM", Locale.US))
    assertEquals(expectedPeriod, reportEntries[0].period)
    assertEquals("Doctors without borders", reportEntries[0].organization)
    assertEquals(BigDecimal("10.00"), reportEntries[0].donationUsers)
    assertEquals(expectedPeriod, reportEntries[1].period)
    assertEquals("Hunger Project", reportEntries[1].organization)
    assertEquals(BigDecimal("5.00"), reportEntries[1].donationUsers)
    assertEquals(expectedPeriod, reportEntries[2].period)
    assertEquals("Save the Children", reportEntries[2].organization)
    assertEquals(BigDecimal("33.33"), reportEntries[2].donationUsers)

    expectedPeriod = secondPeriod.format(DateTimeFormatter.ofPattern("yyyy MMMM", Locale.US))
    assertEquals(expectedPeriod, reportEntries[3].period)
    assertEquals("Doctors without borders", reportEntries[3].organization)
    assertEquals(BigDecimal("35.00"), reportEntries[3].donationUsers)
    assertEquals(expectedPeriod, reportEntries[4].period)
    assertEquals("Hunger Project", reportEntries[4].organization)
    assertEquals(BigDecimal("00.04"), reportEntries[4].donationUsers)
    assertEquals(expectedPeriod, reportEntries[5].period)
    assertEquals("Save the Children", reportEntries[5].organization)
    assertEquals(BigDecimal("5.00"), reportEntries[5].donationUsers)
  }

  private fun createPaymentsRecord(
    provider: PaymentProviderType = UKRAINE,
    market: String = "US",
    amount: BigDecimal = BigDecimal("5.0"),
    count: Int = 1,
  ) {
    for (i in 1..count) {
      runBlocking {
        paymentPersistenceService.insertPayment(
          cashoutRequest.copy(
            cashoutTransactionId = UUID.randomUUID().toString(),
            provider = provider,
            amount = amount,
            market = market,
          )
        )
      }
    }
  }
}