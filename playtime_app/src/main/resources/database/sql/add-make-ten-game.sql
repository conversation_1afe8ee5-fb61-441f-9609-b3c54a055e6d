with t1 as (
    select order_key as base_order_key
    from playtime.games
    where application_id = 'com.gimica.sudoku'
      and platform = 'ANDROID'
)
update playtime.games, t1
set order_key = order_key + 1
where platform = 'ANDROID'
  and order_key > t1.base_order_key
  and application_id != 'com.justplay.app';

insert into playtime.string_resource (resource_name)
values ('$_make_ten_description'),
       ('$_make_ten_text_install'),
       ('$_make_ten_text_play'),
       ('$_make_ten_text_install_top'),
       ('$_make_ten_text_install_bottom');

insert into playtime.string_resource_translation (resource_name, language, translation)
    (with res as (select * from playtime.string_resource where resource_name like '$\_make\_ten%'),
          lang as (select distinct language from playtime.string_resource_translation)
     select res.resource_name,
            lang.language,
            (case
                 when resource_name like '%description' then 'Make ten for loyalty coins!'
                 when resource_name like '%install'
                     then 'Play Make Ten<br><br>Make ten for loyalty coins!'
                 when resource_name like '%play' then 'Play Make Ten<br><br>Make ten for loyalty coins!'
                 when resource_name like '%install\_top' then 'Play Make Ten'
                 when resource_name like '%install\_bottom' then 'Make ten for loyalty coins!'
                end) as translation
     from res
              left join lang on 1 = 1);

insert into playtime.games (id, application_id, name, description, icon_filename, image_filename, order_key,
                            info_text_install, info_text_play, order_key_alt_1, order_key_alt_2, applovin_api_key, activity_name, install_image_filename,
                            info_text_install_top, info_text_install_bottom,
                            downloads_number, rating, earning_power, publisher_id, platform)
select (select max(id)
        from playtime.games
        where platform = 'ANDROID'
          and application_id != 'com.justplay.app') + 1 as id,
       'com.gimica.maketen'                             as application_id,
       'Make Ten'                                       as name,
       '$_make_ten_description'                          as description,
       'make_ten_icon.jpg'                              as icon_filename,
       'make_ten_preview.jpg'                           as image_filename,
       (select order_key
        from playtime.games
        where application_id = 'com.gimica.sudoku'
          and platform = 'ANDROID') + 1                 as order_key,
       '$_make_ten_text_install'                        as info_text_install,
       '$_make_ten_text_play'                           as info_text_play,
       44                                               as order_key_alt_1,
       44                                               as order_key_alt_2,
       'gimica-api-key'                                 as applovin_api_key,
       'com.unity3d.player.UnityPlayerActivity'         as activity_name,
       'install_image_20230504.jpg'                     as install_image_filename,
       '$_make_ten_text_install_top'                    as info_text_install_top,
       '$_make_ten_text_install_bottom'                 as info_text_install_bottom,
       0                                                as downloads_number,
       0.00                                             as rating,
       ''                                               as earning_power,
       4                                                as publisher_id,
       'ANDROID'                                        as platform;
