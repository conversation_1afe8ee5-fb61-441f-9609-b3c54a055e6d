-- resource name
INSERT INTO playtime.string_resource (resource_name) 
VALUES
('$_brick_slider_description'), ('$_brick_slider_text_install'), ('$_brick_slider_text_play'), ('$_brick_slider_text_install_top'), ('$_brick_slider_text_install_bottom');

-- default translations

INSERT INTO playtime.string_resource_translation (resource_name, language, translation) 
VALUES
('$_brick_slider_description','en','Slide your bricks to stop the wall for coins'), 
('$_brick_slider_text_install','en','Install and Play Brick Slider<br><br>Slide your bricks to stop the wall for coins'), 
('$_brick_slider_text_play','en','Play Brick Slider<br><br>Slide your bricks to stop the wall for coins'), 
('$_brick_slider_text_install_top','en','Install and Play Brick Slider'), 
('$_brick_slider_text_install_bottom','en','Slide your bricks to stop the wall for coins');

-- inserting new game after right after com.gimica.idlemergefun

UPDATE
    playtime.games g
    ,(SELECT order_key
    FROM playtime.games
    WHERE application_id = 'com.gimica.idlemergefun') gmg
SET g.order_key = g.order_key + 10000
WHERE g.order_key < 1000
    AND g.order_key > gmg.order_key;

UPDATE
    playtime.games g
SET g.order_key = g.order_key - 10000 + 1
WHERE g.order_key > 10000
    AND g.order_key < 11000;

INSERT INTO playtime.games (
    id, application_id, do_not_show, name, description, icon_filename, image_filename, order_key, applovin_api_key,
    publisher_id, info_text_install, info_text_play, info_text_install_top, info_text_install_bottom,
    install_image_filename, is_disabled, order_key_alt_1, order_key_alt_2, activity_name, video_preview_filename, exp_image_filename,
    downloads_number, rating, earning_power, back_ground_color, is_highlighted)

SELECT
    200056                                   AS id,
    'com.gimica.brickslider'                 AS applicatioin_id,
    1                                        AS do_not_show,
    'Brick Slider'		             AS name,
    '$_brick_slider_description'             AS description,
    'brick_slider_icon.jpg'                  AS icon_filename,
    'brick_slider_bg.jpg'                    AS image_filename,
    (SELECT order_key + 1 FROM playtime.games WHERE application_id = 'com.gimica.idlemergefun')
                                             AS order_key,
    'gimica-api-key'                         AS applovin_api_key,
    4                                        AS publisher_id,
    '$_brick_slider_text_install'            AS info_text_install,
    '$_brick_slider_text_play'               AS info_text_play,
    '$_brick_slider_text_install_top'        AS info_text_install_top,
    '$_brick_slider_text_install_bottom'     AS info_text_install_bottom,
    'install_image.png'                      AS install_image_filename,
    0                                        AS is_disabled,
    44                                       AS order_key_alt_1,
    44                                       AS order_key_alt_2,
    'com.unity3d.player.UnityPlayerActivity' AS activity_name,
    null                                     AS video_preview_filename,
    null                                     AS exp_image_filename,
    0                                        AS downloads_number,
    0.00                                     AS rating,
    ''                                       AS earning_power,
    null                                     AS back_ground_color,
    0					     AS is_highlighted	
;

