package com.justplayapps.service.rewarding.earnings


import com.justplayapps.playtime.proto.PlaytimeEvents
import com.justplayapps.service.rewarding.earnings.em3.Em3CoinsService
import com.moregames.base.bus.MessageHandler
import com.moregames.base.util.fromProto
import javax.inject.Inject

class Em3CoinsRevenueCheckMessageHandler @Inject constructor(
  private val em3CoinsService: Em3CoinsService,
) {
  @MessageHandler
  suspend fun handle(message: PlaytimeEvents.Em3CoinsRevenueCheckMessage) {
    em3CoinsService.launchRevenueCheckTask(message.userId, message.platform.fromProto())
  }
}