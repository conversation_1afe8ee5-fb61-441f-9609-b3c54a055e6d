package com.moregames.playtime.user.verification

import com.google.inject.Inject
import com.google.inject.name.Named
import com.moregames.base.util.alert
import com.moregames.base.util.logger
import com.moregames.playtime.user.verification.dto.*
import com.moregames.playtime.util.buildFacetecId
import io.ktor.client.*
import io.ktor.client.request.*
import io.ktor.http.*
import javax.inject.Singleton

@Singleton
class FacetecClient @Inject constructor(
  @Named("api-http-client") private val httpClient: HttpClient,
) {

  companion object {
    const val deviceKey = "dDZZJi4ZDpeLfDueuw1UaXwVYm2CZFeM" // TODO: move to secrets!
    const val groupName = "justplay"
    const val minMatchLevel = 10

    const val deviceKeyHeader = "X-Device-Key"
    const val userAgentHeader = "X-User-Agent"
  }

  suspend fun initiateSession(): String {
    val url = "${getFacetecUrl()}/session-token"
    val result = httpClient.get<CreateSessionResponseDto>(url) {
      header(device<PERSON>eyHeader, deviceKey)
    }
    if (result.errorMessage != null) {
      logger().alert("Facetec session creation failed: ${result.errorMessage}")
    }
    return result.sessionToken ?: throw IllegalStateException("Session token can't be generated")
  }

  suspend fun checkLiveness(sessionId: String, userAgent: String, userId: String, request: VerifyFaceLivenessRequestDto): VerifyFaceLivenessResponseDto {
    if (request.faceScan.isEmpty() || request.auditTrailImage.isEmpty() || request.lowQualityAuditTrailImage.isEmpty()) {
      logger().warn("Session $sessionId for user $userId liveliness check failed: expected fields are empty. Request = $request")
      return VerifyFaceLivenessResponseDto(success = false, error = true)
    }

    val url = "${getFacetecUrl()}/enrollment-3d"
    val result = httpClient.post<VerifyFaceLivenessResponseDto>(url) {
      addHeaders(userAgent)
      body = request.copy(externalDatabaseRefID = buildFacetecId(sessionId, userId))
    }
    if (result.errorMessage != null) {
      if (result.errorMessage == "An enrollment already exists for this externalDatabaseRefID.") {
        // happens when App sends same verification data again
        logger().warn("User $userId, Session $sessionId : ${result.errorMessage}")
      } else {
        logger().error("Session $sessionId for user $userId liveliness check failed: ${result.errorMessage}")
      }
    }
    return result
  }

  suspend fun checkUniqueness(sessionId: String, userAgent: String, userId: String): UniquenessCheckResultDto {
    val url = "${getFacetecUrl()}/3d-db/search"
    val result = httpClient.post<VerifyFaceUniquenessResponseDto>(url) {
      addHeaders(userAgent)
      body = VerifyFaceUniquenessRequestDto(
        externalDatabaseRefID = buildFacetecId(sessionId, userId),
        groupName = groupName,
        minMatchLevel = minMatchLevel
      )
    }
    if (result.errorMessage != null) {
      logger().error("Session uniqueness check failed for sessionId = '$sessionId': ${result.errorMessage}")
    }

    // it's not an error to have same faces for same user
    val samefacedUsers = result.results.map { it.identifier }
      .filter { it.parseFacetecId().first != userId }
      .distinct()

    if (samefacedUsers.isNotEmpty()) {
      logger().warn("Face of user $userId is not unique. Similar users: ${samefacedUsers.joinToString()}")
    }

    return UniquenessCheckResultDto(result.success && samefacedUsers.isEmpty(), samefacedUsers.map { it.parseFacetecId().first }, samefacedUsers)
  }

  suspend fun saveToDatabase(sessionId: String, userAgent: String, userId: String): Boolean {
    val url = "${getFacetecUrl()}/3d-db/enroll"
    val result = httpClient.post<SaveFaceToSearchDatabaseResponseDto>(url) {
      addHeaders(userAgent)
      body = SaveFaceToSearchDatabaseRequestDto(
        externalDatabaseRefID = buildFacetecId(sessionId, userId),
        groupName = groupName
      )
    }
    if (result.errorMessage != null) {
      logger().error("Search db enroll failed for session '$sessionId': ${result.errorMessage}")
    }
    return result.success
  }

  suspend fun removeFromDatabase(facetecId: String): DeleteFaceFromSearchDatabaseResponseDto {
    val url = "${getFacetecUrl()}/3d-db/delete"
    val result = httpClient.post<DeleteFaceFromSearchDatabaseResponseDto>(url) {
      addHeaders("")
      body = DeleteFaceFromSearchDatabaseRequestDto(
        identifier = facetecId,
        groupName = groupName
      )
    }
    if (result.errorMessage != null) {
      logger().error("Deletion of user $facetecId facetecId from search db failed: ${result.errorMessage}")
    }
    return result
  }

  suspend fun removeFromDatabase(sessionId: String?, userId: String): DeleteFaceFromSearchDatabaseResponseDto {
    // back compatibility when faces were stored with "identifier = userId"
    val facetedId = sessionId?.let { buildFacetecId(sessionId, userId) } ?: userId
    return removeFromDatabase(facetedId)
  }

  private fun HttpRequestBuilder.addHeaders(userAgent: String) {
    header(deviceKeyHeader, deviceKey)
    header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
    if (userAgent.isNotEmpty()) header(userAgentHeader, userAgent)
  }

  private fun String.parseFacetecId(): Pair<String, String?> =
    this.split("_", limit = 2)
      .let { Pair(it.first(), it.getOrNull(1)) }

  private fun getFacetecUrl(): String {
    return "http://facetec-server.justplay-facetec.internal:8080" // justplay-facetec
  }
}
