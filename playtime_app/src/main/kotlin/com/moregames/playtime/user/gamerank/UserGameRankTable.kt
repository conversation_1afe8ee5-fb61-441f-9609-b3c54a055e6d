package com.moregames.playtime.user.gamerank

import org.jetbrains.exposed.sql.Table

object UserGameRankTable : Table("playtime.user_game_rank") {
  val userId = varchar("user_id", 36)
  val gameId = integer("game_id")
  val rank = enumerationByName("rank", 20, GameRank::class)
  val progress = integer("progress").default(0)
  val achievement = text("achievement").nullable()

  override val primaryKey = PrimaryKey(userId, gameId)
}