package com.moregames.playtime.user.usergame

import com.google.inject.Inject
import com.moregames.base.base.BasePersistenceService
import com.moregames.base.util.TimeService
import com.moregames.base.util.insertOrUpdate
import com.moregames.playtime.user.usergame.table.UserPreGameScreenOpenedTable
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.select
import java.time.Duration

class UserGamePersistenceService @Inject constructor(
  private val timeService: TimeService,
  database: Database
) : BasePersistenceService(database) {

  // returns True if record is new
  suspend fun trackUserOpenedPreGameScreenReturnNew(userId: String): Boolean {
    val lastUpdatedAt = getLastTimeUserOpenedPreGameScreen(userId)

    val alreadyExists = lastUpdatedAt != null

    //will insert new record if there is no record for this user or update current record if it was updated last time not today
    if (lastUpdatedAt == null || (Duration.between(lastUpdatedAt, timeService.now()).toDays() > 0L)) {
      dbQuery {
        UserPreGameScreenOpenedTable
          .insertOrUpdate(
            UserPreGameScreenOpenedTable.updatedAt
          ) {
            it[UserPreGameScreenOpenedTable.userId] = userId
          }
      }
    }
    return !alreadyExists
  }

  suspend fun getLastTimeUserOpenedPreGameScreen(userId: String) =
    dbQuery {
      UserPreGameScreenOpenedTable.slice(UserPreGameScreenOpenedTable.updatedAt)
        .select { UserPreGameScreenOpenedTable.userId eq userId }
        .orderBy(UserPreGameScreenOpenedTable.updatedAt, SortOrder.DESC)
        .limit(1)
        .firstOrNull()?.get(UserPreGameScreenOpenedTable.updatedAt)
    }
}