package com.moregames.playtime.user.temprestrictions

import com.google.inject.Inject
import com.moregames.base.base.BasePersistenceService
import com.moregames.base.util.TimeService
import com.moregames.base.util.insertOrUpdate
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.select
import java.time.Instant
import javax.inject.Singleton

@Singleton
class TemporalRestrictionsPersistenceService @Inject constructor(
  database: Database,
  private val timeService: TimeService
) : BasePersistenceService(database) {

  suspend fun applyTemporalRestrictions(userId: String, type: TemporalRestrictionsType, until: Instant) = dbQuery {
    UserTemporalRestrictionsTable.insertOrUpdate(UserTemporalRestrictionsTable.activeUntil) {
      it[UserTemporalRestrictionsTable.userId] = userId
      it[UserTemporalRestrictionsTable.type] = type.name
      it[activeUntil] = until
    }
  }

  suspend fun isTemporalRestrictionActive(userId: String, type: TemporalRestrictionsType): Boolean = dbQuery {
    UserTemporalRestrictionsTable
      .slice(UserTemporalRestrictionsTable.type)
      .select {
        (UserTemporalRestrictionsTable.userId eq userId) and
          (UserTemporalRestrictionsTable.type eq type.name) and
          (UserTemporalRestrictionsTable.activeUntil greater timeService.now())
      }.firstOrNull() != null
  }

}

