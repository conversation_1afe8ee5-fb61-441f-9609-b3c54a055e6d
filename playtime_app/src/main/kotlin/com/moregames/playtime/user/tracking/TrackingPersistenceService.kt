package com.moregames.playtime.user.tracking

import com.google.inject.Inject
import com.moregames.base.base.BasePersistenceService
import com.moregames.base.bus.MessageBus
import com.moregames.base.exceptions.UserRecordNotFoundException
import com.moregames.base.table.UserTable
import com.moregames.playtime.administration.blacklist.table.BlacklistGoogleAdIdTable
import com.moregames.playtime.administration.blacklist.table.TrackingDataBlacklistTable
import com.moregames.playtime.administration.whitelist.TrackingDataWhitelistTable
import com.moregames.playtime.administration.whitelist.WhitelistGoogleAdIdTable
import com.moregames.playtime.buseffects.InvalidateUserExternalIDsCacheEffect
import com.moregames.playtime.user.dto.UserGaidAuditEntryDto
import com.moregames.playtime.user.dto.UserTrackingDataAuditEntryDto
import com.moregames.playtime.user.table.TrackingDataTable
import com.moregames.playtime.user.table.UserGoogleAdIdsTable
import com.moregames.playtime.user.table.UserLimitedTrackingTable
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.neq
import java.time.Instant
import javax.inject.Singleton

@Singleton
class TrackingPersistenceService @Inject constructor(
  database: Database,
  private val messageBus: MessageBus,
) : BasePersistenceService(database) {

  suspend fun addGoogleAdIdToWhitelist(googleAdId: String) =
    dbQuery {
      WhitelistGoogleAdIdTable.insertIgnore {
        it[WhitelistGoogleAdIdTable.googleAdId] = googleAdId
      }
      BlacklistGoogleAdIdTable.deleteWhere { (BlacklistGoogleAdIdTable.googleAdId eq googleAdId) }
    }

  suspend fun removeGoogleAdIdsFromWhitelist(googleAdIds: Set<String>) =
    dbQuery {
      WhitelistGoogleAdIdTable.deleteWhere {
        WhitelistGoogleAdIdTable.googleAdId inList googleAdIds
      }
    }

  suspend fun isGoogleAdIdWhitelisted(googleAdId: String): Boolean =
    dbQuery {
      WhitelistGoogleAdIdTable.select { WhitelistGoogleAdIdTable.googleAdId eq googleAdId }.count() > 0
    }

  suspend fun blacklistGoogleAdIds(googleAdIds: Set<String>, reason: String) {
    dbQuery {
      BlacklistGoogleAdIdTable.batchInsert(googleAdIds, ignore = true) { googleAdId ->
        this[BlacklistGoogleAdIdTable.googleAdId] = googleAdId
        this[BlacklistGoogleAdIdTable.reason] = reason
      }
    }
  }

  suspend fun removeGoogleAdIdFromBlacklist(googleAdId: String) {
    dbQuery {
      BlacklistGoogleAdIdTable.deleteWhere { (BlacklistGoogleAdIdTable.googleAdId eq googleAdId) }
    }
  }

  suspend fun isGoogleAdIdBlacklisted(googleAdId: String) =
    dbQuery {
      BlacklistGoogleAdIdTable.select { (BlacklistGoogleAdIdTable.googleAdId eq googleAdId) }.count() > 0
    }

  suspend fun updateGoogleAdId(userId: String, googleAdId: String) =
    dbQuery {
      val rowsUpdated = UserTable
        .update({ UserTable.id eq userId }) {
          it[UserTable.googleAdId] = googleAdId
        }
      if (rowsUpdated != 1) {
        throw UserRecordNotFoundException(userId)
      }
      UserGoogleAdIdsTable.insert {
        it[UserGoogleAdIdsTable.userId] = userId
        it[UserGoogleAdIdsTable.googleAdId] = googleAdId
      }
      UserLimitedTrackingTable.deleteWhere { UserLimitedTrackingTable.userId eq userId }
    }.also { messageBus.publishAsync(InvalidateUserExternalIDsCacheEffect(userId)) }

  suspend fun countGoogleAdIdTracked(userId: String, since: Instant) =
    dbQuery {
      UserGoogleAdIdsTable.select {
        (UserGoogleAdIdsTable.userId eq userId) and
          (UserGoogleAdIdsTable.createdAt greater since) and
          (UserGoogleAdIdsTable.googleAdId neq TRACKING_ID_STUB)
      }.count()
    }

  suspend fun countGoogleAdIdTracked(userId: String) =
    dbQuery {
      UserGoogleAdIdsTable.select {
        (UserGoogleAdIdsTable.userId eq userId)
      }.count()
    }

  suspend fun loadGaidAuditForUserIds(associatedUserIds: List<String>): List<UserGaidAuditEntryDto> = dbQuery {
    UserGoogleAdIdsTable
      .select { UserGoogleAdIdsTable.userId inList associatedUserIds }
      .map {
        UserGaidAuditEntryDto(
          it[UserGoogleAdIdsTable.userId],
          it[UserGoogleAdIdsTable.googleAdId],
          it[UserGoogleAdIdsTable.createdAt]
        )
      }
  }

  suspend fun obfuscateUserGaidTrackingPersonals(userId: String): Int = dbQuery {
    UserGoogleAdIdsTable.update({ UserGoogleAdIdsTable.userId eq userId }) {
      it[googleAdId] = "personals deleted"
    }
  }

  suspend fun addTrackingData(userId: String, trackingData: TrackingData) =
    dbQuery {
      TrackingDataTable.insert {
        it[TrackingDataTable.userId] = userId
        it[trackingId] = trackingData.id
        it[trackingType] = trackingData.type.name
        it[appPlatform] = trackingData.platform.name
      }.also { messageBus.publishAsync(InvalidateUserExternalIDsCacheEffect(userId)) }
    }

  /**
   * @return Set of user ids that were deleted
   */
  suspend fun changeUserCurrentTrackingData(userId: String, trackingData: TrackingData): Set<String> = dbQuery {
    val nonDeletedUsersWithTheSameTrackingData = (UserTable.currentTrackingId eq trackingData.id) and
      (UserTable.currentTrackingType eq trackingData.type.name) and
      (UserTable.appPlatform eq trackingData.platform.name) and
      (UserTable.id neq userId) and
      (UserTable.isDeleted eq false)
    val usersToDelete = UserTable.slice(UserTable.id).select { nonDeletedUsersWithTheSameTrackingData }.map { it[UserTable.id] }
    UserTable
      .update({ nonDeletedUsersWithTheSameTrackingData }) {
        it[isDeleted] = true
      }
    val rowsUpdated = UserTable
      .update({ UserTable.id eq userId }) {
        it[currentTrackingId] = trackingData.id
        it[currentTrackingType] = trackingData.type.name
        it[appPlatform] = trackingData.platform.name
      }
    if (rowsUpdated != 1) {
      throw UserRecordNotFoundException(userId)
    }
    TrackingDataTable.insert {
      it[TrackingDataTable.userId] = userId
      it[trackingId] = trackingData.id
      it[trackingType] = trackingData.type.name
      it[appPlatform] = trackingData.platform.name
    }
    UserLimitedTrackingTable.deleteWhere { UserLimitedTrackingTable.userId eq userId }
    usersToDelete.toSet()
  }

  suspend fun isTrackingDataWhitelisted(trackingData: TrackingData): Boolean =
    dbQuery {
      TrackingDataWhitelistTable
        .select {
          (TrackingDataWhitelistTable.trackingId eq trackingData.id) and
            (TrackingDataWhitelistTable.trackingType eq trackingData.type.name) and
            (TrackingDataWhitelistTable.appPlatform eq trackingData.platform.name)
        }
        .count() > 0
    }

  suspend fun addTrackingDataToWhitelist(trackingData: TrackingData) =
    dbQuery {
      TrackingDataWhitelistTable.insertIgnore {
        it[trackingId] = trackingData.id
        it[trackingType] = trackingData.type.name
        it[appPlatform] = trackingData.platform.name
      }
      TrackingDataBlacklistTable.deleteWhere {
        (TrackingDataBlacklistTable.trackingId eq trackingData.id) and
          (TrackingDataBlacklistTable.trackingType eq trackingData.type.name) and
          (TrackingDataBlacklistTable.appPlatform eq trackingData.platform.name)
      }
    }

  suspend fun removeTrackingDataListFromWhitelist(trackingDataSet: Set<TrackingData>) {
    trackingDataSet.forEach {
      dbQuery {
        TrackingDataWhitelistTable.deleteWhere {
          (TrackingDataWhitelistTable.trackingId eq it.id) and
            (TrackingDataWhitelistTable.trackingType eq it.type.name) and
            (TrackingDataWhitelistTable.appPlatform eq it.platform.name)
        }
      }
    }
  }

  suspend fun loadTrackingDataAuditForUserIds(userIds: List<String>): List<UserTrackingDataAuditEntryDto> =
    dbQuery {
      TrackingDataTable
        .select { TrackingDataTable.userId inList userIds }
        .map {
          UserTrackingDataAuditEntryDto(
            it[TrackingDataTable.userId],
            TrackingData.fromValues(
              it[TrackingDataTable.trackingId],
              it[TrackingDataTable.trackingType],
              it[TrackingDataTable.appPlatform]
            ),
            it[TrackingDataTable.createdAt]
          )
        }
    }

  suspend fun countTrackingData(userId: String, since: Instant) =
    dbQuery {
      TrackingDataTable.select {
        (TrackingDataTable.userId eq userId) and (TrackingDataTable.createdAt greater since)
      }.count()
    }

  suspend fun countTrackingData(userId: String) =
    dbQuery {
      TrackingDataTable.select {
        TrackingDataTable.userId eq userId
      }.count()
    }

  suspend fun obfuscateUserTrackingData(userId: String): Int = dbQuery {
    TrackingDataTable.update({ TrackingDataTable.userId eq userId }) {
      it[trackingId] = "personals deleted"
    }
  }

  suspend fun blacklistTrackingData(trackingDataSet: Set<TrackingData>, reason: String) =
    dbQuery {
      TrackingDataBlacklistTable.batchInsert(trackingDataSet, ignore = true) { trackingData ->
        this[TrackingDataBlacklistTable.trackingId] = trackingData.id
        this[TrackingDataBlacklistTable.trackingType] = trackingData.type.name
        this[TrackingDataBlacklistTable.appPlatform] = trackingData.platform.name
        this[TrackingDataBlacklistTable.reason] = reason
      }
    }

  suspend fun isTrackingDataBlacklisted(trackingData: TrackingData) =
    dbQuery {
      TrackingDataBlacklistTable.select {
        (TrackingDataBlacklistTable.trackingId eq trackingData.id) and
          (TrackingDataBlacklistTable.trackingType eq trackingData.type.name) and
          (TrackingDataBlacklistTable.appPlatform eq trackingData.platform.name)
      }
        .count() > 0
    }

  suspend fun removeTrackingDataFromBlacklist(trackingData: TrackingData) =
    dbQuery {
      TrackingDataBlacklistTable.deleteWhere {
        (TrackingDataBlacklistTable.trackingId eq trackingData.id) and
          (TrackingDataBlacklistTable.trackingType eq trackingData.type.name) and
          (TrackingDataBlacklistTable.appPlatform eq trackingData.platform.name)
      }
    }

  suspend fun getLatestTrackingData(userId: String, trackingType: String, appPlatform: String) =
    dbQuery {
      TrackingDataTable.select {
        (TrackingDataTable.userId eq userId) and
          (TrackingDataTable.trackingType eq trackingType) and
          (TrackingDataTable.appPlatform eq appPlatform)
      }.orderBy(TrackingDataTable.createdAt, SortOrder.DESC)
        .limit(1)
        .firstOrNull()?.toTrackingData()
    }

}

private fun ResultRow.toTrackingData(): TrackingData {
  return TrackingData.fromValues(
    this[TrackingDataTable.trackingId],
    this[TrackingDataTable.trackingType],
    this[TrackingDataTable.appPlatform],
  )
}