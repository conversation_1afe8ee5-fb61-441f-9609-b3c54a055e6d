package com.moregames.playtime.user.gamerank

import com.google.inject.AbstractModule
import com.google.inject.multibindings.MapBinder
import com.moregames.playtime.user.gamerank.calculators.GameRankProgressCalculator

class UserGameRankModule : AbstractModule() {
  override fun configure() {
    val mapBinder = MapBinder.newMapBinder(binder(), GameRankCalculator::class.java, GameRankProgressCalculator::class.java)
    GameRankCalculator.entries.forEach {
      mapBinder.addBinding(it).to(it.calculatorClass.java)
    }
  }
}