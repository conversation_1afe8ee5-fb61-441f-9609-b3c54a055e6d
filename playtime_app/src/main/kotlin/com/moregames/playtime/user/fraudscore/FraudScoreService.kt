package com.moregames.playtime.user.fraudscore

import com.google.inject.Inject
import com.google.inject.Provider
import com.moregames.base.app.BuildVariant
import com.moregames.base.app.BuildVariant.TEST
import com.moregames.base.app.PaymentProviderType
import com.moregames.base.bigquery.BigQueryEventPublisher
import com.moregames.base.bus.MessageBus
import com.moregames.base.config.ApplicationConfig
import com.moregames.base.dto.AppPlatform
import com.moregames.base.dto.AppPlatform.*
import com.moregames.base.dto.AppVersionDto
import com.moregames.base.exceptions.UserRecordNotFoundException
import com.moregames.base.messaging.dto.UserBannedEventDto
import com.moregames.base.messaging.dto.UserBlockedBqEventDto
import com.moregames.base.messaging.dto.UserRestrictedBqEventDto
import com.moregames.base.messaging.dto.UserUnBannedEventDto
import com.moregames.base.user.dto.CreateUserData
import com.moregames.base.user.dto.CreateUserRequestDto
import com.moregames.base.user.dto.SimInfo
import com.moregames.base.user.dto.UserRequestMetadata
import com.moregames.base.util.ClientVersionsSupport.getDesiredAppVersion
import com.moregames.base.util.IoCoroutineScope
import com.moregames.base.util.TimeService
import com.moregames.base.util.logger
import com.moregames.playtime.administration.blacklist.BlacklistPersistenceService
import com.moregames.playtime.administration.qa.QaUserSettingsService
import com.moregames.playtime.buseffects.SendUserCashoutRestrictedEffect
import com.moregames.playtime.checks.IpQualityScoreServiceClient
import com.moregames.playtime.clientapp.ClientAppService
import com.moregames.playtime.general.MarketService
import com.moregames.playtime.general.TrackedEventsPersistenceService
import com.moregames.playtime.general.dto.TrackedEvent.Companion.buildTrackedEventsForAllPlatforms
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.tracking.AdMarketService
import com.moregames.playtime.user.BanReason
import com.moregames.playtime.user.UserPersistenceService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.CashoutPersistenceService
import com.moregames.playtime.user.fraudscore.FraudScoreChangeReason.*
import com.moregames.playtime.user.fraudscore.FraudScorePersistenceService.FraudScoreTransaction
import com.moregames.playtime.user.fraudscore.dto.FsTransactionChangedDto
import com.moregames.playtime.user.fraudscore.dto.FsTransactionsRemovedByReasonDto
import com.moregames.playtime.user.tracking.TrackingService
import com.moregames.playtime.webhook.adjust.dto.AdjustInstallation
import kotlinx.coroutines.launch
import java.time.Duration
import java.time.Instant
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import javax.inject.Singleton
import kotlin.math.max
import kotlin.math.min

@Singleton
class FraudScoreService @Inject constructor(
  private val blacklistPersistenceService: BlacklistPersistenceService,
  private val userPersistenceService: UserPersistenceService,
  private val rewardingFacade: RewardingFacade,
  private val userService: UserService,
  private val cashoutPersistenceService: CashoutPersistenceService,
  private val fraudScorePersistenceService: FraudScorePersistenceService,
  private val marketService: MarketService,
  private val timeService: TimeService,
  private val clientAppService: ClientAppService,
  private val bigQueryEventPublisher: BigQueryEventPublisher,
  private val trackingService: TrackingService,
  private val qaUserSettingsService: QaUserSettingsService,
  private val buildVariant: BuildVariant,
  private val ipqsServiceClient: IpQualityScoreServiceClient,
  private val applicationConfig: ApplicationConfig,
  private val messageBus: MessageBus,
  private val coroutineScope: Provider<IoCoroutineScope>,
  private val adMarketService: AdMarketService,
  private val trackedEventsPersistenceService: TrackedEventsPersistenceService,
  private val userAttestationService: UserAttestationService,
) {
  // Note: this list has to be synchronized with a list on forwarding server
  companion object {
    val HIGH_ECPM_COUNTRIES = setOf(
      "FR", "US", "DE", "CA", "AU", "GB", "UK", "IT", "ES", "PT", "NL", "BE", "AT", "IE", "CH", "NZ", "SE", "FI", "DK",
      "NO", "JP", "CN", "HK", "TW", "KR", "SG", "MO", "AE", "QA", "KW", "IL", "PL"
    )

    const val UNKNOWN_COUNTRY = "ZZ"

    const val FRAUD_SCORE_BAN_THRESHOLD = 190
    const val FRAUD_SCORE_BLOCK_THRESHOLD = 2000.0

    val US_ONLY_PROVIDERS = setOf(PaymentProviderType.WALMART, PaymentProviderType.TARGET, PaymentProviderType.BEST_BUY)

    const val GAID_CHANGE_RATE_CHECK_PERIOD_DAYS = 30L
    const val GAID_CHANGE_MAX_COUNT = 2
    const val DAYS_TO_FS_FREEZE = 5L
    const val OLD_APP_VERSION_TOLERANCE_DAYS = 120
    const val MAX_EMAIL_USAGE_COUNT = 3

    val SUSPICIOUS_APPLOVIN_REVENUE_FREQUENCY_CONDITIONS = SuspiciousApplovinRevenueFrequencyConditions(
      nonBannerTransactionsPerHourAmount = 5,
      hoursWithGreaterAmount = 21,
      daysToCheck = 14,
      minDaysToProveFraud = 2
    )

    val iosPlatforms = setOf(IOS, IOS_WEB)
  }

  suspend fun getFraudScore(userId: String): Double =
    fraudScorePersistenceService.getFrozenFraudScore(userId)
      ?: if (userPersistenceService.isUserWhitelisted(userId)) 0.0
      else fraudScorePersistenceService.getUserFraudScore(userId)


  // TODO: extract to separate service (to simplify unit testing)
  suspend fun banUserWithHighFraudScore(userId: String, bypassFrozenScore: Boolean = false, blockUser: Boolean = true): Boolean {
    if (buildVariant == TEST && qaUserSettingsService.shouldRestrictBan(userId)) {
      logger().info("Avoid banning user $userId on test environment")
      return false
    }
    if (fraudScorePersistenceService.getFrozenFraudScore(userId) != null && !bypassFrozenScore) {
      return false
    }
    val fraudScore = getFraudScore(userId)
    if (fraudScore > FRAUD_SCORE_BAN_THRESHOLD) {
      val isUserBannedBefore = userService.getUser(userId).isBanned
      logger().info("Banning user $userId because of high fraud score $fraudScore")
      userPersistenceService.banUser(userId, BanReason.HIGH_FRAUD_SCORE, "Fraud score $fraudScore is higher than $FRAUD_SCORE_BAN_THRESHOLD")
      if (fraudScore >= FRAUD_SCORE_BLOCK_THRESHOLD && blockUser) {
        blockUser(userId)
      }
      val isUserBanned = userService.getUser(userId).isBanned
      if (!isUserBannedBefore && isUserBanned) {
        bigQueryEventPublisher.publish(
          UserBannedEventDto(
            userId = userId,
            market = applicationConfig.justplayMarket,
            createdAt = timeService.now(),
            BanReason.HIGH_FRAUD_SCORE.toString()
          )
        )
        coroutineScope.get().launch {
          trackedEventsPersistenceService.addTrackedEvents(buildTrackedEventsForAllPlatforms(userId = userId, eventName = "user_banned"))
          adMarketService.sendMarketEvents(userId, listOf("user_banned"))
        }
      }
      return isUserBanned
    }
    return false
  }

  suspend fun unbanUserWithLowFraudScore(userId: String) {
    if (fraudScorePersistenceService.getFrozenFraudScore(userId) != null) {
      return
    }
    if (userService.getUser(userId).isBanned) {
      val fraudScore = getFraudScore(userId)
      if (fraudScore <= FRAUD_SCORE_BAN_THRESHOLD) {
        logger().info("Unbanning user $userId because of too low fraud score $fraudScore")
        userPersistenceService.unbanUser(userId, "Fraud score $fraudScore is lower than $FRAUD_SCORE_BAN_THRESHOLD")
        bigQueryEventPublisher.publish(
          UserUnBannedEventDto(
            userId = userId,
            market = applicationConfig.justplayMarket,
            createdAt = timeService.now()
          )
        )
      }
    }
  }

  suspend fun onNewGoogleAdId(userId: String, googleAdId: String, appVersion: Int?) {
    val recentGoogleAdIdChanges = trackingService.countGoogleAdIdTracked(
      userId, timeService.now().minus(GAID_CHANGE_RATE_CHECK_PERIOD_DAYS, ChronoUnit.DAYS)
    ) - 1
    if (fraudScorePersistenceService.getFrozenFraudScore(userId) != null) {
      if (recentGoogleAdIdChanges > 7) {
        addOrUpdateTransaction(
          FraudScoreTransaction(
            userId = userId,
            amount = (recentGoogleAdIdChanges - 7) * 200.0,
            reasonType = USER_GOOGLE_AD_ID_CHANGE_RATE_IS_HIGH_FFS,
            reasonUniqueId = userId,
            description = "User: $userId gaid change rate is $recentGoogleAdIdChanges during last $GAID_CHANGE_RATE_CHECK_PERIOD_DAYS days"
          )
        )
        fraudScorePersistenceService.freezeFraudScore(userId)
        banUserWithHighFraudScore(userId, bypassFrozenScore = true)
      }
      return
    }
    if (trackingService.isGoogleAdIdBlacklisted(googleAdId)) {
      addOrUpdateTransaction(
        FraudScoreTransaction(
          userId = userId,
          amount = FRAUD_SCORE_BLOCK_THRESHOLD,
          reasonType = USER_GOOGLE_AD_ID_IS_BLACKLISTED,
          reasonUniqueId = googleAdId,
          description = "User: $userId Google Ad id $googleAdId is blacklisted"
        )
      )
      blockUser(userId)
    }
    if (recentGoogleAdIdChanges >= GAID_CHANGE_MAX_COUNT) {
      addOrUpdateTransaction(
        FraudScoreTransaction(
          userId = userId,
          amount = FRAUD_SCORE_BAN_THRESHOLD.toDouble() + max(recentGoogleAdIdChanges - 3, 0) * 150.0,
          reasonType = USER_GOOGLE_AD_ID_CHANGE_RATE_IS_HIGH,
          reasonUniqueId = userId,
          description = "User: $userId gaid change rate is $recentGoogleAdIdChanges during last $GAID_CHANGE_RATE_CHECK_PERIOD_DAYS days"
        )
      )
    } else if (recentGoogleAdIdChanges == 1L) {
      addOrUpdateTransaction(
        FraudScoreTransaction(
          userId = userId,
          amount = 60.0,
          reasonType = USER_GOOGLE_AD_ID_CHANGE_RATE_IS_HIGH,
          reasonUniqueId = userId,
          description = "User: $userId gaid change rate is $recentGoogleAdIdChanges during last $GAID_CHANGE_RATE_CHECK_PERIOD_DAYS days"
        )
      )
    }
    val additionalUsersOnGoogleAdId = userPersistenceService.countUsersWithSameGoogleAdId(userId, googleAdId)
    if (additionalUsersOnGoogleAdId > 0) {
      addOrUpdateTransaction(
        FraudScoreTransaction(
          userId = userId,
          amount = additionalUsersOnGoogleAdId * 3.0,
          reasonType = USER_SHARE_GOOGLE_AD_ID_WITH_OTHER_USERS,
          reasonUniqueId = googleAdId,
          description = "User: $userId use new GoogleAdId: $googleAdId"
        )
      )
    } else if (appVersion != null && recentGoogleAdIdChanges <= 0L
      && trackingService.countGoogleAdIdTracked(userId) == 1L
    ) {

      clientAppService.getAppVersionsFullRolloutDates()
        .firstOrNull { it.appVersion > appVersion }
        ?.also {
          val daysPassed = Duration.between(it.fullRolloutDate.atStartOfDay(), LocalDate.now().atStartOfDay()).toDays()
          val fs = calculateFsForCreatingUsersFromOldAppVersion(daysPassed)
          if (fs > 0.0) {
            addOrUpdateTransaction(
              FraudScoreTransaction(
                userId = userId,
                amount = fs,
                reasonType = USER_CREATED_FROM_OLD_VERSION_APP,
                reasonUniqueId = userId,
                description = "User: $userId was created using $appVersion app version"
              )
            )
          }
        }
    }
    banUserWithHighFraudScore(userId)
    // We don't need to change score for other users on this googleAdId, since they are deleted
  }

  suspend fun onNewIp(userId: String, ip: String, countryCode: String?, userOtherCountries: Set<String>) {
    val userHasFrozenFS = fraudScorePersistenceService.getFrozenFraudScore(userId) != null
    if (!userHasFrozenFS && blacklistPersistenceService.isIpBlacklisted(ip)) {
      addOrUpdateTransaction(
        FraudScoreTransaction(
          userId = userId,
          amount = 40.0,
          reasonType = USER_IP_IS_BLACKILISTED,
          reasonUniqueId = ip,
          description = "User: $userId uses blacklisted ip: $ip"
        )
      )
    }

    val associatedUsers = userPersistenceService.loadRecentUsersAssociatedByIp(userId, ip, marketService.getAllowedCountries())

    if (!userHasFrozenFS && associatedUsers.isEmpty()) {

      // -3 if IP is unique if user has no FFS
      addOrUpdateTransaction(
        FraudScoreTransaction(
          userId = userId,
          amount = -3.0,
          reasonType = USER_IP_IS_UNIQUE,
          reasonUniqueId = ip,
          description = "User: $userId use unique ip: $ip"
        )
      )
      unbanUserWithLowFraudScore(userId)
    } else {
      if (associatedUsers.size == 1) {
        // add +1 to associated user, since it doesn't have unique IP anymore if user has no FFS
        if (!associatedUsers.first().hasFrozenFraudScore) {
          addOrUpdateTransaction(
            FraudScoreTransaction(
              userId = associatedUsers.first().userId,
              amount = 1.0,
              reasonType = USER_IP_IS_NOT_UNIQUE,
              reasonUniqueId = ip,
              description = "User: ${associatedUsers.first().userId} ip: $ip is not unique anymore"
            )
          )
        }
      }

      if (userAttestationService.attestationPassedWell(userId)) {
        banUserWithHighFraudScore(userId)
        return // do not add FS for sharing IP with other users
      }

      when {
        userHasFrozenFS -> {
          //do nothing if user has FFS
        }

        associatedUsers.sumOf { if (it.allowedCountriesOnly) 1L else 4L } >= 200L -> {
          // +200 if count of associated users is too high
          addOrUpdateTransaction(
            FraudScoreTransaction(
              userId = userId,
              amount = 200.0,
              reasonType = USER_SHARE_IP_WITH_OTHER_USER,
              reasonUniqueId = "users",
              description = "User: $userId share ip: $ip with a lot of other users"
            )
          )
        }

        else -> {
          // +1 for each associated user
          addTransactions(
            associatedUsers.map { associatedUser ->
              FraudScoreTransaction(
                userId = userId,
                amount = 1.0,
                reasonType = USER_SHARE_IP_WITH_OTHER_USER,
                reasonUniqueId = associatedUser.userId,
                description = "User: $userId share ip: $ip with user: ${associatedUser.userId}"
              )
            }
          )
          // +3 for each associated user from non-supported countries
          associatedUsers.filterNot { it.allowedCountriesOnly }.takeIf { it.isNotEmpty() }?.let {
            addTransactions(
              it.map { associatedUser ->
                FraudScoreTransaction(
                  userId = userId,
                  amount = 3.0,
                  reasonType = USER_SHARE_IP_WITH_USER_FROM_NON_ALLOWED_COUNTRY,
                  reasonUniqueId = associatedUser.userId,
                  description = "User: $userId share ip: $ip with user: ${associatedUser.userId} from non-allowed countries"
                )
              }
            )
          }
          // +7 for each associated banned user
          associatedUsers.filter { it.isBanned }.takeIf { it.isNotEmpty() }?.let {
            addTransactions(
              it.map { associatedUser ->
                FraudScoreTransaction(
                  userId = userId,
                  amount = 7.0,
                  reasonType = USER_SHARE_IP_WITH_BANNED_USER,
                  reasonUniqueId = associatedUser.userId,
                  description = "User: $userId share ip: $ip with banned user: ${associatedUser.userId}"
                )
              }
            )
          }
        }
      }

      // +1 to each associated non-deleted, non-banned, non-with-FFS user
      associatedUsers.filterNot { it.isBanned || it.isDeleted || it.hasFrozenFraudScore }.takeIf { it.isNotEmpty() }?.let {
        addTransactions(
          it.map { associatedUser ->
            FraudScoreTransaction(
              userId = associatedUser.userId,
              amount = 1.0,
              reasonType = USER_SHARE_IP_WITH_OTHER_USER,
              reasonUniqueId = userId,
              description = "User: ${associatedUser.userId} share ip: $ip with user: $userId"
            )
          }
        )
      }
      val allowedCountries = getHighEcpmCountries(userId)
      if (countryCode != null
        && (countryCode !in allowedCountries
          || userOtherCountries.any { it !in allowedCountries })
      ) {
        // +3 to each associated non-deleted, non-banned, non-with-FFS user if current user connected from non-allowed country
        associatedUsers.filterNot { it.isBanned || it.isDeleted || it.allowedCountriesOnly || it.hasFrozenFraudScore }
          .takeIf { it.isNotEmpty() }?.let {
            addTransactions(
              it.map { associatedUser ->
                FraudScoreTransaction(
                  userId = associatedUser.userId,
                  amount = 3.0,
                  reasonType = USER_SHARE_IP_WITH_USER_FROM_NON_ALLOWED_COUNTRY,
                  reasonUniqueId = userId,
                  description = "User: ${associatedUser.userId} share ip: $ip with user: $userId from non-allowed countries"
                )
              }
            )
          }
      }
      if (banUserWithHighFraudScore(userId)) {
        // +7 to each associated non-deleted, non-banned, non-with-FFS user if the current user is banned
        associatedUsers.filterNot { it.isBanned || it.isDeleted || it.allowedCountriesOnly || it.hasFrozenFraudScore }
          .takeIf { it.isNotEmpty() }?.let {
            addTransactions(
              it.map { associatedUser ->
                FraudScoreTransaction(
                  userId = associatedUser.userId,
                  amount = 7.0,
                  reasonType = USER_SHARE_IP_WITH_BANNED_USER,
                  reasonUniqueId = userId,
                  description = "User: ${associatedUser.userId} share ip: $ip with banned user: $userId"
                )
              }
            )
          }
      }
    }
  }

  suspend fun onNewCountry(userId: String, countryCode: String, userOtherCountries: Set<String>) {
    if (fraudScorePersistenceService.getFrozenFraudScore(userId) != null) {
      return
    }
    if (countryCode in marketService.getAllowedCountries() && userOtherCountries.isEmpty()) {
      return
    }

    if (countryCode == UNKNOWN_COUNTRY) {
      // + 15 if country code is not defined
      addOrUpdateTransaction(
        FraudScoreTransaction(
          userId = userId,
          amount = 15.0,
          reasonType = USER_IS_CONNECTED_FROM_UNKNOWN_COUNTRY,
          reasonUniqueId = countryCode,
          description = "User: $userId is connected from unknown country: $countryCode"
        )
      )
    } else if (countryCode !in getHighEcpmCountries(userId)) {
      // +FRAUD_SCORE_BLOCK_THRESHOLD to when user is connected from non-allowed country
      addOrUpdateTransaction(
        FraudScoreTransaction(
          userId = userId,
          amount = FRAUD_SCORE_BLOCK_THRESHOLD,
          reasonType = USER_IS_CONNECTED_FROM_NON_ALLOWED_COUNTRY,
          reasonUniqueId = countryCode,
          description = "User: $userId is connected from non-supported country: $countryCode"
        )
      )
      blockUser(userId)
    } else {
      // + 20 if country is not unique
      if (userOtherCountries.isNotEmpty()) {
        addOrUpdateTransaction(
          FraudScoreTransaction(
            userId = userId,
            amount = 20.0,
            reasonType = USER_IS_CONNECTED_FROM_ADDITIONAL_COUNTRY,
            reasonUniqueId = countryCode,
            description = "User: $userId connected from additional country: $countryCode"
          )
        )
      }
    }
    banUserWithHighFraudScore(userId)
  }

  suspend fun onAdjustDataReceived(adjustInstallation: AdjustInstallation) = with(adjustInstallation) {
    if (fraudScorePersistenceService.getFrozenFraudScore(userId) != null) return

    if (!isOrganic()) {
      addOrUpdateTransaction(
        FraudScoreTransaction(
          userId = userId,
          amount = -20.0,
          reasonType = USER_IS_ACQUIRED_FROM_AD_PLATFORM,
          reasonUniqueId = googleAdId ?: idfv ?: userId,
          description = "User: $userId is acquired from Advertising platform"
        )
      )
      removeAllFsForInstallNotFromStore(userId)
      unbanUserWithLowFraudScore(userId)
    }
  }

  suspend fun onNewUserEmail(userId: String, emailHash: String, normalizedEmailHash: String) {
    if (blacklistPersistenceService.isEmailBlacklisted(emailHash)) {
      blockUser(userId)
      if (fraudScorePersistenceService.getFrozenFraudScore(userId) == null) {
        addOrUpdateTransaction(
          FraudScoreTransaction(
            userId = userId,
            amount = FRAUD_SCORE_BLOCK_THRESHOLD,
            reasonType = USER_EMAIL_IS_BLACKLISTED,
            reasonUniqueId = emailHash.take(50),
            description = "User: $userId email hash $emailHash is blacklisted"
          )
        )
      }
    }

    val associatedUserIds = cashoutPersistenceService.getRecentUserIdsWithSameEmail(userId, emailHash)
    if (associatedUserIds.isEmpty()) return
    userPersistenceService.markUsersAsSharedEmail(listOf(userId) + associatedUserIds)
    val usersWithUsualFS = fraudScorePersistenceService.removeUsersWithFrozenFS(listOf(userId).plus(associatedUserIds))
    usersWithUsualFS.forEach { associatedUserId ->
      addOrUpdateTransaction(
        FraudScoreTransaction(
          userId = associatedUserId,
          amount = 15.0 * (associatedUserIds.size),
          reasonType = USER_SHARE_EMAIL_WITH_OTHER_USERS,
          reasonUniqueId = emailHash.take(50),
          description = "User: $associatedUserId is share email hash: $emailHash with other users"
        )
      )
    }

    banUserWithHighFraudScore(userId)
  }

  suspend fun onUserFaceVerified(userId: String) {
    if (fraudScorePersistenceService.getFrozenFraudScore(userId) != null) {
      return
    }
    addOrUpdateTransaction(
      FraudScoreTransaction(
        userId = userId,
        amount = -80.0,
        reasonType = USER_FACE_VERIFIED,
        reasonUniqueId = userId,
        description = "User: $userId passed face verification"
      )
    )
    unbanUserWithLowFraudScore(userId)
  }

  suspend fun onSuccessfulCashout(userId: String, provider: PaymentProviderType) {
    if (fraudScorePersistenceService.getFrozenFraudScore(userId) != null) {
      return
    }
    addProviderDependentTransaction(userId, provider)
    if (userPersistenceService.loadUserCreationDate(userId).isAfter(timeService.now().minus(7, ChronoUnit.DAYS))) return

    val latestFsTransaction = fraudScorePersistenceService.getLatestTransactionDate(userId, USER_CASHED_OUT_SUCCESSFULLY)
    if (latestFsTransaction == null || latestFsTransaction.isBefore(timeService.now().minus(7, ChronoUnit.DAYS))) {
      addOrUpdateTransaction(
        FraudScoreTransaction(
          userId = userId,
          amount = -40.0,
          reasonType = USER_CASHED_OUT_SUCCESSFULLY,
          reasonUniqueId = timeService.now().truncatedTo(ChronoUnit.DAYS).toString(),
          description = "User: $userId cashed out successfully"
        )
      )
      unbanUserWithLowFraudScore(userId)
    }
  }

  suspend fun onAppVersionChange(userId: String, appVersion: AppVersionDto, oldAppVersion: Int?) {
    if (fraudScorePersistenceService.getFrozenFraudScore(userId) != null) {
      return
    }
    if (oldAppVersion != null && (oldAppVersion - appVersion.version) > 1) { // more than one version step downgrade
      addOrUpdateTransaction(
        FraudScoreTransaction(
          userId = userId,
          amount = 40.0,
          reasonType = USER_DOWNGRADED_APP_VERSION,
          reasonUniqueId = "$oldAppVersion -> ${appVersion.version}",
          description = "Attempt to downgrade app version from $oldAppVersion to ${appVersion.version}"
        )
      )
    }
    addOrUpdateTransaction(
      FraudScoreTransaction(
        userId = userId,
        amount = if (appVersion.version >= getDesiredAppVersion(appVersion.platform)) 0.0 else 600.0,
        reasonType = USER_USED_NON_DESIRED_VERSION,
        reasonUniqueId = userId,
        description = "User app version '${appVersion.version}'. Desired app version '${getDesiredAppVersion(appVersion.platform)}'"
      )
    )
    banUserWithHighFraudScore(userId)
    unbanUserWithLowFraudScore(userId)
  }

  private suspend fun addProviderDependentTransaction(userId: String, provider: PaymentProviderType) {
    val amount = when {
      provider.donation -> -80.0
      provider in US_ONLY_PROVIDERS -> -50.0
      provider == PaymentProviderType.AMAZON -> -3.0
      else -> 0.0
    }
    if (amount < 0.0) {
      addOrUpdateTransaction(
        FraudScoreTransaction(
          userId = userId,
          amount = amount,
          reasonType = USER_CASHED_OUT_SUCCESSFULLY_ADDITIONAL,
          reasonUniqueId = timeService.now().toString(),
          description = "User: $userId cashed out successfully, provider: ${provider.key}"
        )
      )
      unbanUserWithLowFraudScore(userId)
    }
  }

  suspend fun onNewUserCreated(userId: String, createUserData: CreateUserData) {
    val request = with(createUserData.userRequestDto!!) {
      CreateUserRequestDto(
        simCountry = simCountry?.uppercase(),
        simOperatorName = simOperatorName?.lowercase(),
        networkCountry = networkCountry?.uppercase(),
        networkOperatorName = networkOperatorName?.lowercase(),
        deviceLocale = deviceLocale?.uppercase(),
        deviceLanguageTag = deviceLanguageTag,
        installedFromStore = installedFromStore,
        jailBreak = jailBreak,
      )
    }
    when (createUserData.appVersion.platform) {
      ANDROID -> simAndNetworkCheck(userId, request)
      IOS, IOS_WEB -> {
        iosJailBreakCheck(userId, request)
        iosTimeZoneCheck(userId, createUserData.userRequestDto?.timeZone)
        iosDeviceLocaleCheck(userId, createUserData.userRequestDto?.deviceLocale)
      }
    }

    //all platforms checks.
    // check installedFromStore flag
    if (request.installedFromStore == false) {
      addOrUpdateTransaction(
        FraudScoreTransaction(
          userId = userId,
          amount = 100.0,
          reasonType = USER_DID_NOT_USE_STORE_TO_INSTALL,
          reasonUniqueId = userId,
          description = "User: $userId installed app not from the Play Store"
        )
      )
    }
    banUserWithHighFraudScore(userId)
  }

  suspend fun onUsingVPN(userId: String) {
    if (fraudScorePersistenceService.getFrozenFraudScore(userId) != null) {
      return
    }
    addOrUpdateTransaction(
      FraudScoreTransaction(
        userId = userId,
        amount = 40.0,
        reasonType = USER_USE_VPN,
        reasonUniqueId = userId,
        description = "User: $userId uses VPN"
      )
    )
    banUserWithHighFraudScore(userId)
  }

  suspend fun onUserFaceUniquenessCheckFailed(userId: String) {
    if (fraudScorePersistenceService.getFrozenFraudScore(userId) != null) {
      return
    }
    addOrUpdateTransaction(
      FraudScoreTransaction(
        userId = userId,
        amount = 120.0,
        reasonType = USER_FACE_UNIQUENESS_CHECK_FAILED,
        reasonUniqueId = userId,
        description = "User: $userId fail face uniqueness check"
      )
    )
    banUserWithHighFraudScore(userId)
  }

  suspend fun onLocationCheck(userId: String, checkPassed: Boolean) {
    if (!checkPassed) {
      blockUserByReason(userId, USER_IS_CONNECTED_FROM_NON_ALLOWED_COUNTRY, "User: $userId connected from non allowed country")
    } else if (userService.getUser(userId).appPlatform in iosPlatforms) {
      addOrUpdateTransaction(
        FraudScoreTransaction(
          userId = userId,
          amount = -100.0,
          reasonType = USER_IS_CONNECTED_FROM_ALLOWED_COUNTRY,
          reasonUniqueId = userId,
          description = "User: $userId is connected from allowed country"
        )
      )
    }
  }

  suspend fun blockUserOnMockedLocation(userId: String) {
    blockUserByReason(userId, USER_GPS_LOCATION_IS_MOCKED, "User: $userId is mocking GPS location")
  }

  suspend fun forceBanUser(userId: String) {
    if (!userService.userExists(userId)) throw UserRecordNotFoundException(userId)

    val userFraudScoreBeforeBan = fraudScorePersistenceService.getUserFraudScore(userId)
    val amount = if (userFraudScoreBeforeBan <= FRAUD_SCORE_BAN_THRESHOLD) FRAUD_SCORE_BAN_THRESHOLD - userFraudScoreBeforeBan + 1.0 else 1.0
    addOrUpdateTransaction(
      FraudScoreTransaction(
        userId = userId,
        amount = amount,
        reasonType = MANUAL_FS,
        reasonUniqueId = userId,
        description = "User: $userId is force banned"
      )
    )
    if (fraudScorePersistenceService.getFrozenFraudScore(userId) != null) {
      fraudScorePersistenceService.freezeFraudScore(userId)
      val frozenFS = fraudScorePersistenceService.getFrozenFraudScore(userId) ?: 0.0
      if (frozenFS > FRAUD_SCORE_BAN_THRESHOLD) {
        logger().info("Banning user $userId because of high fraud score $frozenFS")
        userPersistenceService.banUser(userId, BanReason.HIGH_FRAUD_SCORE, "Fraud score $frozenFS is higher than $FRAUD_SCORE_BAN_THRESHOLD")
        bigQueryEventPublisher.publish(
          UserBannedEventDto(
            userId = userId,
            market = applicationConfig.justplayMarket,
            createdAt = timeService.now(),
            BanReason.HIGH_FRAUD_SCORE.toString()
          )
        )
      }
    } else {
      banUserWithHighFraudScore(userId)
    }
  }

  suspend fun forceBlockUser(userId: String) {
    if (!userService.userExists(userId)) throw UserRecordNotFoundException(userId)
    if (fraudScorePersistenceService.isUserBlocked(userId)) throw IllegalStateException("User $userId is already blocked")

    blockUserByReason(userId, MANUAL_ADMIN, "User: $userId is blocked manually via admin endpoint")
  }

  suspend fun freezeFraudScore(userId: String, hasSuccessfulCashout: Boolean = false) {
    val tenDaysAgo = timeService.now().minus(DAYS_TO_FS_FREEZE, ChronoUnit.DAYS)
    val user = userService.getUser(userId)
    if (user.createdAt.isBefore(tenDaysAgo) &&
      !user.isWhitelisted &&
      fraudScorePersistenceService.getFrozenFraudScore(userId) == null &&
      !user.isBanned &&
      (hasSuccessfulCashout || cashoutPersistenceService.userHasSuccessfulCashout(userId)) &&
      !banUserWithHighFraudScore(userId)
    ) {
      fraudScorePersistenceService.freezeFraudScore(userId)
    }
  }

  suspend fun recalculateFraudScoreTotals(userId: String) {
    if (fraudScorePersistenceService.getFrozenFraudScore(userId) == null) {
      fraudScorePersistenceService.recalculateTotals(userId)
    }
  }

  suspend fun isUserBlocked(userId: String): Boolean =
    fraudScorePersistenceService.isUserBlocked(userId) && !userPersistenceService.isUserWhitelisted(userId)

  suspend fun blockUserOnDeviceExaminationFail(userId: String) =
    blockUserByReason(userId, USER_DEVICE_EXAMINATION_FAILED, "User: $userId device examination failed")

  suspend fun blockUserOnTooManyDeviceExaminationAttempts(userId: String) =
    blockUserByReason(userId, USER_DEVICE_EXAMINATION_TOO_MANY_ATTEMPTS, "User: $userId had too many examination attempts")

  suspend fun blockUserOnJailBreakUsageDetected(userId: String) =
    blockUserByReason(userId, JAIL_BREAK_DETECTED, "User: $userId jailBreak usage detected")

  suspend fun blockUserOnPlatformChangeDetected(userId: String) =
    blockUserByReason(userId, PLATFORM_CHANGE_DETECTED, "User: $userId platform change detected")

  suspend fun obfuscateFraudScorePersonals(userId: String): Int =
    fraudScorePersistenceService.obfuscateFraudScorePersonals(userId)

  suspend fun checkCountriesAndRemoveFS(userId: String) {
    if (userAttestationService.attestationPassedWell(userId)) {
      removeAllFsForSharingIpsAndDeviceLocale(userId)
      unbanUserWithLowFraudScore(userId)
    }
  }

  suspend fun checkUserApplovinRevenueFrequencyAndBlockFraudsters(userId: String) = with(SUSPICIOUS_APPLOVIN_REVENUE_FREQUENCY_CONDITIONS) {
    val checkSinceDateTime = timeService.now().minus(daysToCheck, ChronoUnit.DAYS)
    val isFraudster = rewardingFacade.getUserNonBannerApplovinRevenueTransactionsCountByHours(userId, checkSinceDateTime)
      .groupBy { it.periodStart.truncatedTo(ChronoUnit.DAYS) }
      .mapValues { (_, listOfTransactionsCountPerHour) ->
        listOfTransactionsCountPerHour
          .count { it.transactionsCount > nonBannerTransactionsPerHourAmount }
      }.values.count { it >= hoursWithGreaterAmount } >= minDaysToProveFraud
    if (isFraudster) {
      blockUserByReason(
        userId = userId,
        reason = USER_APPLOVIN_REVENUE_FREQUENCY_WAS_SUSPICIOUS,
        description = "User: $userId had too frequent non-banner applovin revenue transactions"
      )
    }
  }

  suspend fun blockUserOnGameAutomation(userId: String) {
    val checkSinceDateTime = timeService.now().minus(9, ChronoUnit.HOURS)
    val max5MinIntervalsWithRevenueForGame = 60L
    val isFraudster = rewardingFacade.get5MinIntervalsWithRevenueByGames(userId, checkSinceDateTime)
      .any { (_, periods) ->
        var previousPeriod: Instant? = null
        var counter = 0
        val numberOfConsecutiveIntervals = periods.sorted()
          .maxOfOrNull { currentPeriod ->
            if (previousPeriod == null || Duration.between(previousPeriod, currentPeriod).toMinutes() <= 5) {
              previousPeriod = currentPeriod
              counter++
            } else {
              previousPeriod = currentPeriod
              counter = 0
              0
            }
          } ?: 0
        numberOfConsecutiveIntervals >= max5MinIntervalsWithRevenueForGame
      }
    if (isFraudster) {
      blockUserByReason(
        userId = userId,
        reason = USER_APPLOVIN_REVENUE_FREQUENCY_WAS_SUSPICIOUS,
        description = "User: $userId had too frequent game revenue"
      )
    }
  }

  suspend fun restrictUser(userId: String) {
    fraudScorePersistenceService.restrictUser(userId)
    bigQueryEventPublisher.publish(
      UserRestrictedBqEventDto(
        userId = userId,
        createdAt = timeService.now()
      )
    )
  }

  suspend fun isUserRestricted(userId: String): Boolean =
    fraudScorePersistenceService.isUserRestricted(userId)

  suspend fun isUserSharedEmail(userId: String): Boolean =
    userPersistenceService.isUserSharedEmail(userId)

  suspend fun checkEmailIsNotOverusedOrRestrict(userId: String, emailHash: String, appPlatform: AppPlatform) =
    cashoutPersistenceService
      .getRecentUserIdsWithSameEmail(userId, emailHash)
      .takeIf { it.count() > MAX_EMAIL_USAGE_COUNT }
      ?.let { otherUsersWithSameEmail ->
        val description = "Email hash '$emailHash' used too much by other users: $otherUsersWithSameEmail"

        addOrUpdateTransaction(
          FraudScoreTransaction(
            userId = userId,
            amount = 1.0, // just marking
            reasonType = USER_USES_OVERUSED_EMAIL,
            reasonUniqueId = emailHash.take(50),
            description = description
          )
        )

        restrictUserCashout(userId, appPlatform, description)
      }

  private fun calculateFsForCreatingUsersFromOldAppVersion(daysPassed: Long): Double = min(500.0, 75.0 * max(daysPassed - OLD_APP_VERSION_TOLERANCE_DAYS, 0))


  private suspend fun blockUserByReason(userId: String, reason: FraudScoreChangeReason, description: String) {
    addOrUpdateTransaction(
      FraudScoreTransaction(
        userId = userId,
        amount = FRAUD_SCORE_BLOCK_THRESHOLD,
        reasonType = reason,
        reasonUniqueId = userId,
        description = description
      )
    )
    blockUser(userId)
    banUserWithHighFraudScore(userId, blockUser = false)
  }

  private suspend fun blockUser(userId: String) {
    fraudScorePersistenceService.blockUser(userId)
    bigQueryEventPublisher.publish(
      UserBlockedBqEventDto(
        userId = userId,
        createdAt = timeService.now()
      )
    )
  }

  fun getTheBestSimByCountry(list: List<SimInfo>?): SimInfo? =
    list?.maxByOrNull { countryRating(it.networkCountry) + countryRating(it.simCountry) }

  private fun countryRating(country: String): Int {
    val allowedCountries = marketService.getAllowedCountries()
    return when {
      country.uppercase() in allowedCountries -> 250 //-80FS and possibly -150FS on top
      country.uppercase() in HIGH_ECPM_COUNTRIES -> 50 //+20FS
      country.isBlank() -> 25//+40FS
      else -> -1000//+2000FS
    }
  }

  private suspend fun simAndNetworkCheck(userId: String, request: CreateUserRequestDto) {
    val marketCountries = marketService.getAllowedCountries()
    // sim check
    if (request.simCountry.isNullOrBlank()) {
      addOrUpdateTransaction(
        FraudScoreTransaction(
          userId = userId,
          amount = 40.0,
          reasonType = USER_SIM_COUNTRY_UNKNOWN,
          reasonUniqueId = userId,
          description = "User: $userId sim country is unknown"
        )
      )
    } else if (request.simCountry in marketCountries) {
      addOrUpdateTransaction(
        FraudScoreTransaction(
          userId = userId,
          amount = -80.0,
          reasonType = USER_USE_SIM_FROM_LOW_FRAUD_COUNTRY,
          reasonUniqueId = userId,
          description = "User: $userId sim operator locates in a low fraud country '${request.simCountry}'"
        )
      )
    } else if (request.simCountry in HIGH_ECPM_COUNTRIES) {
      addOrUpdateTransaction(
        FraudScoreTransaction(
          userId = userId,
          amount = 20.0,
          reasonType = USER_USE_SIM_FROM_MID_FRAUD_COUNTRY,
          reasonUniqueId = request.simCountry.orEmpty(),
          description = "User: $userId sim country '${request.simCountry}' is not a low fraud country"
        )
      )
    } else {
      giveFsForSimFromNonAllowed(userId, request.simCountry, FRAUD_SCORE_BLOCK_THRESHOLD)
      blockUser(userId)
    }

    // network check
    if (request.networkCountry.isNullOrBlank()) {
      addOrUpdateTransaction(
        FraudScoreTransaction(
          userId = userId,
          amount = 40.0,
          reasonType = USER_NETWORK_COUNTRY_UNKNOWN,
          reasonUniqueId = userId,
          description = "User: $userId network country is unknown"
        )
      )
    } else if (request.networkCountry in marketCountries) {
      addOrUpdateTransaction(
        FraudScoreTransaction(
          userId = userId,
          amount = -80.0,
          reasonType = USER_NETWORK_FROM_LOW_FRAUD_COUNTRY,
          reasonUniqueId = userId,
          description = "User: $userId network locates in a low fraud country '${request.networkCountry}'"
        )
      )
    } else if (request.networkCountry in HIGH_ECPM_COUNTRIES) {
      addOrUpdateTransaction(
        FraudScoreTransaction(
          userId = userId,
          amount = 20.0,
          reasonType = USER_NETWORK_FROM_MID_FRAUD_COUNTRY,
          reasonUniqueId = request.networkCountry.orEmpty(),
          description = "User: $userId network country '${request.networkCountry}' is not a low fraud country"
        )
      )
    } else {
      addOrUpdateTransaction(
        FraudScoreTransaction(
          userId = userId,
          amount = FRAUD_SCORE_BLOCK_THRESHOLD,
          reasonType = USER_USE_NETWORK_FROM_NON_ALLOWED_COUNTRY,
          reasonUniqueId = request.networkCountry.orEmpty(),
          description = "User: $userId network country '${request.networkCountry}' is not allowed"
        )
      )
      blockUser(userId)
    }
    //combo check
    if (request.networkCountry in marketCountries && request.simCountry in marketCountries &&
      !request.simOperatorName.isNullOrBlank() &&
      !request.networkOperatorName.isNullOrBlank()
    ) {
      addOrUpdateTransaction(
        FraudScoreTransaction(
          userId = userId,
          amount = -150.0,
          reasonType = USER_USE_SIM_AND_NETWORK_FROM_LOW_FRAUD_COUNTRY,
          reasonUniqueId = userId,
          description = "User: $userId use sim (${request.simCountry}) and network (${request.networkCountry}) from a low fraud country"
        )
      )
    }
    // device locale check
    if (!marketService.deviceLocaleMatchesCountry(request.deviceLocale, request.networkCountry)) {
      addOrUpdateTransaction(
        FraudScoreTransaction(
          userId = userId,
          amount = 30.0,
          reasonType = USER_DEVICE_LOCALE_IS_NOT_ALLOWED,
          reasonUniqueId = request.deviceLocale ?: "emptyLocale",
          description = "User: $userId device locale '${request.deviceLocale}' is not en/es"
        )
      )
    }
  }

  private suspend fun iosJailBreakCheck(userId: String, request: CreateUserRequestDto) {
    if (request.jailBreak == true) {
      addOrUpdateTransaction(
        FraudScoreTransaction(
          userId = userId,
          amount = FRAUD_SCORE_BAN_THRESHOLD.toDouble(),
          reasonType = JAIL_BREAK_DETECTED,
          reasonUniqueId = userId,
          description = "User: $userId. JailBreak usage detected"
        )
      )
    }
  }

  //temporarily disabled https://app.asana.com/0/1155692811605665/1205615512712469/f
  private suspend fun vpnCheck(userId: String, userRequestMetadata: UserRequestMetadata) {
    try {
      val userIp = userRequestMetadata.forwardedIp ?: throw IllegalStateException("Can't determine user ip")
      ipqsServiceClient.checkIpQualityScore(userIp)?.vpn ?: false
    } catch (e: Exception) {
      logger().error("Error on vpn check: ${e.message}")
      false
    }.let { vpnUsed ->
      if (vpnUsed) {
        userPersistenceService.markUserAsConnectedViaVpn(userId)
        addOrUpdateTransaction(
          FraudScoreTransaction(
            userId = userId,
            amount = 40.0,
            reasonType = USER_USE_VPN,
            reasonUniqueId = userId,
            description = "User: $userId uses VPN"
          )
        )
      }
    }
  }

  private suspend fun iosTimeZoneCheck(userId: String, timeZone: String?) {
    if (marketService.isTimeZoneAllowed(timeZone)) {
      addOrUpdateTransaction(
        FraudScoreTransaction(
          userId = userId,
          amount = -20.0,
          reasonType = USER_USES_ALLOWED_TIMEZONE,
          reasonUniqueId = userId,
          description = "User: '$userId' uses allowed timeZone '$timeZone'"
        )
      )
    } else {
      addOrUpdateTransaction(
        FraudScoreTransaction(
          userId = userId,
          amount = 30.0,
          reasonType = USER_USES_DISALLOWED_TIMEZONE,
          reasonUniqueId = userId,
          description = "User: '$userId' uses disallowed timeZone '$timeZone'"
        )
      )
    }
  }

  private suspend fun iosDeviceLocaleCheck(userId: String, deviceLocale: String?) {
    if (marketService.isLocalePreferred(deviceLocale)) {
      addOrUpdateTransaction(
        FraudScoreTransaction(
          userId = userId,
          amount = -10.0,
          reasonType = USER_USES_PREFERRED_LOCALE,
          reasonUniqueId = "$userId, $deviceLocale",
          description = "User: '$userId' uses preferred locale '$deviceLocale'"
        )
      )
    }
  }

  private suspend fun restrictUserCashout(userId: String, appPlatform: AppPlatform, reason: String) {
    fraudScorePersistenceService.restrictUserCashout(userId, reason)
    messageBus.publishAsync(SendUserCashoutRestrictedEffect(userId, appPlatform, reason))
  }

  private suspend fun getHighEcpmCountries(userId: String) = if (marketService.isUsUser(userId)) {
    HIGH_ECPM_COUNTRIES + "PR"
  } else {
    HIGH_ECPM_COUNTRIES
  }

  private suspend fun giveFsForSimFromNonAllowed(userId: String, simCountry: String?, fsAmount: Double) {
    addOrUpdateTransaction(
      FraudScoreTransaction(
        userId = userId,
        amount = fsAmount,
        reasonType = USER_USE_SIM_FROM_NON_ALLOWED_COUNTRY,
        reasonUniqueId = simCountry.orEmpty(),
        description = "User: $userId sim country '$simCountry' is not allowed"
      )
    )
  }

  suspend fun addOrUpdateTransaction(transaction: FraudScoreTransaction) {
    fraudScorePersistenceService.addOrUpdateTransaction(transaction)

    coroutineScope.get().launch {
      FsTransactionChangedDto.from(transaction, timeService.now())
        .let { event -> bigQueryEventPublisher.publish(event) }
    }
  }

  suspend fun addTransactions(transactions: List<FraudScoreTransaction>) {
    fraudScorePersistenceService.addTransactions(transactions)

    coroutineScope.get().launch {
      transactions
        .map { FsTransactionChangedDto.from(it, timeService.now()) }
        .let { events -> bigQueryEventPublisher.publish(events) }
    }
  }

  suspend fun removeAllFsForSharingIpsAndDeviceLocale(userId: String) {
    val reasons = ipSharingReasonTypes() + USER_DEVICE_LOCALE_IS_NOT_ALLOWED

    fraudScorePersistenceService.removeAllFSTransactionsByReasonType(userId, reasons)

    coroutineScope.get().launch { sendFsRemovedEvents(userId, reasons) }
  }

  suspend fun removeAllFsForInstallNotFromStore(userId: String) {
    val reasons = listOf(USER_DID_NOT_USE_STORE_TO_INSTALL)

    fraudScorePersistenceService.removeAllFSTransactionsByReasonType(userId, reasons)
    coroutineScope.get().launch { sendFsRemovedEvents(userId, reasons) }
  }

  private suspend fun sendFsRemovedEvents(userId: String, reasons: List<FraudScoreChangeReason>) {
    val now = timeService.now()

    reasons
      .map { reason ->
        FsTransactionsRemovedByReasonDto(
          userId,
          reason,
          createdAt = now,
        )
      }
      .let { events -> bigQueryEventPublisher.publish(events) }
  }
}

data class SuspiciousApplovinRevenueFrequencyConditions(
  val nonBannerTransactionsPerHourAmount: Int,
  val hoursWithGreaterAmount: Int,
  val daysToCheck: Long,
  val minDaysToProveFraud: Int
)
