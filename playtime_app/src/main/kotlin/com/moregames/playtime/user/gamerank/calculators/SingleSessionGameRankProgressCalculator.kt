package com.moregames.playtime.user.gamerank.calculators

import com.moregames.base.messaging.dto.UserChallengeProgressDto
import com.moregames.base.messaging.dto.UserChallengeProgressDto.*
import com.moregames.base.util.logger
import com.moregames.playtime.user.challenge.progress.achievement.AchievementDto
import com.moregames.playtime.user.gamerank.GameRank
import com.moregames.playtime.user.gamerank.GameRankConfig
import kotlin.math.max
import kotlin.math.min

class SingleSessionGameRankProgressCalculator : GameRankProgressCalculator {
  override fun calculateProgress(
    progressDto: UserChallengeProgressDto,
    currentProgress: Int,
    currentAchievement: AchievementDto?,
    config: GameRankConfig
  ): GameRankProgress {
    logger().debug("Calculating progress {}", progressDto)
    val score = when (progressDto) {
      is ScoreCompletedProgressDto -> progressDto.score
      is ScoreProgressDto -> progressDto.score

      is AmountMilestoneProgressDto, is LevelIdProgressDto, is MilestoneProgressDto, is TmProgressDto -> {
        logger().warn("Score progress has wrong type")
        return GameRankProgress(config.getRank(currentProgress), currentProgress, currentAchievement)
      }
    }
    val realProgress = max(currentProgress, score)
    val cappedProgress = min(realProgress, config.progressMax)
    return GameRankProgress(config.getRank(cappedProgress), cappedProgress, currentAchievement)
  }

  private fun GameRankConfig.getRank(score: Int): GameRank {
    return when {
      score < oneStarProgressMax -> GameRank.ZERO
      score < twoStarProgressMax -> GameRank.ONE
      score < threeStarProgressMax -> GameRank.TWO
      else -> GameRank.THREE
    }
  }
}