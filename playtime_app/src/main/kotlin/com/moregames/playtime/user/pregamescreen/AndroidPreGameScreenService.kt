package com.moregames.playtime.user.pregamescreen

import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.DEFAULT
import com.moregames.base.abtesting.variations.AndroidPreGameScreenVariation
import com.moregames.base.util.ApplicationId.BALL_BOUNCE_APP_ID
import com.moregames.base.util.ApplicationId.BLOCKBUSTER_APP_ID
import com.moregames.base.util.ApplicationId.BLOCK_HOLE_CLASH_APP_ID
import com.moregames.base.util.ApplicationId.BLOCK_SLIDER_APP_ID
import com.moregames.base.util.ApplicationId.BRICK_DOKU_APP_ID
import com.moregames.base.util.ApplicationId.BUBBLE_POP_APP_ID
import com.moregames.base.util.ApplicationId.CARS_MERGE_APP_ID
import com.moregames.base.util.ApplicationId.COLOR_LOGIC_APP_ID
import com.moregames.base.util.ApplicationId.CRYSTAL_CRUSH_APP_ID
import com.moregames.base.util.ApplicationId.DICE_LOGIC_APP_ID
import com.moregames.base.util.ApplicationId.EMOJICLICKERS_APP_ID
import com.moregames.base.util.ApplicationId.FAIRY_TALE_MANSION_APP_ID
import com.moregames.base.util.ApplicationId.HEXA_PUZZLE_FUN_APP_ID
import com.moregames.base.util.ApplicationId.HEX_MATCH_APP_ID
import com.moregames.base.util.ApplicationId.IDLE_MERGE_FUN_APP_ID
import com.moregames.base.util.ApplicationId.MAD_SMASH_APP_ID
import com.moregames.base.util.ApplicationId.MARBLE_MADNESS_APP_ID
import com.moregames.base.util.ApplicationId.MERGE_BLAST_APP_ID
import com.moregames.base.util.ApplicationId.MIX_BLOX_APP_ID
import com.moregames.base.util.ApplicationId.PUZZLE_POP_BLASTER_APP_ID
import com.moregames.base.util.ApplicationId.SOLITAIRE_VERSE_APP_ID
import com.moregames.base.util.ApplicationId.SPACE_CONNECT_APP_ID
import com.moregames.base.util.ApplicationId.SPIRAL_DROP_APP_ID
import com.moregames.base.util.ApplicationId.SUDOKU_APP_ID
import com.moregames.base.util.ApplicationId.SUGAR_RUSH_APP_ID
import com.moregames.base.util.ApplicationId.TANGRAM_APP_ID
import com.moregames.base.util.ApplicationId.TILE_MATCH_PRO_APP_ID
import com.moregames.base.util.ApplicationId.TREASURE_MASTER_APP_ID
import com.moregames.base.util.ApplicationId.TRIVIA_MADNESS_APP_ID
import com.moregames.base.util.ApplicationId.WATER_SORTER_APP_ID
import com.moregames.base.util.ApplicationId.WOODEN_PUZZLE_APP_ID
import com.moregames.base.util.ApplicationId.WORD_KITCHEN_APP_ID
import com.moregames.base.util.ApplicationId.WORD_SEEKER_APP_ID
import com.moregames.base.util.TimeService
import com.moregames.playtime.app.ImageService
import com.moregames.playtime.user.cashout.CashoutPeriodsService
import com.moregames.playtime.user.offer.AndroidGameOffer
import com.moregames.playtime.user.timezone.UserTimeZoneService
import com.moregames.playtime.user.usergame.UserGamePersistenceService
import java.time.Duration
import java.time.temporal.ChronoUnit
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AndroidPreGameScreenService @Inject constructor(
  private val timeService: TimeService,
  private val userTimeZoneService: UserTimeZoneService,
  private val abTestingService: AbTestingService,
  private val cashoutPeriodsService: CashoutPeriodsService,
  private val userGamePersistenceService: UserGamePersistenceService,
  private val imageService: ImageService,
) {
  companion object {
    private const val DEFAULT_JUSTPLAY_SCREEN = "default_justplay_screen"
  }

  suspend fun applyExperiment(userId: String, games: List<AndroidGameOffer>): List<AndroidGameOffer> {
    val assignedVariation = abTestingService.assignedVariationValue(userId, ClientExperiment.ANDROID_PRE_GAME_SCREEN)
    if (assignedVariation == DEFAULT) {
      return games
    }

    val timerEnd = (abTestingService.assignedVariationValue(
      userId = userId,
      experiment = ClientExperiment.ANDROID_PRE_GAME_SCREEN
    ) as? AndroidPreGameScreenVariation)?.let {
      when (it) {
        AndroidPreGameScreenVariation.PreGameScreenIntroPermanent,
        AndroidPreGameScreenVariation.PreGameScreenIntroBeforeInstall -> {
          cashoutPeriodsService.getCurrentCashoutPeriod(userId).periodEnd.epochSecond
        }

        AndroidPreGameScreenVariation.PreGameScreenIntroPermanent3hTimer -> {
          calculate3HTimerEndInSeconds(userId)
        }
      }
    }

    return games.map { offer ->
      val screenOffer = screens[offer.applicationId] ?: screens[DEFAULT_JUSTPLAY_SCREEN]
      offer.copy(
        preGameScreenMode = screenOffer
          ?.copy(
            timerEnd = timerEnd, mode = AndroidPreGameScreenModeDto.from(assignedVariation),
            backgroundImage = imageService.toUrl(screenOffer.backgroundImage!!),
            amountImage = imageService.toUrl(screenOffer.amountImage!!)
          )
      )
    }
  }

  private suspend fun calculate3HTimerEndInSeconds(userId: String): Long = with(userTimeZoneService) {
    val nowUTC = timeService.now()

    val resultUTC = userGamePersistenceService
      .getLastTimeUserOpenedPreGameScreen(userId)
      ?.takeIf { Duration.between(it, nowUTC).toDays() == 0L }
      ?: nowUTC

    return resultUTC
      .atUserZone(userId)
      .toInstant()
      .plus(3, ChronoUnit.HOURS)
      .epochSecond
  }

  private val screens = mapOf(
    TREASURE_MASTER_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_tm_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_tm_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    MIX_BLOX_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_mixblox_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_mixblox_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    MAD_SMASH_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_madsmash_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_madsmash_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    BALL_BOUNCE_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_ballbounce_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_ballbounce_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    HEXA_PUZZLE_FUN_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_hexapuzzle_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_hexapuzzle_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    SOLITAIRE_VERSE_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_solitaireverse_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_solitaireverse_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    PUZZLE_POP_BLASTER_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_popblast_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_popblast_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    WORD_SEEKER_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_wordseeker_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_wordseeker_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    TRIVIA_MADNESS_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_triviamadness_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_triviamadness_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    MERGE_BLAST_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_mergeblast_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_mergeblast_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    EMOJICLICKERS_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_emojiclickers_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_emojiclickers_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    HEX_MATCH_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_hexmatch_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_hexmatch_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    WOODEN_PUZZLE_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_zentile_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_zentile_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    SUGAR_RUSH_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_sugarrush_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_sugarrush_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    CARS_MERGE_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_cars_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_cars_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    BLOCK_HOLE_CLASH_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_blockhole_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_blockhole_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    IDLE_MERGE_FUN_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_idlemergefun_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_idlemergefun_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    BLOCK_SLIDER_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_blockslider_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_blockslider_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    BUBBLE_POP_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_bubblepop_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_bubblepop_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    MARBLE_MADNESS_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_marblemadness_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_marblemadness_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    BRICK_DOKU_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_brickdoku_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_brickdoku_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    SUDOKU_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_sudoku_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_sudoku_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    WORD_KITCHEN_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_wordkitchen_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_wordkitchen_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    WATER_SORTER_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_watersort_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_watersort_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    COLOR_LOGIC_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_colorlogic_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_colorlogic_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    DICE_LOGIC_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_dicelogic_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_dicelogic_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    CRYSTAL_CRUSH_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_crystalcrush_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_crystalcrush_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    FAIRY_TALE_MANSION_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_fairytalemansion_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_fairytalemansion_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    SPIRAL_DROP_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_spiraldrop_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_spiraldrop_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    TILE_MATCH_PRO_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_tilematchpro_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_tilematchpro_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    SPACE_CONNECT_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_spaceconnect_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_spaceconnect_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    TANGRAM_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_tangram_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_tangram_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    BLOCKBUSTER_APP_ID to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_blockbuster_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_blockbuster_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    ),
    DEFAULT_JUSTPLAY_SCREEN to AndroidPreGameScreenContent(
      backgroundImage = "games/pre_game_screen/android_pre_game_screen_justplay_image.png",
      amountImage = "games/pre_game_screen/android_pre_game_screen_justplay_amount.png",
      badgeText = "Top earners made this week",
      badgeTextColor = "#1276FD",
      buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
      buttonSubText = "Your Adventure Awaits!",
    )
  )
}