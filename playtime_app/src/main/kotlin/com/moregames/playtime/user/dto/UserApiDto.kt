package com.moregames.playtime.user.dto

import com.moregames.base.coins.UserCurrentCoinsGoalBalance
import com.moregames.base.user.dto.PrivacyRegulationType
import com.moregames.base.util.ClientVersionsSupport
import com.moregames.base.util.ClientVersionsSupport.ANDROID_COINS_AMOUNT_SEPARATOR_APP_VERSION
import com.moregames.base.util.ClientVersionsSupport.ANDROID_GET_USER_COIN_GOAL_STRING_UNUSED_APP_VERSION
import com.moregames.base.util.InstantAsString
import com.moregames.playtime.ios.cashoutcoins.CashoutCoinsApiDto
import com.moregames.playtime.user.UserPersistenceService.Companion.MAIN_COIN_GOAL_LABEL
import com.moregames.playtime.user.UserPersistenceService.Companion.MAIN_COIN_GOAL_REACHED_LABEL
import com.moregames.playtime.user.cashout.AndroidIncompleteCashoutRestoringMode
import com.moregames.playtime.user.cashout.dto.AndroidCashoutProgressBarMode
import com.moregames.playtime.user.cashout.dto.AndroidFaceScanPreScreenParams
import com.moregames.playtime.user.dto.threedotmenu.ThreeDotMenuItem
import com.moregames.playtime.user.gamestories.AndroidGameStoriesModeApiDto
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.time.Instant

@Serializable
data class UserApiDto @OptIn(ExperimentalSerializationApi::class) constructor(
  @SerialName("id")
  val userId: String,
  // goal current coins (back compatibility, previously - coins visible on main screen)
  val coins: Int,
  @Deprecated("Unused from version $ANDROID_COINS_AMOUNT_SEPARATOR_APP_VERSION")
  val coinsString: String,
  // coins visible on main screen
  val coinsBalance: Long?,
  val cashoutPeriodId: String? = null,

  // goal region
  val coinGoal: Int? = null,
  @Deprecated("Unused from version $ANDROID_GET_USER_COIN_GOAL_STRING_UNUSED_APP_VERSION")
  val coinGoalString: String? = null,
  val coinGoalLabel: String,
  val coinGoalBarMode: CoinGoalBarMode? = null,
  val milestonesConfig: List<MilestoneDesc> = emptyList(),
  // end goal region

  val cashoutAvailable: Boolean,
  val cashoutAmount: String = "",
  val cashoutAmountBefore: String? = null,
  val nextCashoutTimestamp: InstantAsString? = null,
  val useRewards: Boolean,
  val timestamp: InstantAsString,
  val expLabels: Map<String, String>,
  val tutorialSteps: List<String>,
  val videoAdIntervalSeconds: Int?,
  val showTimerInCoinGoalSection: Boolean,
  val threeDotMenuItems: List<ThreeDotMenuItem>,
  val useAmplitudeAnalytics: Boolean,
  val market: String? = null,
  val attestationRequired: Boolean? = null,
  val initializeApplovin: Boolean? = null,
  val consentedToAnalytics: Boolean? = null,
  @Deprecated("Offboarded from version ${ClientVersionsSupport.ANDROID_PLAY_STORE_NOTIFICATION_UNUSED_APP_VERSION}")
  val enablePlaystoreTrackingNotifications: Boolean? = true,
  @Deprecated("Offboarded from version ${ClientVersionsSupport.ANDROID_PLAYERS_ONLINE_UNUSED_APP_VERSION}")
  val playersOnlineType: String? = "CENTER_LONG_BLUE",
  val giftBoxInsteadOfEarnings: Boolean,
  val cashoutButtonStyle: CashoutButtonStyle?,
  val cashoutTimerSubtext: String? = "$$$ Boosted",
  val paymentProviderSurvey: PaymentProviderSurvey? = null,
  val cashoutProgressBarMode: AndroidCashoutProgressBarMode?,
  val incompleteCashoutRestoringMode: AndroidIncompleteCashoutRestoringMode? = null,
  @Deprecated("Offboarded from version ${ClientVersionsSupport.ANDROID_NUMBER_SEPARATOR_APP_VERSION}")
  val numberSeparatorType: String = "locale",
  val cashoutBonusCoins: CashoutCoinsApiDto? = null,
  val faceScanPreScreen: AndroidFaceScanPreScreenParams? = null,
  val paymentProviderAvailable: Boolean? = null,
  val demoGamesLaunchMode: DemoGameLaunchModeApiDto? = null,
  val privacyRegulation: PrivacyRegulationType? = null,
  val preGameMode: PreGameModeApiDto? = null,
  val onboardingProgressBarMode: String? = null,
  val celebrateEarningsConfig: CelebrateEarningsConfigApiDto? = null,
  val showPayPalLogo: Boolean? = null,
  val cashStreakMode: AndroidCashStreakModeApiDto? = null,
  val gameStoriesMode: AndroidGameStoriesModeApiDto? = null,
  val coinsConversionRatioLabelText: String? = null,
  val boostedMode: UserBoostedModeApiDto? = null,
) {
  @OptIn(ExperimentalSerializationApi::class)
  constructor(
    user: User,
    coinsBalance: UserCurrentCoinsGoalBalance,
    cashoutAvailable: Boolean,
    cashoutAmount: String,
    cashoutAmountBefore: String? = null,
    nextCashoutTimestamp: Instant?,
    useRewards: Boolean,
    timestamp: Instant,
    tutorialSteps: List<String>,
    videoAdIntervalSeconds: Int?,
    showTimerInCoinGoalSection: Boolean,
    threeDotMenuItems: List<ThreeDotMenuItem>,
    useAmplitudeAnalytics: Boolean,
    market: String?,
    attestationRequired: Boolean?,
    initializeApplovin: Boolean?,
    consentedToAnalytics: Boolean?,
    coinGoal: Int?,
    coinGoalReached: Boolean,
    giftBoxInsteadOfEarnings: Boolean,
    cashoutButtonStyle: CashoutButtonStyle?,
    cashoutTimerSubtext: String?,
    paymentProviderSurvey: PaymentProviderSurvey?,
    cashoutProgressBarMode: AndroidCashoutProgressBarMode?,
    incompleteCashoutRestoringMode: AndroidIncompleteCashoutRestoringMode?,
    cashoutBonusCoins: CashoutCoinsApiDto?,
    faceScanPreScreen: AndroidFaceScanPreScreenParams?,
    paymentProviderAvailable: Boolean?,
    demoGamesLaunchMode: DemoGameLaunchModeApiDto? = null,
    privacyRegulation: PrivacyRegulationType?,
    preGameMode: PreGameModeApiDto? = null,
    onboardingProgressBarMode: String? = null,
    celebrateEarningsConfig: CelebrateEarningsConfigApiDto? = null,
    showPayPalLogo: Boolean? = null,
    cashStreakMode: AndroidCashStreakModeApiDto? = null,
    gameStoriesMode: AndroidGameStoriesModeApiDto? = null,
    coinGoalBarMode: CoinGoalBarMode?,
    cashoutPeriodId: String,
    milestonesConfig: List<MilestoneDesc>,
    coinsConversionRatioLabelText: String? = null,
    boostedMode: UserBoostedModeApiDto? = null,
  ) :
    this(
      userId = user.userId,
      coins = coinsBalance.goalCoins,
      coinsString = coinsBalance.coins.toString(),
      coinsBalance = coinsBalance.coins,
      coinGoal = coinGoal,
      coinGoalString = coinGoal?.toString(),
      cashoutAvailable = cashoutAvailable,
      cashoutAmount = cashoutAmount,
      cashoutAmountBefore = cashoutAmountBefore,
      nextCashoutTimestamp = nextCashoutTimestamp,
      useRewards = useRewards,
      timestamp = timestamp,
      coinGoalLabel = if (coinGoalReached) MAIN_COIN_GOAL_REACHED_LABEL else MAIN_COIN_GOAL_LABEL,
      expLabels = user.expLabels,
      tutorialSteps = tutorialSteps,
      videoAdIntervalSeconds = videoAdIntervalSeconds,
      showTimerInCoinGoalSection = showTimerInCoinGoalSection,
      threeDotMenuItems = threeDotMenuItems,
      useAmplitudeAnalytics = useAmplitudeAnalytics,
      market = market,
      attestationRequired = attestationRequired,
      initializeApplovin = initializeApplovin,
      consentedToAnalytics = consentedToAnalytics,
      giftBoxInsteadOfEarnings = giftBoxInsteadOfEarnings,
      cashoutButtonStyle = cashoutButtonStyle,
      cashoutTimerSubtext = cashoutTimerSubtext,
      paymentProviderSurvey = paymentProviderSurvey,
      cashoutProgressBarMode = cashoutProgressBarMode,
      incompleteCashoutRestoringMode = incompleteCashoutRestoringMode,
      cashoutBonusCoins = cashoutBonusCoins,
      faceScanPreScreen = faceScanPreScreen,
      paymentProviderAvailable = paymentProviderAvailable,
      demoGamesLaunchMode = demoGamesLaunchMode,
      privacyRegulation = privacyRegulation,
      preGameMode = preGameMode,
      onboardingProgressBarMode = onboardingProgressBarMode,
      celebrateEarningsConfig = celebrateEarningsConfig,
      showPayPalLogo = showPayPalLogo,
      cashStreakMode = cashStreakMode,
      gameStoriesMode = gameStoriesMode,
      coinGoalBarMode = coinGoalBarMode,
      cashoutPeriodId = cashoutPeriodId,
      milestonesConfig = milestonesConfig,
      coinsConversionRatioLabelText = coinsConversionRatioLabelText,
      boostedMode = boostedMode,
    )

  enum class CoinGoalBarMode {
    EM3
  }

  @Serializable
  data class MilestoneDesc(
    val milestone: Int,
    val reward: Int,
  )
}