package com.moregames.playtime.user.gamerank.calculators

import com.moregames.base.messaging.dto.UserChallengeProgressDto
import com.moregames.playtime.user.challenge.progress.achievement.AchievementDto
import com.moregames.playtime.user.gamerank.GameRank
import com.moregames.playtime.user.gamerank.GameRankConfig

interface GameRankProgressCalculator {
  fun calculateProgress(
    progressDto: UserChallengeProgressDto,
    currentProgress: Int,
    currentAchievement: AchievementDto?,
    config: GameRankConfig
  ): GameRankProgress
}

data class GameRankProgress(
  val rank: GameRank,
  val progress: Int,
  val achievement: AchievementDto?,
)