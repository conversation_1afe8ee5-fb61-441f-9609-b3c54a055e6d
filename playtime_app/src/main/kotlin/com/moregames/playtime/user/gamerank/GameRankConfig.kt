package com.moregames.playtime.user.gamerank

data class GameRankConfig(
  val oneStarProgressMax: Int,
  val twoStarProgressMax: Int,
  val threeStarProgressMax: Int,
  val calculator: GameRankCalculator,
) {
  val progressMax = threeStarProgressMax
}

object GameRankConfigHolder {
  val gamesConfig = mapOf(
    // solitaire
    200044 to GameRankConfig(
      oneStarProgressMax = 10,
      twoStarProgressMax = 30,
      threeStarProgressMax = 100,
      calculator = GameRankCalculator.SOLITAIRE,
    ),
    // sugar rush
    200052 to GameRankConfig(
      oneStarProgressMax = 10,
      twoStarProgressMax = 30,
      threeStarProgressMax = 100,
      calculator = GameRankCalculator.LEVEL_ID,
    ),
    // blockbuster
    200074 to GameRankConfig(
      oneStarProgressMax = 1000,
      twoStarProgressMax = 10000,
      threeStarProgressMax = 40000,
      calculator = GameRankCalculator.SCORE_PROGRESS,
    ),
    // pinmaster
    200075 to GameRankConfig(
      oneStarProgressMax = 10,
      twoStarProgressMax = 30,
      threeStarProgressMax = 100,
      calculator = GameRankCalculator.LEVEL_ID,
    ),
    // bubblepop
    200057 to GameRankConfig(
      oneStarProgressMax = 10,
      twoStarProgressMax = 30,
      threeStarProgressMax = 100,
      calculator = GameRankCalculator.LEVEL_ID,
    ),
  )
}