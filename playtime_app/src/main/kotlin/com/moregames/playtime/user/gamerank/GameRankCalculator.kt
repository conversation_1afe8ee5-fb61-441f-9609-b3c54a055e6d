package com.moregames.playtime.user.gamerank

import com.moregames.playtime.user.gamerank.calculators.GameRankProgressCalculator
import com.moregames.playtime.user.gamerank.calculators.LevelIdGameRankProgressCalculator
import com.moregames.playtime.user.gamerank.calculators.SingleSessionGameRankProgressCalculator
import com.moregames.playtime.user.gamerank.calculators.SolitaireGameRankProgressCalculator
import kotlin.reflect.KClass

enum class GameRankCalculator(val calculatorClass: KClass<out GameRankProgressCalculator>) {
  LEVEL_ID(LevelIdGameRankProgressCalculator::class),
  SCORE_PROGRESS(SingleSessionGameRankProgressCalculator::class),
  SOLITAIRE(SolitaireGameRankProgressCalculator::class),
}