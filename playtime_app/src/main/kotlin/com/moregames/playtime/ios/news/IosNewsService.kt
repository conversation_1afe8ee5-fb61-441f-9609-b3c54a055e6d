package com.moregames.playtime.ios.news

import com.google.inject.Inject
import com.google.inject.Provider
import com.google.inject.Singleton
import com.moregames.base.app.BuildVariant
import com.moregames.base.util.IoCoroutineScope
import com.moregames.base.util.buildCache
import com.moregames.playtime.app.ImageService
import com.moregames.playtime.games.IosGameOffer
import com.moregames.playtime.games.IosGameService
import com.moregames.playtime.translations.TranslationService
import kotlinx.coroutines.async
import java.util.*

@Singleton
class IosNewsService @Inject constructor(
  private val iosNewsPersistenceService: IosNewsPersistenceService,
  private val imageService: ImageService,
  private val translationService: TranslationService,
  private val iosGameService: IosGameService,
  buildVariant: BuildVariant,
  private val coroutineScope: Provider<IoCoroutineScope>,
) {

  private val newsCache = buildCache(buildVariant, expireAfter = 15L) { _: String ->
    coroutineScope.get().async {
      getNews()
    }
  }

  suspend fun getLocalizedNews(locale: Locale) = NewsListApiDto(
    newsCache.get("stub").await()
      .map { news -> translateNews(news, locale) }
  )

  private suspend fun getNews(): List<NewsApiDto> {
    val news = iosNewsPersistenceService.getNews()
    val relatedGameOffers =
      iosGameService.loadNewsRelatedGameOffers(news.mapNotNull { it.relatedGameApplicationId }.toSet()).associateBy({ it.applicationId }, { it })
    return news
      .map { toApiDto(it, relatedGameOffers[it.relatedGameApplicationId]) }
  }

  private fun toApiDto(news: IosNews, relatedGame: IosGameOffer?) =
    NewsApiDto(
      title = news.title,
      text = news.text,
      imageUrl = imageService.toUrl(news.image),
      detailedDescription = news.detailedDescription,
      detailedImageUrl = news.detailedImage?.let { imageService.toUrl(news.detailedImage) },
      link = news.link,
      featureSurveyId = news.surveyId,
      game = relatedGame?.let { offer ->
        NewsRelatedGameApiDto(
          appstoreId = offer.iosApplicationId,
          bundleId = offer.applicationId,
          installButtonText = offer.infoTextInstall,
          appScheme = offer.iosGameUrl,
        )
      }
    )

  private suspend fun translateNews(news: NewsApiDto, locale: Locale): NewsApiDto = with(translationService) {
    news.copy(
      title = tryTranslate(news.title, locale),
      text = tryTranslate(news.text, locale),
      detailedDescription = news.detailedDescription?.let { tryTranslate(it, locale) },
      game = news.game?.copy(
        installButtonText = tryTranslate(news.game.installButtonText, locale)
      )
    )
  }

}