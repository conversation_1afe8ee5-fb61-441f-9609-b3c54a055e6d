package com.moregames.playtime.app

import AbTestingApiGrpc
import com.google.inject.Guice
import com.justplayapps.playtime.games.progress.proto.GameProgress
import com.justplayapps.playtime.proto.PlaytimeEvents
import com.justplayapps.playtime.rewarding.boostedmode.BoostedModeApiGrpc
import com.justplayapps.playtime.rewarding.proto.PlaytimeRewardingApiGrpc
import com.justplayapps.service.rewarding.bonus.UserBonusBalanceApiImpl
import com.justplayapps.service.rewarding.bonus.proto.UserBonusBalanceApiGrpc
import com.justplayapps.service.rewarding.earnings.EmApiImpl
import com.justplayapps.service.rewarding.earnings.em3.AddRevenueCoinsEventPushSubscriber
import com.justplayapps.service.rewarding.earnings.proto.EmApiGrpc
import com.justplayapps.service.rewarding.earnings.proto.RewardingEvents
import com.justplayapps.service.rewarding.revenue.RevenueReceivedMessagePushSubscriber
import com.moregames.base.app.BuildVariant
import com.moregames.base.app.ModulesContainer
import com.moregames.base.app.ModulesInitializer
import com.moregames.base.bus.MessageBusModule
import com.moregames.base.config.ServicesRegistry
import com.moregames.base.grpc.server.GrpcModule
import com.moregames.base.grpc.server.GrpcServerConfig
import com.moregames.base.lifecycle.ServiceManagerModule
import com.moregames.base.lifecycle.getServiceManager
import com.moregames.base.messaging.push.PubsubPushMessagesModule
import com.moregames.base.util.logger
import com.moregames.playtime.boost.BoostedModeApiImpl
import com.moregames.playtime.health.HealthService
import com.moregames.playtime.service.PlaytimeGamesApiImpl
import com.moregames.playtime.service.PlaytimeRewardingApiImpl
import com.moregames.playtime.subscribers.*
import com.moregames.playtime.user.abtesting.AbTestingApiImpl
import com.moregames.playtime.user.cashout.TangoCardPaymentProvidersService
import com.moregames.playtime.user.challenge.ChallengeModule
import com.moregames.playtime.user.challenge.progress.ChallengeProgressSubscriber
import com.moregames.playtime.user.coingoal.GenerateNewCoinGoalSetSubscriber
import com.moregames.playtime.user.gamerank.UserGameRankModule
import com.moregames.playtime.user.luckyhour.StartLuckyHourCommand
import com.moregames.playtime.user.usergame.RemindToPlayNotifySubscriber
import io.ktor.application.*
import io.ktor.features.*
import io.ktor.routing.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.InternalSerializationApi

/**
 * Entry Point of the application as defined in resources/application.conf.
 *
 * See [https://ktor.io/servers/configuration.html#hocon-file](https://ktor.io/servers/configuration.html#hocon-file)
 */
@Suppress("unused")
@InternalSerializationApi
fun Application.main() {
  val buildVariant = BuildVariant.byKey(System.getenv("NODE_ENV"))

  // This adds Date and Server headers to each response, and allows custom additional headers
  install(DefaultHeaders) {
    header("Connection", "close")
  }

  install(IgnoreTrailingSlash)

  val playtimeApplication = PlaytimeApplication(ModulesContainer(this, buildVariant, playtimeModules))
  playtimeApplication.onStart()

  environment.monitor.subscribe(ApplicationStopped) {
    playtimeApplication.onShutdown()
  }
}

val playtimeModules: ModulesInitializer = {
  +CoreModule(application, buildVariant)
  +PubsubPushMessagesModule(
    "subscribers",
    AdMarketEventAdjustPushSubscriber::class,
    AdMarketEventGaPushSubscriber::class,
    GameSessionEndedEventPushSubscriber::class,
    UserBalanceUpdatePushSubscriber::class,
    UserGotSpecificAmountRevenueAdjustPushSubscriber::class,
    UserGotSpecificAmountRevenueGaPushSubscriber::class,
    UserVariationActivatedEventPushSubscriber::class,
    UserVariationAssignedEventPushSubscriber::class,
    XMinutesToCashoutPeriodEndPushSubscriber::class,
    XHoursPassedSinceNoCashoutCheckPushSubscriber::class,
    XHoursPassedSinceNoEarningsCheckPushSubscriber::class,
    XHoursPassedSinceUnclaimedEarningsCheckPushSubscriber::class,
    AfterXMinUserCreationAddBonusCoinsMessagePushSubscriber::class,
    CashoutPeriodEndedMessagePushSubscriber::class,
    EarningsAddedMessagePushSubscriber::class,
    PaymentRejectedEventMessagePushSubscriber::class,
    PaymentSentEventMessagePushSubscriber::class,
    RevenueReceivedMessagePushSubscriber::class,
    UserAppInactivityPushReminderPushSubscriber::class,
    UserGameInactivityPushReminderPushSubscriber::class,
    UserPeriodicReminderPushSubscriber::class,
    UserPersonalsDeletedMessagePushSubscriber::class,
    GenerateNewCoinGoalSetSubscriber::class,
    RemindToPlayNotifySubscriber::class,
    AdImpressionEventPushSubscriber::class,
    InstallGamesWhenNoCoinsSubscriber::class,
    FirstGamePlayedEventPushSubscriber::class,
    ChallengeProgressSubscriber::class,
    RewardEarningsAddedEventSubscriber::class,
    CoinGoalReachedEventPushSubscriber::class,
    AddRevenueCoinsEventPushSubscriber::class,
    WebAppUserRegistrationPushSubscriber::class,
    WebAppUserAdditionalDataPushSubscriber::class,
    WebAppUserJailBreakCheckPushSubscriber::class,
  )
  +MessageBusModule {
    rootPackages("com.moregames.playtime", "com.justplayapps.service.rewarding")
    pullEndpoint("playtime-generic", default = true)
    pushListenOnly(path = "/playtime-events-generic")
    pullListenOnly("add-user-coins")

    val revenueSavedRoute by pullEndpoint("revenue-saved")
    val calculateTargetEarningsCoefficientRoute by publishEndpoint("calculate-target-earnings-coefficient")
    val createUserRevenueMessagesRoute by pullEndpoint("create-user-revenue-messages")
    val userCoinsAddedRoute by pullEndpoint("user-coins-added")
    val em3CoinsRevenueCheckRoute by pullEndpoint("em3-coins-revenue-check")

    val luckyHourNotifications by pullEndpoint("lucky-hour-notification", queueName = "lucky-hour-notification-tf")
    routing {
      route<RewardingEvents.RevenueSavedEventProto>() to revenueSavedRoute
      route<GameProgress.CalculateTargetEarningsCoefficientCommand>() to calculateTargetEarningsCoefficientRoute
      route<RewardingEvents.CreateUserRevenueMessagesMessage>() to createUserRevenueMessagesRoute
      route<RewardingEvents.UserCoinsAddedEvent>() to userCoinsAddedRoute
      route<PlaytimeEvents.Em3CoinsRevenueCheckMessage>() to em3CoinsRevenueCheckRoute

      route<StartLuckyHourCommand>() to luckyHourNotifications
    }
  }
  +GrpcModule {
    externalServer(GrpcServerConfig.DEFAULT) {
      service<PlaytimeGamesApiImpl>()
      service<EmApiImpl>()
    }
    internalServer {
      service<UserBonusBalanceApiImpl>()
      service<EmApiImpl>()
      service<AbTestingApiImpl>()
      service<PlaytimeRewardingApiImpl>()
      service<BoostedModeApiImpl>()
    }
    ServicesRegistry.PLAYTIME {
      localStub(UserBonusBalanceApiGrpc::newStub)
      localStub(EmApiGrpc::newStub)
      localStub(AbTestingApiGrpc::newStub)
      localStub(PlaytimeRewardingApiGrpc::newStub)
      localStub(BoostedModeApiGrpc::newStub)
    }
  }
  +ChallengeModule()
  +ServiceManagerModule()
  +UserGameRankModule()
}

@InternalSerializationApi
class PlaytimeApplication(modulesContainer: ModulesContainer) {

  private val injector = Guice.createInjector(modulesContainer)

  fun onStart() {
    val buildVariant = injector.getInstance(BuildVariant::class.java)
    logger().info("Starting application with build variant $buildVariant")

    runBlocking {
      injector.getInstance(ApiManager::class.java).initApi()
      if (!injector.getInstance(HealthService::class.java).isHealthy())
        throw IllegalStateException("Service not healthy")

      // may not wait and may not worry about exceptions
      CoroutineScope(Dispatchers.IO).launch {
        injector.getInstance(AdministrationApiRouter::class.java).initPages()
        // what it's for? eliminate first call latency?
        injector.getInstance(TangoCardPaymentProvidersService::class.java).init()
      }
      injector.getServiceManager().startAsync().awaitHealthy()
    }
  }

  fun onShutdown() {
    injector.getServiceManager().stopAsync().awaitStopped()
  }
}
