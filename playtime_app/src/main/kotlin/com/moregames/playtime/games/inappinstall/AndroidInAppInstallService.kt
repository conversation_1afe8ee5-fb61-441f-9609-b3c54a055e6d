package com.moregames.playtime.games.inappinstall

import com.google.inject.Inject
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.util.ApplicationId
import com.moregames.base.util.ApplicationId.SOLITAIRE_VERSE_APP_ID
import com.moregames.base.util.ApplicationId.TREASURE_MASTER_APP_ID
import com.moregames.playtime.app.ImageService
import com.moregames.playtime.games.inappinstall.dto.AndroidInAppInstallInfoDto
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Singleton

@Singleton
class AndroidInAppInstallService @Inject constructor(
  private val abTestingService: AbTestingService,
  imageService: ImageService,
) {
  companion object {
    const val INSTALL_VIA_DIGITAL_TURBINE = "dti"
    val SUPPORTED_GAMES_LIST = listOf(TREASURE_MASTER_APP_ID, SOLITAIRE_VERSE_APP_ID)
  }

  private val treasureMasterDetails = AndroidInAppInstallInfoDto(
    installMethod = INSTALL_VIA_DIGITAL_TURBINE,
    packageName = TREASURE_MASTER_APP_ID,
    appName = "Treasure Master",
    developer = "Gimica GmbH",
    apkFileSize = (110.8 * 1024 * 1024).toLong(),
    updateTimestamp = SimpleDateFormat("yyyy.MM.dd", Locale.ENGLISH).parse("2024.07.08")?.time,
    description = """
      How to earn: (1) Install the game - (2) Play - (3) Defeat monsters for coins!

      Treasure Master will take you in an endless treasure hunt!
      
      Different Monsters, Boss Battles and more!

      Shoot arrows at the bosses and make sure you don't miss. The better you aim the further you get. But be careful the game gets harder as you progress.

      Treasure Master offers an endless shooting journey and will entertain you for weeks.
      """.trimIndent(),
    iconUrl = imageService.toUrl("treasure_master.png"),
    screenshots = listOf(
      imageService.toUrl("screenshots/TreasureMaster-1.webp"),
      imageService.toUrl("screenshots/TreasureMaster-2.webp"),
      imageService.toUrl("screenshots/TreasureMaster-3.webp"),
      imageService.toUrl("screenshots/TreasureMaster-4.webp"),
    ),
    website = "https://gimica.com",
    email = "<EMAIL>",
    privacyLink = "https://www.gimica.com/privacy-policy",
    permissions = "",
  )
  private val solitaireVerseDetails = AndroidInAppInstallInfoDto(
    installMethod = INSTALL_VIA_DIGITAL_TURBINE,
    packageName = ApplicationId.SOLITAIRE_VERSE_APP_ID,
    appName = "Solitaire Verse",
    developer = "Gimica GmbH",
    apkFileSize = (92.3 * 1024 * 1024).toLong(),
    updateTimestamp = SimpleDateFormat("yyyy.MM.dd", Locale.ENGLISH).parse("2024.07.04")?.time,
    description = """
      How to earn: (1) Install the game - (2) Play - (3) Play cards your way and earn!

      Enjoy your free classic solitaire with more game modes and daily challenges!

      The more daily challenges you complete the better rank you will reach. You can also choose between different solitaire scoring types (Normal, Vegas, Cumulative Vegas).

      With customizable cards and backgrounds you can set your solitaire game however you like!
      """.trimIndent(),
    iconUrl = imageService.toUrl("solitaire_verse_green.jpg"),
    screenshots = listOf(
      imageService.toUrl("screenshots/SolitaireVerse-1.webp"),
      imageService.toUrl("screenshots/SolitaireVerse-2.webp"),
      imageService.toUrl("screenshots/SolitaireVerse-3.webp"),
      imageService.toUrl("screenshots/SolitaireVerse-4.webp"),
    ),
    website = "https://gimica.com",
    email = "<EMAIL>",
    privacyLink = "https://www.gimica.com/privacy-policy",
    permissions = "",
  )

  suspend fun getInAppInstallInfo(userId: String, applicationId: String): AndroidInAppInstallInfoDto? {
    if (applicationId !in SUPPORTED_GAMES_LIST) return null
    if (!abTestingService.isUserExperimentParticipant(userId, ClientExperiment.ANDROID_DIGITAL_TURBINE_IGNITE_INSTALL)) return null

    return when (applicationId) {
      TREASURE_MASTER_APP_ID -> treasureMasterDetails
      ApplicationId.SOLITAIRE_VERSE_APP_ID -> solitaireVerseDetails
      else -> null
    }
  }

}
