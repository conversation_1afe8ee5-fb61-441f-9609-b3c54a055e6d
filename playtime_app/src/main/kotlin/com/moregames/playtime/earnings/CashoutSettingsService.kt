package com.moregames.playtime.earnings

import com.google.inject.Inject
import com.google.inject.Provider
import com.moregames.base.app.BuildVariant
import com.moregames.base.config.CashoutConfig
import com.moregames.base.featureflags.FeatureFlagsFacade
import com.moregames.base.offers.ConfigPersistenceService
import com.moregames.base.util.IoCoroutineScope
import com.moregames.base.util.buildCache
import com.moregames.playtime.app.limitEarningsForUsersCreatedAfterMay292025
import com.moregames.playtime.app.useIncreasedEarningsThreshold
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.user.UserPersistenceService
import com.moregames.playtime.user.UserService
import kotlinx.coroutines.async
import java.math.BigDecimal
import java.time.Instant
import javax.inject.Singleton

@Singleton
class CashoutSettingsService @Inject constructor(
  private val configPersistenceService: ConfigPersistenceService,
  private val userPersistenceService: UserPersistenceService,
  private val rewardingFacade: RewardingFacade,
  buildVariant: BuildVariant,
  private val coroutineScope: Provider<IoCoroutineScope>,
  private val featureFlagsFacade: FeatureFlagsFacade,
  private val userService: UserService,
) {

  companion object {
    const val CACHE_KEY_STUB = "stub"
    val INCREASED_EARNINGS_THRESHOLD = BigDecimal("100")
    val REGULAR_EARNINGS_THRESHOLD = BigDecimal("30")
    val HARD_LIMIT_USER_CREATION_DATE_FOR_EARNINGS_CAP = Instant.parse("2025-05-29T00:00:00Z")
  }

  private val settingsCache = buildCache(buildVariant, expireAfter = 5L) { _: String ->
    coroutineScope.get().async {
      configPersistenceService.loadCashoutConfig()
    }
  }

  @Suppress("DEPRECATION")
  suspend fun getUserMaxEarningsAmount(userId: String): BigDecimal {
    val countryTierSettings = userPersistenceService.getUserCountryTierSettings(userId)
    val userCreationDate = userService.getUser(userId, includingDeleted = true).createdAt
    val multiplier = countryTierSettings?.maxCashoutAmountMultiplier ?: BigDecimal.ONE

    return when {
      featureFlagsFacade.limitEarningsForUsersCreatedAfterMay292025()
        && userCreationDate.isAfter(HARD_LIMIT_USER_CREATION_DATE_FOR_EARNINGS_CAP) -> {
        REGULAR_EARNINGS_THRESHOLD * multiplier
      }

      featureFlagsFacade.useIncreasedEarningsThreshold() ->
        INCREASED_EARNINGS_THRESHOLD * multiplier

      else ->
        setOf(
          getCashoutConfig().maxCashoutAmount * multiplier,
          rewardingFacade.getUnpaidUserEarningsUSD(userId)
        ).max()
    }
  }

  @Deprecated("it's better to add explicit methods for getting a setting instead of providing a whole set of settings each time")
  suspend fun getCashoutConfig(): CashoutConfig = settingsCache.get(CACHE_KEY_STUB).await()

  @Suppress("DEPRECATION")
  suspend fun getIronSourceCoinsToUsdConversionRatio() = getCashoutConfig().ironSourceCoinsToUsdConversionRatio

  @Suppress("DEPRECATION")
  suspend fun getTapjoyCoinsToUsdConversionRatio() = getCashoutConfig().tapjoyCoinsToUsdConversionRatio

  @Suppress("DEPRECATION")
  suspend fun getOfferwallCoinsToUsdConversionRatio() = getCashoutConfig().offerwallCoinsToUsdConversionRatio

  suspend fun getAdjoeCoinsToUsdConversionRatio() = getOfferwallCoinsToUsdConversionRatio()

  @Suppress("DEPRECATION")
  suspend fun getFyberCoinsToUsdConversionRatio() = getCashoutConfig().fyberCoinsToUsdConversionRatio

}

