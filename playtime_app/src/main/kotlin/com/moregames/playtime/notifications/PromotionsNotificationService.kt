package com.moregames.playtime.notifications

import com.google.inject.Inject
import com.google.inject.Singleton
import com.moregames.base.app.OfferWallType
import com.moregames.base.bus.MessageBus
import com.moregames.base.util.TimeService
import com.moregames.base.util.localUtcDate
import com.moregames.playtime.user.NotificationsPersistenceService
import com.moregames.playtime.user.NotificationsPersistenceService.PromotionsEmailNotificationDto.Companion.toEmailNotificationEvent


@Singleton
class PromotionsNotificationService @Inject constructor(
  private val notificationsPersistenceService: NotificationsPersistenceService,
  private val timeService: TimeService,
  private val messageBus: MessageBus,
) {

  companion object {
    const val MONTHS_TO_INACTIVE_STATUS = 6L
  }

  suspend fun scheduleNotifications(batchSize: Int, lastChunkUserIdFirstLetter: String, offerWallType: OfferWallType): Int {
    val notificationIds = notificationsPersistenceService.getScheduledPromotionsNotificationsIdsForTomorrow(offerWallType)
    if (notificationIds.isEmpty()) {
      return 0
    }

    val minLastActiveAtDay = timeService.now().localUtcDate().minusMonths(MONTHS_TO_INACTIVE_STATUS)
    val userIds = notificationsPersistenceService.getActiveUsersForNotification(
      userIdStartsWith = lastChunkUserIdFirstLetter,
      notificationIds = notificationIds,
      minLastActiveAtDay = minLastActiveAtDay,
      batchSize = batchSize,
      offerWallType = offerWallType
    )
    if (userIds.isNotEmpty()) {
      notificationsPersistenceService.setUpScheduledNotifications(notificationIds, userIds)
    }

    return userIds.size
  }

  suspend fun sendPromotionsEmailsBatch(batchSize: Int, userIdPrefix: String): Int {
    val now = timeService.now()
    val minLastActiveAtDay = now.localUtcDate().minusMonths(MONTHS_TO_INACTIVE_STATUS)

    return notificationsPersistenceService.getActiveUsersForPromotionsEmails(
      userIdPrefix = userIdPrefix,
      minLastActiveAtDay = minLastActiveAtDay,
      now = now,
      batchSize = batchSize,
    ).chunked(25)
      .sumOf { chunk ->
        messageBus.publish(*chunk.map { it.toEmailNotificationEvent() }.toTypedArray())
        notificationsPersistenceService.trackPromotionsEmailSending(
          notificationDtoList = chunk,
          sentAt = timeService.now()
        )
        chunk.size
      }
  }

  suspend fun cleanUpProcessedPromotionsEmails(batchSize: Int) =
    notificationsPersistenceService.cleanUpProcessedPromotionsEmails(batchSize)

}
