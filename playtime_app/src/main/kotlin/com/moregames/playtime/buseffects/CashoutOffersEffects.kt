package com.moregames.playtime.buseffects

import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.variations.SpecialCashoutOffersVariation
import com.moregames.base.abtesting.variations.SpecialCashoutOffersVariation.*
import com.moregames.base.bus.*
import com.moregames.base.util.logger
import com.moregames.playtime.notifications.PushNotification.AndroidPushNotification
import com.moregames.playtime.user.cashout.CashoutService
import com.moregames.playtime.user.cashout.offers.CashoutOffersService
import com.moregames.playtime.user.cashout.offers.CashoutOffersService.CashoutOffer
import com.moregames.playtime.user.cashout.offers.CashoutOffersService.CashoutOfferSet
import java.math.BigDecimal
import java.time.Instant
import javax.inject.Inject

class CashoutOffersEffects @Inject constructor(
  private val abTestingService: AbTestingService,
  private val cashoutOffersService: CashoutOffersService,
  private val messageBus: MessageBus,
  private val cashoutService: CashoutService,
) {

  @EffectHandler
  suspend fun handleCreateCashoutOfferSetEffect(effect: CreateCashoutOfferSetEffect) {
    val variation = abTestingService.assignedVariationValue(effect.userId, ClientExperiment.SPECIAL_CASHOUT_OFFERS) as? SpecialCashoutOffersVariation
      ?: return
    logger().debug("[CashoutOffers] Received CreateCashoutOfferSetEffect: {}", effect)
    when (variation) {
      ThreeCashoutOffers, ThreeRandomCashoutOffers -> cashoutOffersService.createSetIfNeeded(effect.userId)
      ThreeRandom025EarningCashoutOffers -> {
        if (cashoutService.userHasSuccessfulCashout(effect.userId) || earningsAmountIsEnough(effect.userId, 0.25)) {
          cashoutOffersService.createSetIfNeeded(effect.userId)
        }
      }

      ThreeRandom05EarningCashoutOffers -> {
        if (cashoutService.userHasSuccessfulCashout(effect.userId) || earningsAmountIsEnough(effect.userId, 0.5)) {
          cashoutOffersService.createSetIfNeeded(effect.userId)
        }
      }

      ThreeRandom1EarningCashoutOffers -> {
        if (cashoutService.userHasSuccessfulCashout(effect.userId) || earningsAmountIsEnough(effect.userId, 1.0)) {
          cashoutOffersService.createSetIfNeeded(effect.userId)
        }
      }
    }
  }

  private suspend fun earningsAmountIsEnough(userId: String, targetEarningsAmount: Double): Boolean {
    val amountUsd = cashoutService.getNonCashedUserCurrencyEarnings(userId).amountUsd
    return amountUsd > BigDecimal.ZERO && amountUsd < targetEarningsAmount.toBigDecimal()
  }

  @MessageHandler
  suspend fun handleCashoutOfferActivatedEvent(event: CashoutOfferActivatedEvent) = with(event) {
    val set = cashoutOffersService.getOfferSet(userId) as? CashoutOfferSet.Active ?: return@with

    if (set.offers.filter { it.gameId != gameId }
        .all { it is CashoutOffer.Claimed }) { // this condition indicates that all offers previous are claimed; thus this one is last
      messageBus.publish(
        message = CloseCashoutOfferSetCommand(userId),
        delayUntil = activeUntilDate
      )
    }

    messageBus.publishAsync(PushNotificationEffect(AndroidPushNotification.CashoutOfferStarted(userId, gameName)))
  }

  @MessageHandler
  suspend fun handleCloseCashoutOfferSetCommand(command: CloseCashoutOfferSetCommand) {
    cashoutOffersService.closeSetIfNeeded(command.userId)
  }
}

data class CreateCashoutOfferSetEffect(val userId: String) : Effect
data class CashoutOfferActivatedEvent(val userId: String, val gameId: Int, val gameName: String, val activeUntilDate: Instant) : Message
data class CloseCashoutOfferSetCommand(val userId: String) : Message