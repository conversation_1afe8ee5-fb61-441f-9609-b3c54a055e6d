package com.moregames.playtime.earnings

import assertk.assertThat
import assertk.assertions.isEqualByComparingTo
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.messaging.dto.RevenueReceivedEventDto
import com.moregames.base.util.answer
import com.moregames.base.util.mock
import com.moregames.playtime.earnings.dto.GenericRevenueDto
import com.moregames.playtime.user.UserPersistenceService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.CashoutPeriodsPersistenceService
import com.moregames.playtime.user.cashout.dto.CashoutPeriodDto
import com.moregames.playtime.user.challenge.ChallengeRevenueService
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import java.math.BigDecimal
import java.time.Duration
import java.time.Instant

class Em2CoinsCappingServiceTest {
  private val abTestingService: AbTestingService = mock()
  private val userService: UserService = mock()
  private val challengeRevenueService: ChallengeRevenueService = mock()
  private val cashoutPeriodsPersistenceService: CashoutPeriodsPersistenceService = mock()

  private val service = Em2CoinsCappingService(
    abTestingService = abTestingService,
    userService = userService,
    challengeRevenueService = challengeRevenueService,
    cashoutPeriodsPersistenceService = cashoutPeriodsPersistenceService,
  )

  private val userId = "user-id"
  private val now = Instant.now()
  private val revenueGame = GenericRevenueDto(
    id = "eventId",
    userId = "userId",
    timestamp = Instant.now(),
    source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
    amount = BigDecimal("2.00"),
    amountExtra = null,
    gameId = null,
  )
  private val currentCashoutPeriod = CashoutPeriodDto(userId, now, now, 1500, counter = 2, noEarningsCounter = 0, coinGoalMilestones = listOf(1, 2, 3))

  @BeforeEach
  fun before() {
    challengeRevenueService.answer(
      { revenueWithChallengesCut(any(), any()) },
      {
        (it.getArgument(1) as List<GenericRevenueDto>)
          .sumOf { rev -> rev.amount }
      }
    )
    cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriod(userId) }, currentCashoutPeriod)
    abTestingService.mock({ isTargetEarningsParticipant(userId) }, false)
  }

  @Test
  fun `SHOULD return zero ON calculateCappedGameRevenue WHEN coins are zero`() {
    runBlocking {
      service.calculateCappedGameRevenue(
        userId,
        gameCoins = BigDecimal.ZERO,
        coinsForOneDollar = BigDecimal("100.0"),
        gamesRealRevenues = listOf(revenueGame),
      )
    }.let { actual ->
      assertThat(actual).isEqualByComparingTo(BigDecimal.ZERO)
    }
  }

  @Test
  fun `SHOULD return zero ON calculateCappedGameRevenue WHEN no revenue`() {
    runBlocking {
      service.calculateCappedGameRevenue(
        userId,
        gameCoins = BigDecimal("123.4"),
        coinsForOneDollar = BigDecimal("100.0"),
        gamesRealRevenues = emptyList(),
      )
    }.let { actual ->
      assertThat(actual).isEqualByComparingTo(BigDecimal.ZERO)
    }
  }

  @Test
  fun `SHOULD return uncapped revenue ON calculateCappedGameRevenue WHEN coins less than revenue x2`() {
    runBlocking {
      service.calculateCappedGameRevenue(
        userId,
        gameCoins = BigDecimal("390"),
        coinsForOneDollar = BigDecimal("100.0"),
        gamesRealRevenues = listOf(revenueGame),
      )
    }.let { actual ->
      assertThat(actual).isEqualByComparingTo(BigDecimal("3.9"))
    }
  }

  @Test
  fun `SHOULD return capped revenue ON calculateCappedGameRevenue WHEN coins more than revenue x2`() {
    runBlocking {
      service.calculateCappedGameRevenue(
        userId,
        gameCoins = BigDecimal("410"),
        coinsForOneDollar = BigDecimal("100.0"),
        gamesRealRevenues = listOf(revenueGame),
      )
    }.let { actual ->
      assertThat(actual).isEqualByComparingTo(BigDecimal("4.0"))
    }
  }

  @Test
  fun `SHOULD raise cap limit ON calculateCappedGameRevenue WHEN less ads coins boost participant`() {
    val gameStartedAt = Instant.now()
    val nullGameRevenue = revenueGame.copy(gameId = null, amount = BigDecimal("1.0"))
    val unknownGameRevenue = revenueGame.copy(gameId = -1, amount = BigDecimal("2.0"))
    val noFirstPlayedAtGameRevenue = revenueGame.copy(gameId = 13, amount = BigDecimal("3.0"))
    val beforeFirst3hPeriodRevenue =
      revenueGame.copy(gameId = 42, amount = BigDecimal("5.0"), timestamp = gameStartedAt.minusSeconds(60))
    val inFirst3hPeriodRevenue =
      revenueGame.copy(gameId = 42, amount = BigDecimal("7.0"), timestamp = gameStartedAt.plusSeconds(60))
    val afterFirst3hPeriodRevenue =
      revenueGame.copy(gameId = 42, amount = BigDecimal("11.0"), timestamp = gameStartedAt.plus(Duration.ofMinutes(181)))
    val revenues = listOf(
      nullGameRevenue, unknownGameRevenue, noFirstPlayedAtGameRevenue,
      beforeFirst3hPeriodRevenue, inFirst3hPeriodRevenue, afterFirst3hPeriodRevenue
    )

    val gameCoinsStub = UserPersistenceService.GamePlayStatusDto(
      gameId = 0,
      coins = -1,
      playedRecently = true,
      firstPlayedAt = gameStartedAt,
      lastPlayedAt = gameStartedAt,
    )

    val someElseGame = gameCoinsStub.copy(gameId = 37)
    val noFirstPlayedAtGame = gameCoinsStub.copy(gameId = 13, firstPlayedAt = null)
    val normalGame = gameCoinsStub.copy(gameId = 42, firstPlayedAt = gameStartedAt)

    val gamesCoinsData = listOf(someElseGame, noFirstPlayedAtGame, normalGame).associateBy { it.gameId }

    abTestingService.mock({ getLessAdsCoinsBoostMultiplier(userId) }, BigDecimal("1.5"))
    userService.mock({ loadUserGameCoins(userId) }, gamesCoinsData)

    // ((7 + 5) * 1.5 + (1 + 2 + 3 + 11)) * 2
    val expectedCapUsd = BigDecimal("70.0")

    runBlocking {
      service.calculateCappedGameRevenue(
        userId,
        gameCoins = BigDecimal("121000"),
        coinsForOneDollar = BigDecimal("100.0"),
        gamesRealRevenues = revenues,
      )
    }.let { actual ->
      assertThat(actual).isEqualByComparingTo(expectedCapUsd)
    }
  }

  @Test
  fun `SHOULD respect challenges cut share ON calculateCappedGameRevenue WHEN capping`() {
    challengeRevenueService.mock({ revenueWithChallengesCut(userId, listOf(revenueGame)) }, BigDecimal("0.10"))

    runBlocking {
      service.calculateCappedGameRevenue(
        userId,
        gameCoins = BigDecimal("410"),
        coinsForOneDollar = BigDecimal("100.0"),
        gamesRealRevenues = listOf(revenueGame),
      )
    }.let { actual ->
      assertThat(actual).isEqualByComparingTo(BigDecimal("0.20"))
    }
  }

  @Test
  fun `SHOULD calculate revenue based on applovin only ON calculateCappedGameRevenue WHEN it is 1st CP AND user is a target earning participant`() {
    val currentCashoutPeriod = currentCashoutPeriod.copy(counter = 1)

    cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriod(userId) }, currentCashoutPeriod)
    abTestingService.mock({ isTargetEarningsParticipant(userId) }, true)
    challengeRevenueService.mock({ revenueWithChallengesCut(userId, listOf(revenueGame)) }, BigDecimal("0.90"))

    runBlocking {
      service.calculateCappedGameRevenue(
        userId,
        gameCoins = BigDecimal("410"),
        coinsForOneDollar = BigDecimal("100.0"),
        gamesRealRevenues = listOf(revenueGame),
      )
    }.let { actual ->
      assertThat(actual).isEqualByComparingTo(BigDecimal("0.90"))
    }
  }

  @Test
  fun `SHOULD calculate revenue by coins ON calculateCappedGameRevenue WHEN NOT it is 1st CP AND user is a target earning participant`() {
    abTestingService.mock({ isTargetEarningsParticipant(userId) }, true)
    challengeRevenueService.mock({ revenueWithChallengesCut(userId, listOf(revenueGame)) }, BigDecimal("0.90"))

    runBlocking {
      service.calculateCappedGameRevenue(
        userId,
        gameCoins = BigDecimal("410"),
        coinsForOneDollar = BigDecimal("100.0"),
        gamesRealRevenues = listOf(revenueGame),
      )
    }.let { actual ->
      assertThat(actual).isEqualByComparingTo(BigDecimal("1.8"))
    }
  }

  @Test
  fun `SHOULD calculate revenue by coins ON calculateCappedGameRevenue WHEN it is 1st CP AND NOT user is a target earning participant`() {
    val currentCashoutPeriod = currentCashoutPeriod.copy(counter = 1)

    cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriod(userId) }, currentCashoutPeriod)
    challengeRevenueService.mock({ revenueWithChallengesCut(userId, listOf(revenueGame)) }, BigDecimal("0.90"))

    runBlocking {
      service.calculateCappedGameRevenue(
        userId,
        gameCoins = BigDecimal("410"),
        coinsForOneDollar = BigDecimal("100.0"),
        gamesRealRevenues = listOf(revenueGame),
      )
    }.let { actual ->
      assertThat(actual).isEqualByComparingTo(BigDecimal("1.8"))
    }
  }
}