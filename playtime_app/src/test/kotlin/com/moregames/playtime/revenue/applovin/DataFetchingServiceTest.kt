package com.moregames.playtime.revenue.applovin

import ApplovingParseResponseException
import assertk.assertThat
import assertk.assertions.containsOnly
import assertk.assertions.isEmpty
import assertk.assertions.isEqualTo
import assertk.assertions.isNull
import com.moregames.playtime.revenue.applovin.DataFetchingService.Companion.REPORTING_URL
import com.moregames.playtime.revenue.applovin.dto.ApplovinRequest
import com.moregames.playtime.revenue.applovin.dto.ApplovinRevenue
import com.moregames.playtime.revenue.applovin.exception.ApplovinDataLoadInvalidException
import com.moregames.playtime.revenue.applovin.exception.ApplovinRequestException
import com.moregames.playtime.util.defaultJsonConverter
import io.ktor.client.*
import io.ktor.client.engine.mock.*
import io.ktor.client.features.json.*
import io.ktor.client.features.json.serializer.*
import io.ktor.http.*
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.time.LocalDate
import kotlin.test.assertFailsWith

class DataFetchingServiceTest {

  private val httpClient = mockHttpClient()

  private val service = DataFetchingService(
    httpClient = httpClient,
    jsonConverter = defaultJsonConverter
  )

  private companion object {
    const val apiKey = "apiKey"
    const val dayString = "2021-05-06"
    const val applicationId = "applicationId"
    const val noDataApplicationId = "noDataApplicationId"
    const val errorDataApplicationId = "errorDataApplicationId"
    const val wrongDataApplicationId = "wrongDataApplicationId"
    const val unparsableDataApplicationId = "unparsableDataApplicationId"
    const val errorResponseBody = "unhandled exception"
    const val errorHeaderContent = "error header"
  }

  @Test
  fun `SHOULD load applovin revenue ON fetchApplovinRevenues`() {
    val actual = runBlocking {
      service.fetchApplovinRevenues(
        ApplovinRequest(
          applicationId = applicationId,
          apiKey = apiKey
        ),
        LocalDate.parse(dayString)
      )
    }
    assertThat(actual).isEqualTo(
      listOf(
        ApplovinRevenue(
          adUnitId = "da39a3ee5e6b4b0",
          googleAdId = "4F2A07BC-315B-11E9-B210-D663BD873D93",
          BigDecimal("5.000025")
        ),
        ApplovinRevenue(
          adUnitId = "da39a3ee5e6b4b0",
          googleAdId = "4F2A0A6E-315B-11E9-B210-AD023491FF20",
          BigDecimal("0.006100")
        )
      )
    )
  }

  @Test
  fun `SHOULD load detailed applovin revenue ON fetchDetailedApplovinRevenues`() {
    val actual = runBlocking {
      service.fetchDetailedApplovinRevenues(
        ApplovinRequest(
          applicationId = applicationId,
          apiKey = apiKey
        ),
        LocalDate.parse(dayString)
      )
    }

    assertThat(actual!!.map { it.toString() }).containsOnly(
      "CSVRecord [comment='null', recordNumber=1, values=[2021-04-01 15:59:12.019, af26d13383e2e817, JustPlay rewarded, Default Waterfall, REWARD, , us, PHONE, fa921cd8-a752-4b50-b904-55759659dee2, , , ADCOLONY_NETWORK, 0.00276]]",
      "CSVRecord [comment='null', recordNumber=2, values=[2021-04-01 15:59:07.553, af26d13383e2e817, JustPlay rewarded, Default Waterfall, REWARD, , us, PHONE, 6c3fa603-b48f-4087-8239-06824ef2fc48, , , APPLOVIN_NETWORK, 0.023182]]"
    )
  }

  @Test
  fun `SHOULD skip applovin revenue ON fetchApplovinRevenues WHEN http error and message starts with Data does not exist for specified date`() {
    val actual = runBlocking {
      service.fetchApplovinRevenues(
        ApplovinRequest(
          applicationId = noDataApplicationId,
          apiKey = apiKey
        ),
        LocalDate.parse(dayString)
      )
    }
    assertThat(actual).isEmpty()
  }

  @Test
  fun `SHOULD skip applovin revenue ON fetchDetailedApplovinRevenues WHEN http error and message starts with Data does not exist for specified date`() {
    val actual = runBlocking {
      service.fetchDetailedApplovinRevenues(
        ApplovinRequest(
          applicationId = noDataApplicationId,
          apiKey = apiKey
        ),
        LocalDate.parse(dayString)
      )
    }

    assertThat(actual).isNull()
  }

  @Test
  fun `SHOULD throw ApplovinRequestException ON fetchApplovinRevenues WHEN http error`() {
    assertFailsWith(ApplovinRequestException::class) {
      runBlocking {
        service.fetchApplovinRevenues(
          ApplovinRequest(
            applicationId = errorDataApplicationId,
            apiKey = apiKey
          ),
          LocalDate.parse(dayString)
        )
      }
    }.let {
      assertThat(it.internalMessage).equals("""Applovin import request for $errorDataApplicationId -> $dayString failed with "$errorResponseBody" ($errorHeaderContent)" """)
    }
  }

  @Test
  fun `SHOULD throw ApplovinRequestException ON fetchDetailedApplovinRevenues WHEN http error`() {
    assertFailsWith(ApplovinRequestException::class) {
      runBlocking {
        service.fetchDetailedApplovinRevenues(
          ApplovinRequest(
            applicationId = errorDataApplicationId,
            apiKey = apiKey
          ),
          LocalDate.parse(dayString)
        )
      }
    }.let {
      assertThat(it.internalMessage).equals("""Applovin import request for $errorDataApplicationId -> $dayString failed with "$errorResponseBody" ($errorHeaderContent)" """)
    }
  }

  @Test
  fun `SHOULD fail parsing response with ApplovingParseResponseException ON fetchApplovinRevenues WHEN correct response is not returned`() {
    assertFailsWith(ApplovingParseResponseException::class) {
      runBlocking {
        service.fetchApplovinRevenues(
          ApplovinRequest(
            applicationId = unparsableDataApplicationId,
            apiKey = apiKey
          ),
          LocalDate.parse(dayString)
        )
      }
    }
  }

  @Test
  fun `SHOULD fail parsing response with ApplovingParseResponseException ON fetchDetailedApplovinRevenues WHEN correct response is not returned`() {
    assertFailsWith(ApplovingParseResponseException::class) {
      runBlocking {
        service.fetchDetailedApplovinRevenues(
          ApplovinRequest(
            applicationId = unparsableDataApplicationId,
            apiKey = apiKey
          ),
          LocalDate.parse(dayString)
        )
      }
    }
  }

  @Test
  fun `SHOULD fail response with ApplovinDataLoadInvalidException ON fetchApplovinRevenues WHEN returned data signals error`() {
    assertFailsWith(ApplovinDataLoadInvalidException::class) {
      runBlocking {
        service.fetchApplovinRevenues(
          ApplovinRequest(
            applicationId = wrongDataApplicationId,
            apiKey = apiKey
          ),
          LocalDate.parse(dayString)
        )
      }
    }
  }

  @Test
  fun `SHOULD fail parsing response with ApplovinDataLoadInvalidException ON fetchDetailedApplovinRevenues WHEN returned data signals error`() {
    assertFailsWith(ApplovinDataLoadInvalidException::class) {
      runBlocking {
        service.fetchDetailedApplovinRevenues(
          ApplovinRequest(
            applicationId = wrongDataApplicationId,
            apiKey = apiKey
          ),
          LocalDate.parse(dayString)
        )
      }
    }
  }


  private fun mockHttpClient() = HttpClient(MockEngine) {
    expectSuccess = false
    install(JsonFeature) {
      val json = kotlinx.serialization.json.Json { ignoreUnknownKeys = true }
      serializer = KotlinxSerializer(json)
    }
    engine {
      addHandler { request ->
        when (request.url.toString()) {
          "$REPORTING_URL?api_key=$apiKey&date=$dayString&platform=android&application=$applicationId&aggregated=true" -> {
            assertThat(request.method).isEqualTo(HttpMethod.Get)
            respond(
              """{
                |"status":200,
                |"url":"${javaClass.getResource("/applovin/limitedAggregatedReport.csv")!!.toExternalForm()}",
                |"ad_revenue_report_url":"${javaClass.getResource("/applovin/aggregatedReport.csv")!!.toExternalForm()}"
                |}""".trimMargin(),
              HttpStatusCode.OK,
              headersOf("Content-Type", ContentType.Application.Json.toString())
            )
          }

          "$REPORTING_URL?api_key=$apiKey&date=$dayString&platform=android&application=$applicationId&aggregated=false" -> {
            assertThat(request.method).isEqualTo(HttpMethod.Get)
            respond(
              """{
                |"status":200,
                |"url":"${javaClass.getResource("/applovin/limitedNonAggregatedReport.csv")!!.toExternalForm()}",
                |"ad_revenue_report_url":"${javaClass.getResource("/applovin/nonAggregatedReport.csv")!!.toExternalForm()}"
                |}""".trimMargin(),
              HttpStatusCode.OK,
              headersOf("Content-Type", ContentType.Application.Json.toString())
            )
          }

          "$REPORTING_URL?api_key=$apiKey&date=$dayString&platform=android&application=$noDataApplicationId&aggregated=false",
          "$REPORTING_URL?api_key=$apiKey&date=$dayString&platform=android&application=$noDataApplicationId&aggregated=true" -> {
            assertThat(request.method).isEqualTo(HttpMethod.Get)
            respond(
              """Data does not exist for specified date 29.11.2021""".trimMargin(),
              HttpStatusCode.NotFound,
              headersOf("Content-Type", ContentType.Application.Json.toString())
            )
          }

          "$REPORTING_URL?api_key=$apiKey&date=$dayString&platform=android&application=$errorDataApplicationId&aggregated=false",
          "$REPORTING_URL?api_key=$apiKey&date=$dayString&platform=android&application=$errorDataApplicationId&aggregated=true" -> {
            assertThat(request.method).isEqualTo(HttpMethod.Get)
            respond(
              errorResponseBody,
              HttpStatusCode.BadRequest,
              headersOf(
                "Content-Type" to listOf(ContentType.Application.Json.toString()),
                "Applovin-Error" to listOf(errorHeaderContent),
              )
            )
          }

          "$REPORTING_URL?api_key=$apiKey&date=$dayString&platform=android&application=$unparsableDataApplicationId&aggregated=false",
          "$REPORTING_URL?api_key=$apiKey&date=$dayString&platform=android&application=$unparsableDataApplicationId&aggregated=true" -> {
            assertThat(request.method).isEqualTo(HttpMethod.Get)
            respond(
              "unparsable json",
              // 202 - Accepted
              HttpStatusCode.fromValue(202),
              headersOf(
                "Content-Type" to listOf(ContentType.Application.Json.toString()),
              )
            )
          }

          "$REPORTING_URL?api_key=$apiKey&date=$dayString&platform=android&application=$wrongDataApplicationId&aggregated=false",
          "$REPORTING_URL?api_key=$apiKey&date=$dayString&platform=android&application=$wrongDataApplicationId&aggregated=true" -> {
            assertThat(request.method).isEqualTo(HttpMethod.Get)
            respond(
              """{"status": 202, "url": "", "ad_revenue_report_url": ""}""",
              HttpStatusCode.OK,
              headersOf(
                "Content-Type" to listOf(ContentType.Application.Json.toString()),
              )
            )
          }

          else -> error("Unhandled ${request.url.fullPath}")
        }
      }
    }
  }
}
