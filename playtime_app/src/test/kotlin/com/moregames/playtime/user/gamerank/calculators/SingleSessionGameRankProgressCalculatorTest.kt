package com.moregames.playtime.user.gamerank.calculators

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.messaging.dto.UserChallengeProgressDto
import com.moregames.base.util.ApplicationId
import com.moregames.playtime.user.gamerank.GameRank
import com.moregames.playtime.user.gamerank.GameRankCalculator
import com.moregames.playtime.user.gamerank.GameRankConfig
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class SingleSessionGameRankProgressCalculatorTest {

  private val underTest = SingleSessionGameRankProgressCalculator()

  companion object {
    const val USER_ID = "test-user-id"

    val config = GameRankConfig(
      oneStarProgressMax = 1000,
      twoStarProgressMax = 10000,
      threeStarProgressMax = 40000,
      calculator = GameRankCalculator.SCORE_PROGRESS
    )
  }

  @Test
  fun `SHOULD calculate progress correctly with ScoreProgressDto`() {
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      score = 1500
    )

    val result = underTest.calculateProgress(progressDto, 500, null, config)

    assertThat(result.rank).isEqualTo(GameRank.ONE) // 1500 -> rank ONE (>= 1000, < 10000)
    assertThat(result.progress).isEqualTo(1500) // max(500, 1500) = 1500
    assertThat(result.achievement).isEqualTo(null)
  }

  @Test
  fun `SHOULD calculate progress correctly with ScoreCompletedProgressDto`() {
    val progressDto = UserChallengeProgressDto.ScoreCompletedProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      score = 15000,
      isHighScore = true
    )

    val result = underTest.calculateProgress(progressDto, 2000, null, config)

    assertThat(result.rank).isEqualTo(GameRank.TWO) // 15000 -> rank TWO (>= 10000, < 40000)
    assertThat(result.progress).isEqualTo(15000) // max(2000, 15000) = 15000
    assertThat(result.achievement).isEqualTo(null)
  }

  @Test
  fun `SHOULD use current progress when it is higher than new score`() {
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      score = 800
    )

    val result = underTest.calculateProgress(progressDto, 1200, null, config)

    assertThat(result.rank).isEqualTo(GameRank.ONE) // 1200 -> rank ONE (>= 1000, < 10000)
    assertThat(result.progress).isEqualTo(1200) // max(1200, 800) = 1200
    assertThat(result.achievement).isEqualTo(null)
  }

  @Test
  fun `SHOULD cap progress at progressMax`() {
    val progressDto = UserChallengeProgressDto.ScoreCompletedProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      score = 50000,
      isHighScore = true
    )

    val result = underTest.calculateProgress(progressDto, 1000, null, config)

    assertThat(result.rank).isEqualTo(GameRank.THREE) // 40000 -> rank THREE (>= 40000)
    assertThat(result.progress).isEqualTo(config.progressMax) // min(50000, 40000) = 40000
    assertThat(result.achievement).isEqualTo(null)
  }

  @ParameterizedTest
  @CsvSource(
    "500, ZERO, 500",      // 500 -> rank ZERO (< 1000)
    "999, ZERO, 999",      // 999 -> rank ZERO (< 1000)
    "1000, ONE, 1000",     // 1000 -> rank ONE (>= 1000, < 10000)
    "5000, ONE, 5000",     // 5000 -> rank ONE (>= 1000, < 10000)
    "9999, ONE, 9999",     // 9999 -> rank ONE (>= 1000, < 10000)
    "10000, TWO, 10000",   // 10000 -> rank TWO (>= 10000, < 40000)
    "25000, TWO, 25000",   // 25000 -> rank TWO (>= 10000, < 40000)
    "39999, TWO, 39999",   // 39999 -> rank TWO (>= 10000, < 40000)
    "40000, THREE, 40000", // 40000 -> rank THREE (>= 40000)
    "50000, THREE, 40000"  // 50000 -> rank THREE (>= 40000) but capped at progressMax
  )
  fun `SHOULD return correct rank based on score`(
    score: Int,
    expectedRank: GameRank,
    expectedProgress: Int
  ) {
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      score = score
    )

    val result = underTest.calculateProgress(progressDto, 0, null, config)

    assertThat(result.rank).isEqualTo(expectedRank)
    assertThat(result.progress).isEqualTo(expectedProgress)
  }

  @Test
  fun `SHOULD return current state for AmountMilestoneProgressDto`() {
    val progressDto = UserChallengeProgressDto.AmountMilestoneProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      milestone = 10,
      amount = 5
    )

    val result = underTest.calculateProgress(progressDto, 1500, null, config)

    assertThat(result.rank).isEqualTo(GameRank.ONE) // 1500 -> rank ONE (>= 1000, < 10000)
    assertThat(result.progress).isEqualTo(1500) // Current progress unchanged
    assertThat(result.achievement).isEqualTo(null)
  }

  @Test
  fun `SHOULD return current state for LevelIdProgressDto`() {
    val progressDto = UserChallengeProgressDto.LevelIdProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      levelId = "level_25"
    )

    val result = underTest.calculateProgress(progressDto, 2500, null, config)

    assertThat(result.rank).isEqualTo(GameRank.ONE) // 2500 -> rank ONE (>= 1000, < 10000)
    assertThat(result.progress).isEqualTo(2500) // Current progress unchanged
    assertThat(result.achievement).isEqualTo(null)
  }

  @Test
  fun `SHOULD return current state for MilestoneProgressDto`() {
    val progressDto = UserChallengeProgressDto.MilestoneProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      milestone = 50
    )

    val result = underTest.calculateProgress(progressDto, 12000, null, config)

    assertThat(result.rank).isEqualTo(GameRank.TWO) // 12000 -> rank TWO (>= 10000, < 40000)
    assertThat(result.progress).isEqualTo(12000) // Current progress unchanged
    assertThat(result.achievement).isEqualTo(null)
  }

  @Test
  fun `SHOULD return current state for TmProgressDto`() {
    val progressDto = UserChallengeProgressDto.TmProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      score = 1000,
      isBoss = false
    )

    val result = underTest.calculateProgress(progressDto, 800, null, config)

    assertThat(result.rank).isEqualTo(GameRank.ZERO) // 800 -> rank ZERO (< 1000)
    assertThat(result.progress).isEqualTo(800) // Current progress unchanged
    assertThat(result.achievement).isEqualTo(null)
  }

  @Test
  fun `SHOULD handle rank transition from ZERO to ONE`() {
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      score = 1000
    )

    val result = underTest.calculateProgress(progressDto, 999, null, config)

    assertThat(result.rank).isEqualTo(GameRank.ONE) // 1000 -> rank ONE (>= 1000, < 10000)
    assertThat(result.progress).isEqualTo(1000) // max(999, 1000) = 1000
    assertThat(result.achievement).isEqualTo(null)
  }

  @Test
  fun `SHOULD handle rank transition from ONE to TWO`() {
    val progressDto = UserChallengeProgressDto.ScoreCompletedProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      score = 10000,
      isHighScore = true
    )

    val result = underTest.calculateProgress(progressDto, 9999, null, config)

    assertThat(result.rank).isEqualTo(GameRank.TWO) // 10000 -> rank TWO (>= 10000, < 40000)
    assertThat(result.progress).isEqualTo(10000) // max(9999, 10000) = 10000
    assertThat(result.achievement).isEqualTo(null)
  }

  @Test
  fun `SHOULD handle rank transition from TWO to THREE`() {
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      score = 40000
    )

    val result = underTest.calculateProgress(progressDto, 39999, null, config)

    assertThat(result.rank).isEqualTo(GameRank.THREE) // 40000 -> rank THREE (>= 40000)
    assertThat(result.progress).isEqualTo(40000) // max(39999, 40000) = 40000
    assertThat(result.achievement).isEqualTo(null)
  }

  @Test
  fun `SHOULD handle zero score correctly`() {
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      score = 0
    )

    val result = underTest.calculateProgress(progressDto, 500, null, config)

    assertThat(result.rank).isEqualTo(GameRank.ZERO) // 500 -> rank ZERO (< 1000)
    assertThat(result.progress).isEqualTo(500) // max(500, 0) = 500
    assertThat(result.achievement).isEqualTo(null)
  }

  @Test
  fun `SHOULD handle negative score correctly`() {
    val progressDto = UserChallengeProgressDto.ScoreCompletedProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      score = -100,
      isHighScore = false
    )

    val result = underTest.calculateProgress(progressDto, 1500, null, config)

    assertThat(result.rank).isEqualTo(GameRank.ONE) // 1500 -> rank ONE (>= 1000, < 10000)
    assertThat(result.progress).isEqualTo(1500) // max(1500, -100) = 1500
    assertThat(result.achievement).isEqualTo(null)
  }

  @Test
  fun `SHOULD handle zero current progress correctly`() {
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      score = 2500
    )

    val result = underTest.calculateProgress(progressDto, 0, null, config)

    assertThat(result.rank).isEqualTo(GameRank.ONE) // 2500 -> rank ONE (>= 1000, < 10000)
    assertThat(result.progress).isEqualTo(2500) // max(0, 2500) = 2500
    assertThat(result.achievement).isEqualTo(null)
  }
}