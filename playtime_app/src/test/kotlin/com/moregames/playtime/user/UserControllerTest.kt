package com.moregames.playtime.user

import assertk.assertThat
import assertk.assertions.contains
import assertk.assertions.containsExactly
import assertk.assertions.isEqualTo
import assertk.assertions.isNull
import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService.UserCurrencyEarnings
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.BaseVariation
import com.moregames.base.abtesting.ClientExperiment.*
import com.moregames.base.abtesting.DEFAULT
import com.moregames.base.abtesting.Variations
import com.moregames.base.abtesting.Variations.ANDROID_GAME_STORIES_PRE_GAME
import com.moregames.base.abtesting.variations.AndroidCashStreakVariation.CashStreakOn
import com.moregames.base.abtesting.variations.AndroidDemoGamesVariation.InstallationMessageAfter
import com.moregames.base.abtesting.variations.AndroidDemoGamesVariation.InstallationMessageBefore
import com.moregames.base.abtesting.variations.AndroidOnboardingProgressBarVariation
import com.moregames.base.app.BuildVariant
import com.moregames.base.coins.UserCurrentCoinsGoalBalance
import com.moregames.base.config.ApplicationConfig
import com.moregames.base.dto.AppPlatform.ANDROID
import com.moregames.base.dto.AppPlatform.IOS
import com.moregames.base.dto.AppVersionDto
import com.moregames.base.dto.TrackingDataType.IDFV
import com.moregames.base.junit.MockExtension
import com.moregames.base.junit.TypedVariationSource
import com.moregames.base.messaging.customnotification.ButtonAction
import com.moregames.base.messaging.customnotification.ButtonActionName
import com.moregames.base.user.UserBonusBalanceType
import com.moregames.base.user.dto.PrivacyRegulationType
import com.moregames.base.util.ClientVersionsSupport.getDesiredAppVersion
import com.moregames.base.util.ClientVersionsSupport.getUserCreationMinAppVersion
import com.moregames.base.util.Constants.ANDROID_APP_VERSION_HEADER
import com.moregames.base.util.Constants.COUNTRY_HEADER
import com.moregames.base.util.Constants.IOS_APP_VERSION_HEADER
import com.moregames.base.util.Constants.IP_HEADER
import com.moregames.base.util.Constants.MARKET_HEADER
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.administration.qa.QaUserSettingsService
import com.moregames.playtime.boost.BoostedModeService
import com.moregames.playtime.boost.BoostedModeTranslationService
import com.moregames.playtime.buseffects.AmplitudeEventEffectHandler
import com.moregames.playtime.cashstreak.CashStreakController
import com.moregames.playtime.checks.ExaminationController
import com.moregames.playtime.checks.ExaminationService
import com.moregames.playtime.general.MarketService
import com.moregames.playtime.general.TrackedEventsService
import com.moregames.playtime.general.dto.TrackedEvent
import com.moregames.playtime.ios.cashoutcoins.CashoutCoinsApiDto
import com.moregames.playtime.ios.cashoutcoins.CashoutCoinsMode
import com.moregames.playtime.ios.cashoutcoins.CashoutCoinsService
import com.moregames.playtime.notifications.status.UserNotificationStatusService
import com.moregames.playtime.notifications.status.UserNotificationsStatusApiDto
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.translations.AndroidCoinsRenamingExpService
import com.moregames.playtime.translations.TranslationResource
import com.moregames.playtime.translations.TranslationResource.*
import com.moregames.playtime.translations.UserTranslationService
import com.moregames.playtime.user.UserController.Companion.DESIRED_APP_VERSION_HEADER
import com.moregames.playtime.user.UserController.Companion.POPUP_MESSAGE_HEADER
import com.moregames.playtime.user.UserController.Companion.USER_ID_PARAMETER
import com.moregames.playtime.user.cashout.*
import com.moregames.playtime.user.cashout.dto.AndroidCashoutProgressBarMode
import com.moregames.playtime.user.cashout.dto.AndroidFaceScanPreScreenParams
import com.moregames.playtime.user.cashout.dto.AndroidFaceScanPreScreenType
import com.moregames.playtime.user.cashout.dto.CashoutPeriodDto
import com.moregames.playtime.user.cashout2xoffer.Cashout2xOfferService
import com.moregames.playtime.user.challenge.ChallengeEventController
import com.moregames.playtime.user.coingoal.CoinGoalVariationsExpService
import com.moregames.playtime.user.dto.*
import com.moregames.playtime.user.dto.AndroidCashStreakModeApiDto.CASH_STREAK_ON
import com.moregames.playtime.user.dto.DemoGameLaunchModeApiDto.INSTALLATION_MESSAGE_AFTER_GAME_COMPLETION
import com.moregames.playtime.user.dto.DemoGameLaunchModeApiDto.INSTALLATION_MESSAGE_BEFORE_GAME_START
import com.moregames.playtime.user.dto.UserApiDto.MilestoneDesc
import com.moregames.playtime.user.dto.threedotmenu.ThreeDotMenuActionApiDto
import com.moregames.playtime.user.dto.threedotmenu.ThreeDotMenuActionName.OPT_OUT
import com.moregames.playtime.user.dto.threedotmenu.ThreeDotMenuItem
import com.moregames.playtime.user.gamestories.AndroidGameStoriesModeApiDto.PRE_GAME
import com.moregames.playtime.user.gamestories.AndroidGameStoriesService
import com.moregames.playtime.user.highlightedgames.AndroidHighlightedGamesService
import com.moregames.playtime.user.offer.AndroidOffersController
import com.moregames.playtime.user.offer.WelcomeCoinsService
import com.moregames.playtime.user.onboarding.progressbar.OnboardingProgressBarService
import com.moregames.playtime.user.onboarding.progressbar.OnboardingProgressBarStep
import com.moregames.playtime.user.onboarding.progressbar.OnboardingProgressBarStepStatus
import com.moregames.playtime.user.onboarding.progressbar.OnboardingProgressBarStepType.*
import com.moregames.playtime.user.promotion.event.manager.PromotionEventService
import com.moregames.playtime.user.promotion.event.manager.api.client.*
import com.moregames.playtime.user.promotion.event.manager.api.client.infobar.InfoBarApiDto
import com.moregames.playtime.user.promotion.event.manager.api.client.infobar.InfoBarContentApiDto
import com.moregames.playtime.user.promotion.event.manager.api.client.infobar.InfoBarContentParametersApiDto
import com.moregames.playtime.user.promotion.event.manager.api.client.infobar.InfoBarDynamicValueConfigurationApiDto
import com.moregames.playtime.user.promotion.event.manager.dto.AnnouncementDetailsDto
import com.moregames.playtime.user.survey.SurveyController
import com.moregames.playtime.user.survey.paymentprovider.PaymentProviderSurveyService
import com.moregames.playtime.user.toprunningbar.TopRunningBarDto
import com.moregames.playtime.user.toprunningbar.TopRunningBarService
import com.moregames.playtime.user.tracking.TrackingData
import com.moregames.playtime.user.tutorial.TutorialService
import com.moregames.playtime.user.tutorial.TutorialSteps
import com.moregames.playtime.user.usergame.UserGameService
import com.moregames.playtime.user.verification.VerificationController
import com.moregames.playtime.util.base64Encoded
import com.moregames.playtime.util.installDefaultContentNegotiation
import com.moregames.playtime.util.plus
import com.moregames.playtime.utils.*
import com.moregames.playtime.utils.Json.defaultJsonConverter
import io.ktor.application.*
import io.ktor.http.*
import io.ktor.routing.*
import io.ktor.server.testing.*
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.encodeToString
import net.javacrumbs.jsonunit.JsonAssert.assertJsonEquals
import net.javacrumbs.jsonunit.JsonAssert.whenIgnoringPaths
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.api.extension.ExtensionContext
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.*
import org.mockito.ArgumentMatchers.anyBoolean
import org.mockito.kotlin.*
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.*
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.time.Duration.Companion.hours

@ExperimentalSerializationApi
@ExperimentalCoroutinesApi
@ExtendWith(MockExtension::class)
class UserControllerTest(
  private val userService: UserService,
  private val cashoutService: CashoutService,
  private val cashoutPeriodsService: CashoutPeriodsService,
  private val cashoutStatusService: CashoutStatusService,
  private val abTestingService: AbTestingService,
  private val marketService: MarketService,
  private val trackedEventsService: TrackedEventsService,
  private val userPopupMessagesService: UserPopupMessagesService,
  private val timeService: TimeService,
  private val cashoutController: CashoutController,
  private val examinationController: ExaminationController,
  private val notificationsController: NotificationsController,
  private val androidOffersController: AndroidOffersController,
  private val paymentsController: PaymentsController,
  private val verificationController: VerificationController,
  private val surveyController: SurveyController,
  private val topRunningBarService: TopRunningBarService,
  private val qaUserSettingsService: QaUserSettingsService,
  private val tutorialService: TutorialService,
  private val translationService: UserTranslationService,
  private val examinationService: ExaminationService,
  private val buildVariant: BuildVariant,
  private val userNotificationStatusService: UserNotificationStatusService,
  private val applicationConfig: ApplicationConfig,
  private val androidOnlineUsersService: AndroidOnlineUsersService,
  private val userGameService: UserGameService,
  private val paymentProviderSurveyService: PaymentProviderSurveyService,
  private val cashoutCoinsService: CashoutCoinsService,
  private val faceScanPreScreenService: FaceScanPreScreenService,
  private val paymentProvidersService: PaymentProvidersService,
  private val amplitudeEventEffectHandler: AmplitudeEventEffectHandler,
  private val hideCashoutAmountExperimentService: HideCashoutAmountExperimentService,
  private val promotionEventService: PromotionEventService,
  private val challengeEventController: ChallengeEventController,
  private val cashout2xOfferService: Cashout2xOfferService,
  private val androidCoinsRenamingExpService: AndroidCoinsRenamingExpService,
  private val onboardingProgressBarService: OnboardingProgressBarService,
  private val coinGoalVariationsExpService: CoinGoalVariationsExpService,
  private val androidHighlightedGamesService: AndroidHighlightedGamesService,
  private val androidAnimationToCelebrateEarningsService: AndroidAnimationToCelebrateEarningsService,
  private val cashStreakController: CashStreakController,
  private val rewardingFacade: RewardingFacade,
  private val androidGameStoriesService: AndroidGameStoriesService,
  private val welcomeCoinsService: WelcomeCoinsService,
  private val boostedModeService: BoostedModeService,
  private val boostedModeTranslationService: BoostedModeTranslationService,
) {

  private fun controller(): Application.() -> Unit = {
    install(IgnoreTrailingSlash)
    installDefaultContentNegotiation()
    routing {
      UserController(
        userService = userService,
        cashoutService = cashoutService,
        rewardingFacade = rewardingFacade,
        cashoutPeriodsService = cashoutPeriodsService,
        cashoutStatusService = cashoutStatusService,
        abTestingService = abTestingService,
        trackedEventsService = trackedEventsService,
        userPopupMessagesService = userPopupMessagesService,
        marketService = marketService,
        timeService = timeService,
        cashoutController = cashoutController,
        examinationController = examinationController,
        notificationsController = notificationsController,
        androidOffersController = androidOffersController,
        paymentsController = paymentsController,
        verificationController = verificationController,
        surveyController = surveyController,
        topRunningBarService = topRunningBarService,
        userNotificationStatusService = userNotificationStatusService,
        qaUserSettingsService = qaUserSettingsService,
        tutorialService = tutorialService,
        translationService = translationService,
        examinationService = examinationService,
        buildVariantProvider = { buildVariant },
        applicationConfig = applicationConfig,
        androidOnlineUsersService = androidOnlineUsersService,
        userGameService = userGameService,
        paymentProviderSurveyService = paymentProviderSurveyService,
        cashoutCoinsService = cashoutCoinsService,
        faceScanPreScreenService = faceScanPreScreenService,
        paymentProvidersService = paymentProvidersService,
        amplitudeEventEffectHandler = amplitudeEventEffectHandler,
        hideCashoutAmountExperimentService = hideCashoutAmountExperimentService,
        promotionEventService = promotionEventService,
        challengeEventController = challengeEventController,
        cashout2xOfferService = cashout2xOfferService,
        androidCoinsRenamingExpService = androidCoinsRenamingExpService,
        onboardingProgressBarService = onboardingProgressBarService,
        coinGoalVariationsExpService = coinGoalVariationsExpService,
        androidHighlightedGamesService = androidHighlightedGamesService,
        androidAnimationToCelebrateEarningsService = androidAnimationToCelebrateEarningsService,
        cashStreakController = cashStreakController,
        androidGameStoriesService = androidGameStoriesService,
        welcomeCoinsService = welcomeCoinsService,
        boostedModeService = boostedModeService,
        boostedModeTranslationService = boostedModeTranslationService,
      ).startRouting(this)
    }
  }

  private val cashoutPeriod: CashoutPeriodDto = mock()

  private companion object {
    const val USER_ID = "userId"
    const val COUNTRY_CODE = "US"
    const val IP = "***********"
    const val SESSION_ID = "sessionId"
    val appVersion = AppVersionDto(ANDROID, getUserCreationMinAppVersion(ANDROID))
    val now: Instant = Instant.now()
  }

  @BeforeEach
  fun before() {
    applicationConfig.mock({ justplayMarket }, "test-market")
    trackedEventsService.mock({ prepareHeadersToSend(USER_ID) }, emptyMap())
    userService.mock({ loadCoinGoalUser(USER_ID) }, user.copy(createdAt = now.minus(1, ChronoUnit.DAYS)))
    abTestingService.mock({ assignedVariationValue(eq(USER_ID), any(), any()) }, DEFAULT)
    abTestingService.mock({ isUserExperimentParticipant(eq(USER_ID), any()) }, false)
    abTestingService.mock({ getVideoOfferCoolDownSeconds(USER_ID) }, null)
    abTestingService.mock({ shouldShowTimerInCoinGoal(USER_ID) }, false)
    abTestingService.mock({ isEm3Participant(USER_ID) }, false)
    rewardingFacade.mock({ inflatingCoinsMultiplier(USER_ID) }, 2000)
    hideCashoutAmountExperimentService.mock({ shouldShowGiftBox(any(), any(), any()) }, false)
    userPopupMessagesService.mock({ getMessageForUser(any(), any(), anyBoolean()) }, "")
    cashoutService.mock(
      { getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) },
      UserCurrencyEarnings(BigDecimal.ZERO, Currency.getInstance("USD"), BigDecimal.ZERO)
    )
    cashoutService.mock({ userHasCashouts(USER_ID) }, false)
    marketService.mock({ getUserAllowedCountryCodeOrUS(USER_ID) }, "US")
    marketService.mock({ isUserFromAllowedCountry(USER_ID) }, true)
    marketService.mock({ isGdprAppliesToCountry(any()) }, false)
    marketService.mock({ getPrivacyRegulation(any()) }, null)
    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriod)
    cashoutStatusService.mock({ isCashoutEnabled(USER_ID) }, true)
    timeService.mock({ now() }, now)
    rewardingFacade.mock({ addBonusCoinsIfNotExists(USER_ID, user.appPlatform, 50, UserBonusBalanceType.WELCOME_COINS) }, true)
    qaUserSettingsService.mock({ shouldSkipOnboarding(USER_ID) }, false)
    tutorialService.mock({ getTutorialSteps(any()) }, emptyList())
    tutorialService.mock({ getAdditionalTranslations(any()) }, emptyMap())
    whenever(runTest { translationService.translateOrDefault(any(), any(), any()) }).then { (it.getArgument(0) as TranslationResource).defaultValue }
    amplitudeEventEffectHandler.mock({ shouldUseAmplitudeAnalytics(USER_ID) }, false)
    examinationService.mock({ wasSuccessfullyExamined(USER_ID) }, true)
    userService.mock({ useAndroidLazyApplovinInitialization(USER_ID) }, false)
    androidCoinsRenamingExpService.mock({ getReplacements(USER_ID) }, emptyMap())
    coinGoalVariationsExpService.mock({ getReachCoinGoalReplacements(USER_ID) }, emptyMap())
    whenever(cashoutPeriod.id).thenReturn("userId:3")
    rewardingFacade.mock({ getUserCurrentCoinsBalance(user.userId, user.appPlatform) }, UserCurrentCoinsGoalBalance(0, 0, 0))
    abTestingService.mock({ isEm2Participant(USER_ID) }, false)
  }

  @Test
  fun `SHOULD return PromotionConfigApiDto ON promotion-event-conf WHEN there is promotion event`() = withTestApplication(controller()) {
    promotionEventService.mock(
      { getPromotionEventConfig(USER_ID, EN_US_LOCALE) },
      PromotionEventConfigApiDto(
        id = "TEST_PROMO",
        startTime = 157600000,
        endTime = 157770000,
        timestamp = 154400000,
        top = TopConfigApiDto(
          backgroundImage = "Christmas_back.jpg",
          foregroundImage = "Christmas_fore.png",
          gradientTop = "#FF0000",
          gradientBottom = "FF0033",
          cashoutButtonColor = "#FF0057",
          durationInMillis = 3000
        ),
        mainTop = listOf(
          TopConfigApiDto(
            backgroundImage = "Christmas_back.jpg",
            foregroundImage = "Christmas_fore.png",
            gradientTop = "#FF0000",
            gradientBottom = "FF0033",
            cashoutButtonColor = "#FF0057",
            durationInMillis = 3000,
            order = 1
          )
        ),
        challengesTop = listOf(
          TopConfigApiDto(
            backgroundImage = "Christmas_back.jpg",
            foregroundImage = "Christmas_fore.png",
            gradientTop = "#FF0000",
            gradientBottom = "FF0033",
            cashoutButtonColor = "#FF0057",
            durationInMillis = 3000,
            order = 1
          )
        ),
        offerModifier = listOf(
          OfferModifierConfigApiDto(
            offerId = "2000089",
            offerImage = "treasure_master_override.jpg",
            badge = BadgeConfigApiDto(
              color = "#FFFF00",
              displaySpecialBadge = true,
              text = "<strikethrough>${'$'}5 daily</strikethrough><br><font color=\"0x00FF00\">Now!</font><br>"
            )
          )
        ),
        translations = mapOf(
          "earn_playing_games" to "Begrenztes Angebot!",
          "notificationBalanceUpdateText" to "Sie haben jetzt %s erhöhte Münzen",
          "notificationBalanceUpdateTitle" to "Erhöhte Münzen!",
          "balance_title" to "Erhöhte Münzen:"
        ),
        countDownBanners = CountDownBannersApiDto(
          startPosition = 1,
          step = 3,
          max = 2,
          title = "Banner title",
          backgroundImage = "background_image.jpg",
          infoImages = listOf("image1.jgp", "image2.jpg"),
          infoTitle = "Info title",
          infoSections = listOf(
            CountDownInfoSectionApiDto(
              subTitle = "SubTitle",
              subText = "SubText",
            )
          ),
          infoButtonClickAction = ButtonAction(
            name = ButtonActionName.OPEN_FIRST_FOUND_OFFERWALL,
            parameters = listOf("parameter1", "parameter2")
          ),
          infoButtonText = "Info button text",
          endTime = 157770000
        ),
        infoBar = InfoBarApiDto(
          content = listOf(
            InfoBarContentApiDto(
              text = "PLAYERS_ONLINE"
            ),
            InfoBarContentApiDto(
              text = "Text with {DYNAMIC_VALUE}$ amount in it!",
              parameters = InfoBarContentParametersApiDto(
                showDuringSec = 5,
                sliding = true,
                dynamicValueConfiguration = InfoBarDynamicValueConfigurationApiDto(
                  baseValue = 1_000_000,
                  updateEachSec = 5,
                  updateValueBy = -200,
                  randomnessPercentage = 5
                )
              )
            )
          )
        ),
        expectedAppVersion = 75,
        announcementDetails = AnnouncementDetailsDto(
          title = "t",
          description = "d",
          image = "i",
          buttonText = "Jetzt aktualisieren",
          themeColor = "tc" ,
          buttonClickAction = ButtonAction(ButtonActionName.ROUTE_TO_MAIN))
      )
    )
    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID/promotion-event-configuration"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    val expectedJson = javaClass.getResource("/user/user-controller/promotionConfig.json")!!.readText()
    assertJsonEquals(expectedJson, response.response.content)
  }

  @Test
  fun `SHOULD return empty json ON promotion-event-conf WHEN no promotion event`() = withTestApplication(controller()) {
    promotionEventService.mock({ getPromotionEventConfig(USER_ID, EN_US_LOCALE) }, PromotionEventConfigApiDto.empty())
    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID/promotion-event-configuration"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertJsonEquals(
      response.response.content,
      //language=json
      """
      { 
        "translations": {}
      }
    """
    )
  }

  @Test
  fun `SHOULD return translations with replacements ON get user data call WHEN we have some from experimental service`() = withTestApplication(controller()) {
    androidCoinsRenamingExpService.mock(
      { getReplacements(USER_ID) },
      mapOf("to_earn_coins" to "To earn tickets", "\$_2048_puzzle_fun_description" to "Reach 2048 to get more nails")
    )

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    defaultJsonConverter.decodeFromString<UserApiDto>(response.response.content!!)
      .let {
        assertThat(it.expLabels).isEqualTo(
          mapOf("to_earn_coins" to "To earn tickets")
        )
      }
    verifyBlocking(androidCoinsRenamingExpService) { getReplacements(USER_ID) }
  }

  @Test
  fun `SHOULD return translations with coin goal replacements ON get user data call WHEN we have some from experimental service`() =
    withTestApplication(controller()) {
      coinGoalVariationsExpService.mock(
        { getReachCoinGoalReplacements(USER_ID) },
        mapOf("mainCoinGoalLabel" to "Keep Going! New Goal for Higher Payouts!")
      )

      val response = handleRequest(
        method = HttpMethod.Get,
        uri = "/users/$USER_ID"
      ) {
        addHeaders()
      }

      assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
      defaultJsonConverter.decodeFromString<UserApiDto>(response.response.content!!)
        .let {
          assertThat(it.expLabels).isEqualTo(
            mapOf("mainCoinGoalLabel" to "Keep Going! New Goal for Higher Payouts!")
          )
        }
      verifyBlocking(coinGoalVariationsExpService) { getReachCoinGoalReplacements(USER_ID) }
    }

  @ParameterizedTest
  @CsvSource(
    "LONG_AGGRESSIVE,Hit Goal by Countdown for Guaranteed High Payouts!",
    "SHORTER_AGGRESSIVE,Hit Goal for Guaranteed High Payouts!",
    "SHORTER,Hit goal before timer ends",
    "SHORT_AGGRESSIVE,Guaranteed High Payouts!"
  )
  fun `SHOULD return goal text labels ON get user data call WHEN ANDROID_COIN_GOAL_TEXT participant`(variation: Variations, result: String) =
    withTestApplication(controller()) {
      abTestingService.mock({ assignedVariationValue(USER_ID, ANDROID_COIN_GOAL_TEXT) }, variation)

      val response = handleRequest(
        method = HttpMethod.Get,
        uri = "/users/$USER_ID"
      ) {
        addHeaders()
      }

      assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
      defaultJsonConverter.decodeFromString<UserApiDto>(response.response.content!!)
        .let {
          assertThat(it.expLabels).isEqualTo(mapOf("mainCoinGoalLabel" to result))
        }
    }

  @ParameterizedTest
  @CsvSource(
    "LOYALTY_NORMAL,Loyalty Program Games:",
    "LOYALTY_FIRE,\uD83D\uDD25 Loyalty Program Games \uD83D\uDD25",
    "LOYALTY_DOLLARS,\$\$ Loyalty Program Games \$\$",
    "NO_TEXT,''"
  )
  fun `SHOULD return earn playing games text labels ON get user data call WHEN ANDROID_EARN_PLAYING_GAMES_TEXT participant`(
    variation: Variations,
    result: String
  ) =
    withTestApplication(controller()) {
      abTestingService.mock({ assignedVariationValue(USER_ID, ANDROID_EARN_PLAYING_GAMES_TEXT) }, variation)

      val response = handleRequest(
        method = HttpMethod.Get,
        uri = "/users/$USER_ID"
      ) {
        addHeaders()
      }

      assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
      defaultJsonConverter.decodeFromString<UserApiDto>(response.response.content!!)
        .let {
          assertThat(it.expLabels).isEqualTo(mapOf("earn_playing_games" to result))
        }
    }

  @Test
  fun `SHOULD return em3 text labels ON get user data call WHEN em3 participant`() =
    withTestApplication(controller()) {
      abTestingService.mock({ isEm3Participant(USER_ID) }, true)

      val response = handleRequest(
        method = HttpMethod.Get,
        uri = "/users/$USER_ID"
      ) {
        addHeaders()
      }

      assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
      defaultJsonConverter.decodeFromString<UserApiDto>(response.response.content!!)
        .let {
          assertThat(it.expLabels).isEqualTo(
            mapOf(
              "mainCoinGoalLabel" to "Play games to earn coins, coin total updated every 5 minutes",
              "mainCoinGoalReachedLabel" to "Play games to earn coins, coin total updated every 5 minutes",
            )
          )
        }
    }

  @Test
  fun `SHOULD enable nested controllers ON controller creation`() = withTestApplication(controller()) {
    // users context: /users/...

    val examinationControllerCapture = argumentCaptor<Route>()
    verify(examinationController).startRouting(examinationControllerCapture.capture())
    var examinationRoute: Route? = examinationControllerCapture.firstValue
    listOf("<slash>", "users", "").forEach { path ->
      assertThat(examinationRoute?.selector?.toString()).isEqualTo(path)
      examinationRoute = examinationRoute?.parent
    }
    assertThat(examinationRoute).isNull()

    // UserId context: /users/${userId}/...
    val cashoutControllerRouteCapture = argumentCaptor<Route>()
    val notificationControllerRouteCapture = argumentCaptor<Route>()
    val offersControllerRouteCapture = argumentCaptor<Route>()
    val paymentsControllerRouteCapture = argumentCaptor<Route>()
    val verificationControllerCapture = argumentCaptor<Route>()
    val surveyControllerCapture = argumentCaptor<Route>()
    val challengeEventControllerCapture = argumentCaptor<Route>()
    val cashStreakControllerCapture = argumentCaptor<Route>()
    verify(cashoutController).startRouting(cashoutControllerRouteCapture.capture())
    verify(notificationsController).startRouting(notificationControllerRouteCapture.capture())
    verify(androidOffersController).startRouting(offersControllerRouteCapture.capture())
    verify(paymentsController).startRouting(paymentsControllerRouteCapture.capture())
    verify(verificationController).startRouting(verificationControllerCapture.capture())
    verify(surveyController).startRouting(surveyControllerCapture.capture())
    verify(challengeEventController).startRouting(challengeEventControllerCapture.capture())
    verify(cashStreakController).startRouting(cashStreakControllerCapture.capture())

    var cashoutRoute: Route? = cashoutControllerRouteCapture.firstValue
    var notificationsRoute: Route? = notificationControllerRouteCapture.firstValue
    var offersRoute: Route? = offersControllerRouteCapture.firstValue
    var paymentsRoute: Route? = paymentsControllerRouteCapture.firstValue
    var verificationRoute: Route? = verificationControllerCapture.firstValue
    var surveyRoute: Route? = surveyControllerCapture.firstValue
    var challengeRoute: Route? = challengeEventControllerCapture.firstValue
    var cashStreakRoute: Route? = challengeEventControllerCapture.firstValue
    listOf("{$USER_ID_PARAMETER}", "<slash>", "users", "").forEach { path ->

      assertThat(cashoutRoute?.selector?.toString()).isEqualTo(path)
      assertThat(notificationsRoute?.selector?.toString()).isEqualTo(path)
      assertThat(offersRoute?.selector?.toString()).isEqualTo(path)
      assertThat(paymentsRoute?.selector?.toString()).isEqualTo(path)
      assertThat(verificationRoute?.selector?.toString()).isEqualTo(path)
      assertThat(surveyRoute?.selector?.toString()).isEqualTo(path)
      assertThat(challengeRoute?.selector?.toString()).isEqualTo(path)
      assertThat(cashStreakRoute?.selector?.toString()).isEqualTo(path)

      cashoutRoute = cashoutRoute?.parent
      notificationsRoute = notificationsRoute?.parent
      offersRoute = offersRoute?.parent
      paymentsRoute = paymentsRoute?.parent
      verificationRoute = verificationRoute?.parent
      surveyRoute = surveyRoute?.parent
      challengeRoute = challengeRoute?.parent
      cashStreakRoute = cashStreakRoute?.parent
    }
    assertThat(cashoutRoute).isNull()
    assertThat(notificationsRoute).isNull()
    assertThat(offersRoute).isNull()
    assertThat(paymentsRoute).isNull()
    assertThat(verificationRoute).isNull()
    assertThat(surveyRoute).isNull()
    assertThat(challengeRoute).isNull()
    assertThat(cashStreakRoute).isNull()
  }

  @Test
  fun `SHOULD update consent data ON update user consent endpoint call`() = withTestApplication(controller()) {
    val actual = handleRequest(
      method = HttpMethod.Post,
      uri = "/users/$USER_ID/consent"
    ) {
      addHeaders()
      setBody(
        defaultJsonConverter.encodeToString(
          ConsentApiDto(hasConsentedToAnalytics = true, hasConsentedToTargetedAdvertisement = true, librariesConsent = mapOf("lib 1" to true))
        )
      )
    }

    assertThat(actual.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(userService) {
      updateUserConsent(
        USER_ID,
        ConsentApiDto(hasConsentedToAnalytics = true, hasConsentedToTargetedAdvertisement = true, librariesConsent = mapOf("lib 1" to true))
      )
    }
    verifyBlocking(userService) { onUserSpecificRequest(eq(USER_ID), any(), eq(appVersion), eq(null), eq(EN_US_LOCALE), eq(null)) }
  }

  @Test
  fun `SHOULD update consent data ON update user consent endpoint call WHEN libraries consent field not passed`() = withTestApplication(controller()) {
    val actual = handleRequest(
      method = HttpMethod.Post,
      uri = "/users/$USER_ID/consent"
    ) {
      addHeaders()
      setBody(javaClass.getResource("/user/user-controller/consent_no_libraries.json")!!.readText())
    }

    assertThat(actual.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(userService) {
      updateUserConsent(
        USER_ID,
        ConsentApiDto(hasConsentedToAnalytics = true, hasConsentedToTargetedAdvertisement = true, librariesConsent = null)
      )
    }
  }

  @Test
  fun `SHOULD request user deletion ON delete endpoint call`() = withTestApplication(controller()) {
    val actual = handleRequest(
      method = HttpMethod.Post,
      uri = "/users/$USER_ID/delete"
    ) {
      addHeaders()
    }

    assertThat(actual.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(userService) { requestUserDeletion(USER_ID) }
  }

  @Test
  fun `SHOULD store video reward ON videoAdReward endpoint call`() = withTestApplication(controller()) {
    val actual = handleRequest(
      method = HttpMethod.Post,
      uri = "/users/$USER_ID/videoAdReward"
    ) {
      addHeaders()
      setBody(defaultJsonConverter.encodeToString(VideoAdReward(revenue = 0.123456789123456)))
    }

    assertThat(actual.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(userService) {
      storeVideoReward(
        USER_ID,
        VideoAdReward(revenue = 0.123456789123456)
      )
    }
    verifyBlocking(userService) { onUserSpecificRequest(eq(USER_ID), any(), eq(appVersion), eq(null), eq(EN_US_LOCALE), eq(null)) }
  }

  @Test
  fun `SHOULD save device token ON deviceToken post call`() = withTestApplication(controller()) {
    val deviceToken = "deviceToken"

    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/users/$USER_ID/deviceToken"
    ) {
      addHeaders()
      setBody(defaultJsonConverter.encodeToString(DeviceTokenApiDto(deviceToken)))
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(userService) {
      updateDeviceToken(USER_ID, deviceToken, ANDROID)
    }
  }

  @Test
  fun `SHOULD NOT save device token ON deviceToken post call WHEN token is empty`() = withTestApplication(controller()) {
    handleRequest(
      method = HttpMethod.Post,
      uri = "/users/$USER_ID/deviceToken"
    ) {
      addHeaders()
      setBody(defaultJsonConverter.encodeToString(DeviceTokenApiDto("")))
    }
      .also { assertThat(it.response.status()).isEqualTo(HttpStatusCode.OK) }

    verifyBlocking(userService, never()) { updateDeviceToken(any(), any(), any()) }
  }

  @Test
  fun `SHOULD save firebaseAppInstanceId ON firebaseAppInstanceId post call`() = withTestApplication(controller()) {
    val firebaseAppInstanceId = "firebaseAppInstanceId"

    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/users/$USER_ID/firebaseAppInstanceId"
    ) {
      addHeaders()
      setBody(defaultJsonConverter.encodeToString(FirebaseAppInstanceIdDto(firebaseAppInstanceId)))
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(userService) {
      addOrUpdateFirebaseAppInstanceId(USER_ID, firebaseAppInstanceId)
    }
  }

  @Test
  fun `SHOULD return showRewardsIcon flag as true ON get user data call WHEN user already has no cashouts`() = withTestApplication(controller()) {
    cashoutService.mock({ userHasCashouts(user.userId) }, true)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(
      defaultJsonConverter.encodeToString(
        userApiDtoStub.copy(
          useRewards = true,
        )
      )
    )
  }

  @Test
  fun `SHOULD return coin label ON get user data call`() = withTestApplication(controller()) {
    userService.mock({ loadCoinGoalUser(USER_ID) }, user)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    defaultJsonConverter.decodeFromString<UserApiDto>(response.response.content!!).coinGoalLabel
      .let { assertThat(it).isEqualTo("mainCoinGoalReachedLabel") }
  }

  @Test
  fun `SHOULD return tutorialSteps ON get user data call WHEN user assigned to tutorialSteps`() = withTestApplication(controller()) {
    val tutorialSteps = TutorialSteps.getDefaultSequence().map(TutorialSteps::text)
    tutorialService.mock({ getTutorialSteps(USER_ID) }, tutorialSteps)
    tutorialService.mock(
      { getAdditionalTranslations(USER_ID) }, mapOf(
        "someTutorialKey1" to "someTutorialValue1",
        "someTutorialKey2" to "someTutorialValue2",
      )
    )

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    defaultJsonConverter.decodeFromString<UserApiDto>(response.response.content!!)
      .let {
        assertThat(it.tutorialSteps).isEqualTo(
          listOf("welcome", "reach_goal", "cash_out", "notification_permission", "welcome_bonus", "explore_now_medium_scroll")
        )
        assertThat(it.expLabels).isEqualTo(
          mapOf(
            "someTutorialKey1" to "someTutorialValue1",
            "someTutorialKey2" to "someTutorialValue2",
          )
        )
      }
  }

  class ParamsPaymentProviderAvailable : ArgumentsProvider {
    override fun provideArguments(context: ExtensionContext?): Stream<out Arguments?>? =
      Stream.of(
        Arguments.of(true, BigDecimal("0.05"), null),
        Arguments.of(false, BigDecimal("0.05"), true),
        Arguments.of(false, BigDecimal("0.06"), true),
        Arguments.of(false, BigDecimal("0.03"), false),
      )
  }

  @ParameterizedTest
  @ArgumentsSource(ParamsPaymentProviderAvailable::class)
  fun `SHOULD return paymentProviderAvailable ON get user data call`(
    userHasCashout: Boolean,
    amountUsd: BigDecimal,
    expected: Boolean?,
  ) {
    withTestApplication(controller()) {
      //given
      cashoutService.mock({ userHasCashouts(USER_ID) }, userHasCashout)
      cashoutService.mock(
        {
          getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID)
        },
        UserCurrencyEarnings(amountUsd, USD, amountUsd)
      )
      paymentProvidersService.mock({ isPaymentProvidersAvailable(userHasCashout, amountUsd) }, expected)
      //when
      val response = handleRequest(
        method = HttpMethod.Get,
        uri = "/users/$USER_ID"
      ) {
        addHeaders()
      }
      //then
      assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
      defaultJsonConverter.decodeFromString<UserApiDto>(response.response.content!!)
        .let {
          assertThat(it.paymentProviderAvailable).isEqualTo(expected)
        }
    }
  }

  class ParamsDemoGamesLaunchMode : ArgumentsProvider {
    override fun provideArguments(context: ExtensionContext?): Stream<out Arguments?>? =
      Stream.of(
        Arguments.of(DEFAULT, null),
        Arguments.of(InstallationMessageBefore, INSTALLATION_MESSAGE_BEFORE_GAME_START),
        Arguments.of(InstallationMessageAfter, INSTALLATION_MESSAGE_AFTER_GAME_COMPLETION),
      )
  }

  @ParameterizedTest
  @ArgumentsSource(ParamsDemoGamesLaunchMode::class)
  fun `SHOULD return demoGamesLaunchMode ON get user data call`(
    variation: BaseVariation,
    expected: DemoGameLaunchModeApiDto?,
  ) {
    withTestApplication(controller()) {
      abTestingService.mock({ assignedVariationValue(USER_ID, ANDROID_DEMO_GAMES) }, variation)

      val response = handleRequest(
        method = HttpMethod.Get,
        uri = "/users/$USER_ID"
      ) { addHeaders() }

      assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
      defaultJsonConverter.decodeFromString<UserApiDto>(response.response.content!!)
        .let {
          assertThat(it.demoGamesLaunchMode).isEqualTo(expected)
        }
    }
  }

  @Test
  fun `SHOULD return consentedToAnalytics false ON get user data call WHEN user not consented in gdpr country`() =
    withTestApplication(controller()) {
      userService.mock({ loadCoinGoalUser(USER_ID) }, user.copy(countryCode = "GB", isConsentedToAnalytics = false))
      marketService.mock({ isGdprAppliesToCountry("GB") }, true)
      val response = handleRequest(
        method = HttpMethod.Get,
        uri = "/users/$USER_ID"
      ) {
        addHeaders()
      }
      assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
      defaultJsonConverter.decodeFromString<UserApiDto>(response.response.content!!)
        .let {
          assertThat(it.consentedToAnalytics).isEqualTo(false)
        }
    }

  @Test
  fun `SHOULD return enablePlaystoreTrackingNotifications true ON get user data call`() =
    withTestApplication(controller()) {
      userService.mock({ loadCoinGoalUser(USER_ID) }, user)
      val response = handleRequest(
        method = HttpMethod.Get,
        uri = "/users/$USER_ID"
      ) {
        addHeaders()
      }
      assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
      defaultJsonConverter.decodeFromString<UserApiDto>(response.response.content!!)
        .let {
          assertThat(it.enablePlaystoreTrackingNotifications).isEqualTo(true)
        }
    }

  @Test
  fun `SHOULD return playersOnlineType WITH value panelCenterLongBlue ON get user data call`() =
    withTestApplication(controller()) {
      userService.mock({ loadCoinGoalUser(USER_ID) }, user)
      val response = handleRequest(
        method = HttpMethod.Get,
        uri = "/users/$USER_ID"
      ) {
        addHeaders()
      }
      assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
      defaultJsonConverter.decodeFromString<UserApiDto>(response.response.content!!)
        .let {
          assertThat(it.playersOnlineType).isEqualTo("CENTER_LONG_BLUE")
        }
    }

  @Test
  fun `SHOULD return online users WHEN online called`(): Unit = withTestApplication(controller()) {
    whenever(androidOnlineUsersService.getActiveUsers()) doReturn 1000

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "android/users/online"
    )
      .also {
        assertThat(it.response.status()).isEqualTo(HttpStatusCode.OK)
      }.response

    assertJsonEquals(
      //language=json
      """{"online":1000}""",
      response.content
    )
  }

  @Test
  fun `SHOULD return threeDotMenuItems ON get user data call`() = withTestApplication(controller()) {
    translationService.mock({ translateOrDefault(MENU_ITEM_MY_REWARDS, DE_LOCALE, USER_ID) }, "Meine Belohnungen")
    translationService.mock({ translateOrDefault(MENU_ITEM_CONTACT_US, DE_LOCALE, USER_ID) }, "helfen")
    translationService.mock({ translateOrDefault(MENU_ITEM_PRIVACY_POLICY, DE_LOCALE, USER_ID) }, "Datenschutzerklärung")
    translationService.mock({ translateOrDefault(MENU_ITEM_FAQ, DE_LOCALE, USER_ID) }, "Regeln des Treueprogramm")
    translationService.mock({ translateOrDefault(MENU_ITEM_TUTORIAL_HUB, DE_LOCALE, USER_ID) }, "Tutorials")
    translationService.mock({ translateOrDefault(MENU_ITEM_ACCOUNT_DELETION, DE_LOCALE, USER_ID) }, "Kontolöschung anfordern")

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders(appVersion = 55, locale = "de")
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    defaultJsonConverter.decodeFromString<UserApiDto>(response.response.content!!)
      .let {
        assertThat(it.threeDotMenuItems).containsExactly(
          ThreeDotMenuItem(ThreeDotMenuActionApiDto.routeToRewards(), "Meine Belohnungen"),
          ThreeDotMenuItem(ThreeDotMenuActionApiDto.contactUs(), "helfen"),
          ThreeDotMenuItem(ThreeDotMenuActionApiDto.openLinkInPopUp("https://justplayapps.com/privacy-policy/"), "Datenschutzerklärung"),
          ThreeDotMenuItem(ThreeDotMenuActionApiDto.openLinkInPopUp("https://justplayapps.com/loyalty-program-rules/"), "Regeln des Treueprogramm"),
          ThreeDotMenuItem(ThreeDotMenuActionApiDto.tutorialHub(), "Tutorials"),
          ThreeDotMenuItem(ThreeDotMenuActionApiDto.accountDeletion(), "Kontolöschung anfordern"),
        )
      }
  }

  @Test
  fun `SHOULD return threeDotMenuItem TUTORIAL_HUB ON get user data call with app version 53`() = withTestApplication(controller()) {
    translationService.mock({ translateOrDefault(MENU_ITEM_CONTACT_US, DE_LOCALE, USER_ID) }, "Tutorials")

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders(appVersion = 53, locale = "de")
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    defaultJsonConverter.decodeFromString<UserApiDto>(response.response.content!!)
      .let {
        assertThat(it.threeDotMenuItems).contains(
          ThreeDotMenuItem(ThreeDotMenuActionApiDto.tutorialHub(), "Tutorials"),
        )
      }
  }

  @Test
  fun `SHOULD return threeDotMenuItem OPT_OUT ON get user data call with app version 68`() = withTestApplication(controller()) {
    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders(appVersion = 68, locale = "de")
    }

    val menuItems = defaultJsonConverter.decodeFromString<UserApiDto>(response.response.content!!).threeDotMenuItems

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(menuItems).isEqualTo(
      // all items for order verification
      listOf(
        ThreeDotMenuItem(ThreeDotMenuActionApiDto.routeToRewards(), "My Rewards"),
        ThreeDotMenuItem(ThreeDotMenuActionApiDto.contactUs(), "Help"),
        ThreeDotMenuItem(
          ThreeDotMenuActionApiDto.openLinkInPopUp("https://justplayapps.com/privacy-policy/"), "Privacy policy",
        ),
        ThreeDotMenuItem(
          ThreeDotMenuActionApiDto.openLinkInPopUp("https://justplayapps.com/loyalty-program-rules/"),
          "Loyalty Program Rules",
        ),
        ThreeDotMenuItem(
          ThreeDotMenuActionApiDto.tutorialHub(),
          "Tutorials",
        ),
        ThreeDotMenuItem(
          ThreeDotMenuActionApiDto(OPT_OUT),
          label = "Opt-out"
        ),
        ThreeDotMenuItem(
          ThreeDotMenuActionApiDto.accountDeletion(),
          "Request Account Deletion",
        )
      )
    )
  }

  @Test
  fun `SHOULD populate values for coinsString, coinGoalString, boosterCoinGoalString ON get user data call WHEN coins, coinGoal, boosterCoinGoal are not null`() =
    withTestApplication(controller()) {
      userService.mock(
        { loadCoinGoalUser(USER_ID) }, user
      )
      rewardingFacade.mock({ getUserCurrentCoinsBalance(user.userId, user.appPlatform) }, UserCurrentCoinsGoalBalance(150, 0, 0))
      cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriodStub.copy(coinGoal = 10))

      val response = handleRequest(
        method = HttpMethod.Get,
        uri = "/users/$USER_ID"
      ) {
        addHeaders()
      }

      assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
      defaultJsonConverter.decodeFromString<UserApiDto>(response.response.content!!)
        .let {
          assertThat(it.coinsString).isEqualTo("150")
          assertThat(it.coinGoalString).isEqualTo("20000")
        }
    }

  @Test
  fun `SHOULD return videoAdIntervalSeconds ON get user data call WHEN user assigned to videoOfferCoolDown exp`() = withTestApplication(controller()) {
    abTestingService.mock({ getVideoOfferCoolDownSeconds(USER_ID) }, 10500)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    defaultJsonConverter.decodeFromString<UserApiDto>(response.response.content!!).videoAdIntervalSeconds
      .let { assertThat(it).isEqualTo(10500) }
  }

  @Test
  fun `SHOULD use stored cashout period and goal data ON get user data call WHEN user is on individualCashoutPeriod experiment`() =
    withTestApplication(controller()) {
      cashoutService.mock(
        { getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) },
        UserCurrencyEarnings(BigDecimal("10.019"), Currency.getInstance("USD"), BigDecimal("10.019"))
      )
      cashoutStatusService.mock({ isCashoutEnabled(user.userId) }, true)
      cashoutPeriodsService.mock({ getCurrentCashoutPeriod(user.userId) }, cashoutPeriodStub)
      rewardingFacade.mock({ getUserCurrentCoinsBalance(user.userId, user.appPlatform) }, UserCurrentCoinsGoalBalance(4000, 1000, 3000))

      val response = handleRequest(
        method = HttpMethod.Get,
        uri = "/users/$USER_ID"
      ) {
        addHeaders()
      }

      assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
      assertThat(response.response.content).isEqualTo(
        defaultJsonConverter.encodeToString(
          userApiDtoStub.copy(
            cashoutAvailable = true,
            cashoutAmount = "$10.01",
            nextCashoutTimestamp = cashoutPeriodStub.periodEnd,
            coinGoal = 2000,
            coinGoalString = "2000",
            coins = 3000,
            coinsBalance = 4000,
            coinsString = "4000"
          )
        )
      )
    }

  @Test
  fun `SHOULD return disabled cashout ON get user data call WHEN ceil rounded amount is zero`() = withTestApplication(controller()) {
    val userEarnings = UserCurrencyEarnings(BigDecimal("0.0099"), Currency.getInstance("USD"), BigDecimal("0.0099"))
    cashoutService.mock({ getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) }, userEarnings)
    cashoutStatusService.mock({ isCashoutEnabled(user.userId) }, true)
    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(user.userId) }, cashoutPeriodStub)
    rewardingFacade.mock({ getUserCurrentCoinsBalance(user.userId, user.appPlatform) }, UserCurrentCoinsGoalBalance(4000, 1000, 3000))

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(
      defaultJsonConverter.encodeToString(
        userApiDtoStub.copy(
          coins = 3000,
          coinsBalance = 4000,
          coinsString = "4000",
          cashoutAvailable = false,
          cashoutAmount = "",
          nextCashoutTimestamp = cashoutPeriodStub.periodEnd,
          coinGoal = 2000,
          coinGoalString = "2000"
        )
      )
    )
  }

  @Test
  fun `SHOULD return user unpaid earnings sum rounded ON get user data call WHEN user has some unpaid earnings`() = withTestApplication(controller()) {
    val userEarnings = UserCurrencyEarnings(BigDecimal("11.0099"), Currency.getInstance("USD"), BigDecimal("11.0099"))
    cashoutService.mock({ getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) }, userEarnings)
    cashoutStatusService.mock({ isCashoutEnabled(user.userId) }, true)
    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(user.userId) }, cashoutPeriodStub)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    defaultJsonConverter.decodeFromString<UserApiDto>(response.response.content!!)
      .let { assertThat(it.cashoutAmount).isEqualTo("$11.00") }
  }

  @Test
  fun `SHOULD return market ON get user data call`() = withTestApplication(controller()) {
    val userEarnings = UserCurrencyEarnings(BigDecimal("11.0099"), Currency.getInstance("USD"), BigDecimal("11.0099"))
    cashoutService.mock({ getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) }, userEarnings)
    cashoutStatusService.mock({ isCashoutEnabled(user.userId) }, true)
    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(user.userId) }, cashoutPeriodStub)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    defaultJsonConverter.decodeFromString<UserApiDto>(response.response.content!!)
      .let { assertThat(it.market).isEqualTo("test-market") }
  }

  @Test
  fun `SHOULD return user data with event headers ON user call WHEN there are tracked events`() = withTestApplication(controller()) {
    userService.mock({ loadCoinGoalUser(USER_ID) }, user.copy(userId = USER_ID))
    trackedEventsService.mock(
      { prepareHeadersToSend(USER_ID) },
      mapOf(
        TrackedEvent.EventTrackingPlatform.FIREBASE.header to "event1,event2",
        TrackedEvent.EventTrackingPlatform.FACEBOOK.header to "event1"
      )
    )
    userPopupMessagesService.mock({ getMessageForUser(any(), any(), anyBoolean()) }, "")

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(
      defaultJsonConverter.encodeToString(userApiDtoStub)
    )
    assertThat(response.response.headers[TrackedEvent.EventTrackingPlatform.FIREBASE.header]).isEqualTo("event1,event2")
    assertThat(response.response.headers[TrackedEvent.EventTrackingPlatform.FACEBOOK.header]).isEqualTo("event1")
  }

  @Test
  fun `SHOULD return response with desired version in header ON user call WHEN android`() =
    withTestApplication(controller()) {
      val response = handleRequest(
        method = HttpMethod.Get,
        uri = "/users/$USER_ID"
      ) {
        addHeader("Content-Type", "application/json")
        addHeader(ANDROID_APP_VERSION_HEADER, "${getDesiredAppVersion(ANDROID) - 1}")
        addHeader(COUNTRY_HEADER, COUNTRY_CODE)
        addHeader(IP_HEADER, IP)
      }

      assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
      assertThat(response.response.headers[DESIRED_APP_VERSION_HEADER]).isEqualTo("${getDesiredAppVersion(ANDROID)}")
    }

  @Test
  fun `SHOULD return disabled rewards ON user call WHEN user is on rewards variation but did not cashout yet`() = withTestApplication(controller()) {
    cashoutService.mock({ userHasCashouts(USER_ID) }, false)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(
      defaultJsonConverter.encodeToString(
        userApiDtoStub
      )
    )
  }

  @Test
  fun `SHOULD return enabled rewards ON user call WHEN user is on rewards variation but already cashed out`() = withTestApplication(controller()) {
    cashoutService.mock({ userHasCashouts(USER_ID) }, true)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(
      defaultJsonConverter.encodeToString(
        userApiDtoStub.copy(
          useRewards = true,
        )
      )
    )
  }

  @Test
  fun `SHOULD return user data with popup message header ON user call WHEN there are some messages`() = withTestApplication(controller()) {
    val testMessage = "some messages here"
    userService.mock({ loadCoinGoalUser(USER_ID) }, user.copy(userId = USER_ID))
    userPopupMessagesService.mock({ getMessageForUser(eq(USER_ID), eq(EndpointType.GET_USER), anyBoolean()) }, testMessage)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.headers[POPUP_MESSAGE_HEADER]).isEqualTo(testMessage.base64Encoded())
  }

  @Test
  fun `SHOULD return user data without popup message header ON user call WHEN no messages provided`() = withTestApplication(controller()) {
    userService.mock({ loadCoinGoalUser(USER_ID) }, user.copy(userId = USER_ID))
    userPopupMessagesService.mock({ getMessageForUser(eq(USER_ID), eq(EndpointType.GET_USER), anyBoolean()) }, null)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.headers[POPUP_MESSAGE_HEADER]).isNull()
  }

  @Test
  fun `SHOULD return user data with giftBoxInsteadOfEarnings ON user call WHEN applicable`() = withTestApplication(controller()) {
    val earnings = UserCurrencyEarnings(BigDecimal("1.5"), Currency.getInstance("USD"), BigDecimal("1.5"))

    cashoutService.mock({ userHasCashouts(USER_ID) }, false)
    cashoutService.mock({ getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) }, earnings)
    hideCashoutAmountExperimentService.mock({ shouldShowGiftBox(USER_ID, false, BigDecimal("1.5")) }, true)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(
      defaultJsonConverter.encodeToString(
        userApiDtoStub.copy(
          cashoutAvailable = true,
          cashoutAmount = "$1.50",
          giftBoxInsteadOfEarnings = true,
        )
      )
    )
  }

  @ParameterizedTest
  @CsvSource(
    delimiter = ';',
    value = [
      "USD; $1,001.50",
      "CAD; $1,001.50",
      "EUR; 1.001,50 €",
      "CHF; CHF 1,001.50",
      "GBP; £1,001.50",
      "DKK; DKK 1,001.50",
      "NOK; kr 1 001,50",
      "PLN; 1 001,50 zł",
      "SEK; 1 001,50 Skr",
      "AUD; A\$ 1,001.50",
      "SGD; SGD1.001,50",
      "JPY; ￥1,002",
      "HKD; HK\$ 1,001.50",
      "MXN; MX\$ 1,001.50",
      "BRL; 1 001,50 BRL",
    ]
  )
  fun `SHOULD return user data with correct cashoutAmount ON user call`(currencyCode: String, expected: String) = withTestApplication(controller()) {
    val earnings = UserCurrencyEarnings(BigDecimal("1001.50"), Currency.getInstance(currencyCode), BigDecimal("1001.50"))

    cashoutService.mock({ userHasCashouts(USER_ID) }, false)
    cashoutService.mock({ getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) }, earnings)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(
      defaultJsonConverter.encodeToString(
        userApiDtoStub.copy(
          cashoutAvailable = true,
          cashoutAmount = expected
        )
      )
    )
  }

  @Test
  fun `SHOULD return user data with cashoutButtonStyle ON user call WHEN experiment participant`() = withTestApplication(controller()) {
    abTestingService.mock({ assignedVariationValue(user.userId, ANDROID_HIDE_EARNINGS) }, Variations.ANDROID_HIDE_EARNINGS_GRAY_GREEN)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(
      defaultJsonConverter.encodeToString(
        userApiDtoStub.copy(
          cashoutButtonStyle = CashoutButtonStyle.GRAY_GREEN
        )
      )
    )
  }

  @Test
  fun `SHOULD return booster sign ON user call WHEN booster sign provided`() = withTestApplication(controller()) {
    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriodStub)
    translationService.mock({ translateOrDefault(CASHOUT_TIMER_SUBTEXT, EN_US_LOCALE, USER_ID) }, "Boosted sign")

    rewardingFacade.mock({ getUserCurrentCoinsBalance(user.userId, user.appPlatform) }, UserCurrentCoinsGoalBalance(4000, 1000, 3000))

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(
      defaultJsonConverter.encodeToString(
        userApiDtoStub.copy(
          coins = 3000,
          coinsBalance = 4000,
          coinsString = "4000",
          cashoutTimerSubtext = "Boosted sign",
          //
          coinGoal = cashoutPeriodStub.coinGoal * 2000,
          coinGoalString = (cashoutPeriodStub.coinGoal * 2000).toString(),
          nextCashoutTimestamp = cashoutPeriodStub.periodEnd,
        )
      )
    )
  }

  @Test
  fun `SHOULD call updateGoogleAdId with app version ON POST googleAdId`() = withTestApplication(controller()) {
    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/users/$USER_ID/googleAdId"
    )
    {
      addHeaders()//app version here = MIN_APP_VERSION_FOR_USER_CREATION
      setBody(defaultJsonConverter.encodeToString(GoogleAdIdApiDto(id = "googleAdId_123")))
    }

    verifyBlocking(userService) { updateGoogleAdId(USER_ID, "googleAdId_123", appVersion) }
    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
  }

  @Test
  fun `SHOULD call addUserTrackingData ON POST appSetId`() = withTestApplication(controller()) {
    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/users/$USER_ID/appSetId"
    )
    {
      addHeaders()
      setBody(defaultJsonConverter.encodeToString(AppSetIdApiDto(id = "appSetId_123")))
    }

    verifyBlocking(userService) { addUserTrackingData(USER_ID, TrackingData("appSetId_123", IDFV, ANDROID)) }
    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
  }

  @Test
  fun `SHOULD call updateAdjustId ON POST adjustId`() = withTestApplication(controller()) {
    val adjustId = "adjustId"
    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/users/$USER_ID/adjustId"
    )
    {
      addHeaders()
      setBody(defaultJsonConverter.encodeToString(AdjustIdDto(adjustId = adjustId)))
    }

    verifyBlocking(userService) { updateAdjustId(USER_ID, adjustId) }
    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
  }

  @Test
  fun `SHOULD process ON welcome-coins-acceptation`() = withTestApplication(controller()) {
    handleRequest(
      method = HttpMethod.Post,
      uri = "/users/$USER_ID/welcome-coins-acceptation"
    ) {
      addHeader(ANDROID_APP_VERSION_HEADER, "39")
    }
      .also { assertThat(it.response.status()).isEqualTo(HttpStatusCode.OK) }

    verifyBlocking(welcomeCoinsService) { acceptWelcomeCoins(USER_ID, user.appPlatform) }
  }

  @Test
  fun `SHOULD return top bar config ON top-running-bar`(): Unit = withTestApplication(controller()) {
    val expected = TopRunningBarDto(
      values = listOf("some", "bar", "text"),
      textColor = "some color",
      backgroundColor = "some background color"
    )
    topRunningBarService.mock({ getTopRunningBarConfig(USER_ID) }, expected)

    handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID/top-running-bar"
    ) {
      addHeader(ANDROID_APP_VERSION_HEADER, "39")
    }
      .also {
        assertThat(it.response.status()).isEqualTo(HttpStatusCode.OK)
        assertThat(defaultJsonConverter.decodeFromString<TopRunningBarDto>(it.response.content!!)).isEqualTo(expected)
      }
  }

  @Test
  fun `SHOULD return 404 WHEN no config for user`(): Unit = withTestApplication(controller()) {
    topRunningBarService.mock({ getTopRunningBarConfig(USER_ID) }, null)

    handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID/top-running-bar"
    ) {
      addHeader(ANDROID_APP_VERSION_HEADER, "39")
    }
      .also {
        assertThat(it.response.status()).isEqualTo(HttpStatusCode.NotFound)
        assertEquals("No bar configuration for user", it.response.content!!)
      }
  }

  @Test
  fun `SHOULD save user notifications status preferences ON post user-notifications-state`(): Unit = withTestApplication(controller()) {
    val request = UserNotificationsStatusApiDto(enabled = true)
    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/users/$USER_ID/user-notifications-state"
    ) {
      addHeader(ANDROID_APP_VERSION_HEADER, "42")
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(request))
    }

    verifyBlocking(userNotificationStatusService) { setUserNotificationsStatus(USER_ID, true) }
    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
  }

  @Test
  fun `SHOULD trigger onUserSpecificRequest ON user context endpoint call`() = withTestApplication(controller()) {
    val actual = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeader(ANDROID_APP_VERSION_HEADER, appVersion.version.toString())
      addHeader(HttpHeaders.AcceptLanguage, "en-US")
    }

    assertThat(actual.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(userService) {
      onUserSpecificRequest(eq(USER_ID), any(), eq(appVersion), eq(null), eq(EN_US_LOCALE), eq(null))
    }
  }

  @ParameterizedTest
  @ValueSource(booleans = [false, true])
  fun `SHOULD respect market header ON get user data call`(marketHeaderDefined: Boolean) = withTestApplication(controller()) {
    val expected = if (marketHeaderDefined) "market" else null

    handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeader(ANDROID_APP_VERSION_HEADER, appVersion.version.toString())
      addHeader(HttpHeaders.AcceptLanguage, "en-US")
      if (marketHeaderDefined) addHeader(MARKET_HEADER, "market")
    }

    verifyBlocking(userService) {
      onUserSpecificRequest(eq(USER_ID), any(), eq(appVersion), eq(null), eq(EN_US_LOCALE), eq(expected))
    }
  }

  @Test
  fun `SHOULD trigger onUserSpecificRequest ON user context endpoint call with IOS header`() = withTestApplication(controller()) {
    val actual = handleRequest(method = HttpMethod.Get, uri = "/users/$USER_ID") {
      addHeader(IOS_APP_VERSION_HEADER, "1")
      addHeader(HttpHeaders.AcceptLanguage, "en-US")
    }

    assertThat(actual.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(userService) {
      onUserSpecificRequest(eq(USER_ID), any(), eq(AppVersionDto(IOS, 1)), eq(null), eq(EN_US_LOCALE), eq(null))
    }
  }

  @Test
  fun `SHOULD return shouldShowTimerInCoinGoalSection false ON get user when earnings are zero`() = withTestApplication(controller()) {
    abTestingService.mock({ shouldShowTimerInCoinGoal(USER_ID) }, true)
    val response = handleRequest(method = HttpMethod.Get, uri = "/users/$USER_ID") {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(
      defaultJsonConverter.encodeToString(
        userApiDtoStub.copy(showTimerInCoinGoalSection = false)
      )
    )
  }

  @Test
  fun `SHOULD return cashoutBonusCoins ON get user WHEN cashoutCoinsService#createApiDto returns a value`() = withTestApplication(controller()) {
    abTestingService.mock({ shouldShowTimerInCoinGoal(USER_ID) }, true)
    val expectedCashoutCoinsDto = CashoutCoinsApiDto(CashoutCoinsMode.HOME_SCREEN_COINS, 10000)
    cashoutCoinsService.mock({ createApiDto(USER_ID, false, ANDROID) }, expectedCashoutCoinsDto)
    val response = handleRequest(method = HttpMethod.Get, uri = "/users/$USER_ID") {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    val expectedJson = javaClass.getResource("/user/user-controller/cashoutBonusCoin.json")!!.readText()
    assertJsonEquals(expectedJson, response.response.content, whenIgnoringPaths("timestamp"))
  }

  @Test
  fun `SHOULD return shouldShowTimerInCoinGoalSection true ON get user when earnings are 1`() = withTestApplication(controller()) {
    cashoutService.mock(
      { getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) },
      UserCurrencyEarnings(BigDecimal.ONE, Currency.getInstance("USD"), BigDecimal.ONE)
    )
    abTestingService.mock({ shouldShowTimerInCoinGoal(USER_ID) }, true)

    val response = handleRequest(method = HttpMethod.Get, uri = "/users/$USER_ID") {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(
      defaultJsonConverter.encodeToString(
        userApiDtoStub.copy(
          showTimerInCoinGoalSection = true,
          cashoutAvailable = true,
          cashoutAmount = "$1.00",
        )
      )
    )
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD return useAmplitudeAnalytics true ON get user when user on Experiment`(onVariation: Boolean) = withTestApplication(controller()) {
    amplitudeEventEffectHandler.mock({ shouldUseAmplitudeAnalytics(USER_ID) }, onVariation)

    val response = handleRequest(method = HttpMethod.Get, uri = "/users/$USER_ID") {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(defaultJsonConverter.encodeToString(userApiDtoStub.copy(useAmplitudeAnalytics = onVariation)))
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD return attestation required flag ON get user`(wasSuccessfullyExamined: Boolean) = withTestApplication(controller()) {
    examinationService.mock({ wasSuccessfullyExamined(USER_ID) }, wasSuccessfullyExamined)

    val response = handleRequest(method = HttpMethod.Get, uri = "/users/$USER_ID") {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(
      defaultJsonConverter.encodeToString(userApiDtoStub.copy(attestationRequired = !wasSuccessfullyExamined))
    )
  }

  @Test
  fun `SHOULD trigger onUserSpecificRequest ON android user context endpoint call`() = withTestApplication(controller()) {
    val actual = handleRequest(
      method = HttpMethod.Post,
      uri = "/android/users/$USER_ID/pre-game-screen-opened"
    ) {
      addHeader(ANDROID_APP_VERSION_HEADER, appVersion.version.toString())
      addHeader(HttpHeaders.AcceptLanguage, "en-US")
    }

    assertThat(actual.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(userService) {
      onUserSpecificRequest(eq(USER_ID), any(), eq(appVersion), eq(null), eq(EN_US_LOCALE), eq(null))
    }
  }

  @Test
  fun `SHOULD process POST pre-game-screen-opened call`() = withTestApplication(controller()) {
    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/android/users/$USER_ID/pre-game-screen-opened"
    )
    {
      addHeaders()
    }

    verifyBlocking(userGameService) { onPreGameScreenOpened(USER_ID) }
    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
  }

  @Test
  fun `SHOULD process POST provider-survey call`() = withTestApplication(controller()) {
    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/android/users/$USER_ID/provider-survey"
    )
    {
      //language=json
      setBody(
        """
        {
          "userChoice": ["test1", "test2"],
          "ownAnswer": "test3"
        }
      """.trimIndent()
      )
      addHeaders()
    }

    verifyBlocking(paymentProviderSurveyService) { saveSurveyResult(USER_ID, listOf("test1", "test2"), "test3", ANDROID) }
    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
  }

  @Test
  fun `SHOULD return user data with cashoutProgressBarMode ON user call WHEN applicable`() = withTestApplication(controller()) {
    val earnings = UserCurrencyEarnings(BigDecimal("1.5"), Currency.getInstance("USD"), BigDecimal("1.5"))

    cashoutService.mock({ userHasCashouts(USER_ID) }, false)
    cashoutService.mock({ getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) }, earnings)
    abTestingService.mock(
      { abTestingService.assignedVariationValue(USER_ID, ANDROID_CASHOUT_PROGRESS_BAR) },
      Variations.ANDROID_DASHED_PROGRESS_BAR
    )

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(
      defaultJsonConverter.encodeToString(
        userApiDtoStub.copy(
          cashoutAvailable = true,
          cashoutAmount = "$1.50",
          giftBoxInsteadOfEarnings = false,
          cashoutProgressBarMode = AndroidCashoutProgressBarMode.ANDROID_DASHED_PROGRESS_BAR,
        )
      )
    )
  }

  @Test
  fun `SHOULD return user data with incompleteCashoutRestoringMode ON user call WHEN applicable`() = withTestApplication(controller()) {
    val earnings = UserCurrencyEarnings(BigDecimal("1.5"), Currency.getInstance("USD"), BigDecimal("1.5"))

    cashoutService.mock({ userHasCashouts(USER_ID) }, false)
    cashoutService.mock({ getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) }, earnings)
    abTestingService.mock({ assignedVariationValue(USER_ID, ANDROID_INCOMPLETE_CASHOUT_RESTORING) }, Variations.ANDROID_RESTORE_CASHOUT)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(
      defaultJsonConverter.encodeToString(
        userApiDtoStub.copy(
          cashoutAvailable = true,
          cashoutAmount = "$1.50",
          giftBoxInsteadOfEarnings = false,
          incompleteCashoutRestoringMode = AndroidIncompleteCashoutRestoringMode.ANDROID_RESTORE_CASHOUT,
        )
      )
    )
  }

  @Test
  fun `SHOULD return initializeAppLovin flag ON user call WHEN applicable`() = withTestApplication(controller()) {
    userService.mock({ useAndroidLazyApplovinInitialization(USER_ID) }, true)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(
      defaultJsonConverter.encodeToString(
        userApiDtoStub.copy(
          initializeApplovin = false,
        )
      )
    )
  }

  @Test
  fun `SHOULD return user data with faceScanPreScreen params ON user call WHEN applicable`() = withTestApplication(controller()) {
    val faceScanPreScreenParams = AndroidFaceScanPreScreenParams(
      screenType = AndroidFaceScanPreScreenType.FULLSCREEN_HUMAN,
      htmlText = "<p>Hello</p><br /><p>World</p>",
      exampleImageUrl = "image.url",
    )

    faceScanPreScreenService.mock({ createApiDto(eq(USER_ID), anyOrNull(), any(), any()) }, faceScanPreScreenParams)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(
      defaultJsonConverter.encodeToString(
        userApiDtoStub.copy(
          faceScanPreScreen = faceScanPreScreenParams,
        )
      )
    )
  }

  @Test
  fun `SHOULD return user data with privacy field ON user call WHEN applicable`() = withTestApplication(controller()) {
    marketService.mock({ getPrivacyRegulation("US") }, PrivacyRegulationType.OPT_OUT)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(
      defaultJsonConverter.encodeToString(
        userApiDtoStub.copy(
          privacyRegulation = PrivacyRegulationType.OPT_OUT
        )
      )
    )
  }

  @Test
  fun `SHOULD return user data with preGameMode ON user call WHEN experiment participant`() = withTestApplication(controller()) {
    abTestingService.mock({ assignedVariationValue(user.userId, ANDROID_TASKS_IN_PRE_GAME) }, Variations.ANDROID_TASK_IN_PRE_GAME)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(
      defaultJsonConverter.encodeToString(
        userApiDtoStub.copy(
          preGameMode = PreGameModeApiDto.TASKS
        )
      )
    )
  }

  @ParameterizedTest
  @TypedVariationSource(AndroidOnboardingProgressBarVariation::class)
  fun `SHOULD return user data with onboardingProgressBare ON user call WHEN experiment participant`(variation: AndroidOnboardingProgressBarVariation) =
    withTestApplication(controller()) {
      abTestingService.mock({ assignedVariationValue(user.userId, ANDROID_ONBOARDING_PROGRESS_BAR) }, variation)

      val response = handleRequest(
        method = HttpMethod.Get,
        uri = "/users/$USER_ID"
      ) {
        addHeaders()
      }

      assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
      assertThat(response.response.content).isEqualTo(
        defaultJsonConverter.encodeToString(
          userApiDtoStub.copy(
            onboardingProgressBarMode = variation.getKey()
          )
        )
      )
    }

  @Test
  fun `SHOULD return user data with animationEffectMode ON user call WHEN experiment participant`() = withTestApplication(controller()) {
    val celebrateEarningsConfig = CelebrateEarningsConfigApiDto(animationMode = AnimationMode.MIN, coinsTotal = 20000)
    androidAnimationToCelebrateEarningsService.mock({ getCelebrateEarningsConfig(user.userId) }, celebrateEarningsConfig)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(
      defaultJsonConverter.encodeToString(
        userApiDtoStub.copy(
          celebrateEarningsConfig = celebrateEarningsConfig
        )
      )
    )
  }

  @Test
  fun `SHOULD process POST cashout-2x-offer-accepted call`() = withTestApplication(controller()) {
    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/users/$USER_ID/cashout-2x-offer-accepted"
    )
    {
      addHeaders()
    }

    verifyBlocking(cashout2xOfferService) { trackOfferAccepted(USER_ID) }
    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
  }

  @Test
  fun `SHOULD return onboarding progress bar with INCOMPLETE status WHEN not all required tasks are completed`() = withTestApplication(controller()) {
    onboardingProgressBarService.mock(
      { getProgressBar(USER_ID) }, listOf(
        OnboardingProgressBarStep("Welcome Bonus", WELCOME_BONUS, OnboardingProgressBarStepStatus.COMPLETED, false, null, "20000"),
        OnboardingProgressBarStep("Keep playing {game_name}", PLAY_FIRST_GAME, OnboardingProgressBarStepStatus.INCOMPLETE, false, null, "Unlimited"),
        OnboardingProgressBarStep("Watch Rewarded Video", WATCH_REWARDED_VIDEO, OnboardingProgressBarStepStatus.INCOMPLETE, true, null, "10000"),
        OnboardingProgressBarStep("1st Payment", ROUTE_TO_CASHOUT, OnboardingProgressBarStepStatus.INCOMPLETE, false, "$$$", null),
      )
    )

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/android/users/$USER_ID/onboarding-progress-bar"
    ) {
      addHeaders()
    }

    assertEquals(HttpStatusCode.OK, response.response.status())
    assertJsonEquals(
      //language=json
      """
      {
        "progressBarStatus" : "INCOMPLETE",
        "steps": [{
          "text": "Welcome Bonus",
          "type": "WELCOME_BONUS",
          "status": "COMPLETED",
          "coins": "20000"
        }, {
          "text": "Keep playing {game_name}",
          "type": "PLAY_FIRST_GAME",
          "status": "INCOMPLETE",
          "coins": "Unlimited"
        }, {
          "text": "Watch Rewarded Video",
          "type": "WATCH_REWARDED_VIDEO",
          "status": "INCOMPLETE",
          "coins": "10000"
        }, {
          "text": "1st Payment",
          "type": "ROUTE_TO_CASHOUT",
          "status": "INCOMPLETE",
          "coins": "$$$",
          "valuation": "$$$"
        }]
      }
    """.trimIndent(), response.response.content
    )
  }

  @Test
  fun `SHOULD return onboarding progress bar with COMPLETED status WHEN all required tasks are completed`() = withTestApplication(controller()) {
    onboardingProgressBarService.mock(
      { getProgressBar(USER_ID) }, listOf(
        OnboardingProgressBarStep("Welcome Bonus", WELCOME_BONUS, OnboardingProgressBarStepStatus.COMPLETED, false, null, "20000"),
        OnboardingProgressBarStep("Keep playing {game_name}", PLAY_FIRST_GAME, OnboardingProgressBarStepStatus.COMPLETED, false, null, "Unlimited"),
        OnboardingProgressBarStep("Watch Rewarded Video", WATCH_REWARDED_VIDEO, OnboardingProgressBarStepStatus.INCOMPLETE, true, null, "10000"),
        OnboardingProgressBarStep("1st Payment", ROUTE_TO_CASHOUT, OnboardingProgressBarStepStatus.COMPLETED, false, "$$$", null),
      )
    )

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/android/users/$USER_ID/onboarding-progress-bar"
    ) {
      addHeaders()
    }

    assertEquals(HttpStatusCode.OK, response.response.status())
    assertJsonEquals(
      //language=json
      """
      {
        "progressBarStatus" : "COMPLETED",
        "steps": [{
          "text": "Welcome Bonus",
          "type": "WELCOME_BONUS",
          "status": "COMPLETED",
          "coins": "20000"
        }, {
          "text": "Keep playing {game_name}",
          "type": "PLAY_FIRST_GAME",
          "status": "COMPLETED",
          "coins": "Unlimited"
        }, {
          "text": "Watch Rewarded Video",
          "type": "WATCH_REWARDED_VIDEO",
          "status": "INCOMPLETE",
          "coins": "10000"
        }, {
          "text": "1st Payment",
          "type": "ROUTE_TO_CASHOUT",
          "status": "COMPLETED",
          "coins": "$$$",
          "valuation": "$$$"
        }]
      }
    """.trimIndent(), response.response.content
    )
  }

  @Test
  fun `SHOULD process GET highlighted-games call`() = withTestApplication(controller()) {
    androidHighlightedGamesService.mock({ loadHighlightedGames(USER_ID, EN_US_LOCALE) }, androidHighlightedGamesApiDtoStub)
    //language=JSON
    val expectedResponse = """
      {
        "items": [
          {
            "id": 2,
            "applicationId": "com.gimica.solitaireverse",
            "title": "name",
            "subtitle": "Play for loyalty points!",
            "subtext": "Play & Earn",
            "iconUrl": "https://storage.googleapis.com/public-playtime/images/iconFilename",
            "activityName": "activityName",
            "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.solitaireverse&referrer=user_id="
          },
          {
            "id": 3,
            "applicationId": "com.gimica.ballbounce",
            "title": "name",
            "subtitle": "Play for loyalty points!",
            "subtext": "Play & Earn",
            "iconUrl": "https://storage.googleapis.com/public-playtime/images/iconFilename",
            "activityName": "activityName",
            "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.ballbounce&referrer=user_id="
          }
        ]
      }
    """.trimIndent()

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/android/users/$USER_ID/highlighted-games"
    )
    {
      addHeaders()
    }

    verifyBlocking(androidHighlightedGamesService) { loadHighlightedGames(USER_ID, EN_US_LOCALE) }
    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertJsonEquals(expectedResponse, response.response.content)
  }

  @Test
  fun `SHOULD return user data with showPayPalLogo ON user call WHEN experiment participant`() = withTestApplication(controller()) {
    abTestingService.mock({ isUserExperimentParticipant(user.userId, ANDROID_SHOW_PAYPAL_LOGO) }, true)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(
      defaultJsonConverter.encodeToString(
        userApiDtoStub.copy(
          showPayPalLogo = true
        )
      )
    )
  }

  @Test
  fun `SHOULD return user data with cashStreakMode ON user call WHEN experiment participant`() = withTestApplication(controller()) {
    abTestingService.mock({ assignedVariationValue(user.userId, ANDROID_CASH_STREAK) }, CashStreakOn)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(
      defaultJsonConverter.encodeToString(
        userApiDtoStub.copy(
          cashStreakMode = CASH_STREAK_ON
        )
      )
    )
  }

  @Test
  fun `SHOULD return user data with gameStoriesMode ON user call WHEN experiment participant`() = withTestApplication(controller()) {
    abTestingService.mock({ assignedVariationValue(user.userId, ANDROID_GAME_STORIES) }, ANDROID_GAME_STORIES_PRE_GAME)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(
      defaultJsonConverter.encodeToString(
        userApiDtoStub.copy(
          gameStoriesMode = PRE_GAME
        )
      )
    )
  }

  @Test
  fun `SHOULD return user data with em3 coin goal and milestones ON user call WHEN experiment participant`() = withTestApplication(controller()) {
    val cashoutPeriod = cashoutPeriodStub.copy(
      coinGoalMilestones = listOf(10, 20, 30)
    )
    val expectedMilestonesConfig = listOf(
      MilestoneDesc(20000, 6000),
      MilestoneDesc(40000, 6000),
      MilestoneDesc(60000, 10000),
    )

    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriod)
    abTestingService.mock({ isEm3Participant(user.userId) }, true)
    rewardingFacade.mock({ getUserCurrentCoinsBalance(user.userId, user.appPlatform) }, UserCurrentCoinsGoalBalance(4000, 1000, 3000))

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(
      defaultJsonConverter.encodeToString(
        userApiDtoStub.copy(
          coins = 3000,
          coinsBalance = 4000,
          coinsString = "4000",
          coinGoal = 2000,
          coinGoalString = "2000",
          coinGoalBarMode = UserApiDto.CoinGoalBarMode.EM3,
          milestonesConfig = expectedMilestonesConfig,
          nextCashoutTimestamp = cashoutPeriod.periodEnd,
          expLabels = mapOf(
            "mainCoinGoalLabel" to "Play games to earn coins, coin total updated every 5 minutes",
            "mainCoinGoalReachedLabel" to "Play games to earn coins, coin total updated every 5 minutes",
          )
        )
      )
    )
  }

  @Test
  fun `SHOULD return user data with boosted mode ON user call WHEN boosted mode is active`() = withTestApplication(controller()) {
    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriodStub)
    rewardingFacade.mock({ getUserCurrentCoinsBalance(user.userId, user.appPlatform) }, UserCurrentCoinsGoalBalance(2000, 1000, 3000))

    userService.mock(
      { loadCoinGoalUser(USER_ID) }, user.copy(
        createdAt = now.minus(1, ChronoUnit.DAYS),
        expLabels = emptyMap()
      )
    )
    cashoutService.mock(
      { getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) },
      UserCurrencyEarnings(BigDecimal.ONE, Currency.getInstance("USD"), BigDecimal.ONE)
    )

    val boostedMode = boostedModeStub(USER_ID, now)
    boostedModeService.mock({ findCurrentBoostedMode(USER_ID) }, boostedMode)
    boostedModeTranslationService.mock(
      { getAppTranslations(EN_US_LOCALE, boostedMode) }, mapOf(
        "balance_title" to "balance_title_replacement",
      )
    )
    translationService.mock({ tryTranslate("mainScreenHintTranslation", EN_US_LOCALE, USER_ID) }, "mainScreenHintTranslated")

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(
      defaultJsonConverter.encodeToString(
        userApiDtoStub.copy(
          cashoutAvailable = true,
          cashoutAmount = "$1.00",
          expLabels = userApiDtoStub.expLabels + mapOf("balance_title" to "balance_title_replacement"),
          coinGoal = 2000,
          coins = 3000,
          coinsBalance = 2000,
          coinsString = "2000",
          coinGoalString = "2000",
          nextCashoutTimestamp = cashoutPeriodStub.periodEnd,
          boostedMode = UserBoostedModeApiDto(
            coinsAfter = 4000,
            endTime = now + 3.hours,
            hintText = "mainScreenHintTranslated",
            hintId = "presetId",
            colorBadge = "colorBadge",
            colorBadgeEnd = "colorBadgeEnd",
            colorTextBadge = "colorTextBadge",
            colorBack = "colorBack",
            colorBackEnd = "colorBackEnd",
            colorTextCoinsBefore = "colorTextCoinsBefore",
            colorTextCoinsNow = "colorTextCoinsNow"
          )
        )
      )
    )
  }

  @Test
  fun `SHOULD process GET game-stories call`() = withTestApplication(controller()) {
    androidGameStoriesService.mock({ getStories(USER_ID, EN_US_LOCALE) }, androidGameStoriesApiDtoStub)
    val expectedResponse = javaClass.getResource("/user/user-controller/game-stories.json")!!.readText()

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/android/users/$USER_ID/game-stories"
    )
    {
      addHeaders()
    }

    verifyBlocking(androidGameStoriesService) { getStories(USER_ID, EN_US_LOCALE) }
    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertJsonEquals(expectedResponse, response.response.content)
  }

  @Test
  fun `SHOULD return coinsConversionRatioLabelText ON get user data call WHEN user is exp participant`() = withTestApplication(controller()) {
    abTestingService.mock({ isUserExperimentParticipant(USER_ID, ANDROID_SHOW_COINS_CONVERSION_RATIO) }, true)
    cashoutService.mock({ userHasCashouts(USER_ID) }, false)
    abTestingService.mock({ isEm2Participant(USER_ID) }, true)
    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(
      defaultJsonConverter.encodeToString(
        userApiDtoStub.copy(
          coinsConversionRatioLabelText = "<font color=\"0x828282\"><b>880,000c = \$1</b> *Rates may vary based on real revenue.</font>",
        )
      )
    )
  }

  @Test
  fun `SHOULD return null coinsConversionRatioLabelText ON get user data call WHEN user is exp participant and has cashout`() =
    withTestApplication(controller()) {
      abTestingService.mock({ isUserExperimentParticipant(USER_ID, ANDROID_SHOW_COINS_CONVERSION_RATIO) }, true)
      cashoutService.mock({ userHasCashouts(USER_ID) }, true)
      abTestingService.mock({ isEm2Participant(USER_ID) }, true)
      val response = handleRequest(
        method = HttpMethod.Get,
        uri = "/users/$USER_ID"
      ) {
        addHeaders()
      }

      assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
      assertThat(response.response.content).isEqualTo(
        defaultJsonConverter.encodeToString(
          userApiDtoStub.copy(
            useRewards = true,
            coinsConversionRatioLabelText = null,
          )
        )
      )
    }

  @Test
  fun `SHOULD return null coinsConversionRatioLabelText ON get user data call WHEN user is exp participant and EM1 user`() =
    withTestApplication(controller()) {
      abTestingService.mock({ isUserExperimentParticipant(USER_ID, ANDROID_SHOW_COINS_CONVERSION_RATIO) }, true)
      cashoutService.mock({ userHasCashouts(USER_ID) }, false)
      abTestingService.mock({ isEm2Participant(USER_ID) }, false)
      val response = handleRequest(
        method = HttpMethod.Get,
        uri = "/users/$USER_ID"
      ) {
        addHeaders()
      }

      assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
      assertThat(response.response.content).isEqualTo(
        defaultJsonConverter.encodeToString(
          userApiDtoStub.copy(
            coinsConversionRatioLabelText = null,
          )
        )
      )
    }

  @Test
  fun `SHOULD return cashoutAmountBefore ON get user data call WHEN non boosted cashout amount defined`() = withTestApplication(controller()) {
    cashoutService.mock(
      { getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) },
      UserCurrencyEarnings(
        amountUsd = BigDecimal("10.019"),
        nonBoostedAmountUsd = BigDecimal("9.876"),
        userCurrency = Currency.getInstance("USD"),
        userCurrencyAmount = BigDecimal("10.019"),
        nonBoostedUserCurrencyAmount = BigDecimal("9.876"),
      )
    )
    cashoutStatusService.mock({ isCashoutEnabled(user.userId) }, true)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/users/$USER_ID"
    ) {
      addHeaders()
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(
      defaultJsonConverter.encodeToString(
        userApiDtoStub.copy(
          cashoutAvailable = true,
          cashoutAmount = "$10.01",
          cashoutAmountBefore = "$9.87",
        )
      )
    )
  }

  private fun TestApplicationRequest.addHeaders(appVersion: Int = UserControllerTest.appVersion.version, locale: String = "en-US") {
    addHeader("Content-Type", "application/json")
    addHeader(ANDROID_APP_VERSION_HEADER, appVersion.toString())
    addHeader(COUNTRY_HEADER, COUNTRY_CODE)
    addHeader(IP_HEADER, IP)
    addHeader("X-VSession", SESSION_ID)
    addHeader(HttpHeaders.AcceptLanguage, locale)
  }

  private val cashoutPeriodStub = CashoutPeriodDto(
    userId = USER_ID,
    periodStart = now.minus(12, ChronoUnit.HOURS),
    periodEnd = now.plus(12, ChronoUnit.HOURS),
    coinGoal = 1,
    counter = 3,
    noEarningsCounter = 0,
    coinGoalMilestones = emptyList(),
  )

  private val userApiDtoStub = UserApiDto(
    user = user,
    coinsBalance = UserCurrentCoinsGoalBalance(0, 0, 0),
    cashoutAvailable = false,
    cashoutAmount = "",
    nextCashoutTimestamp = null,
    useRewards = false,
    timestamp = now,
    tutorialSteps = emptyList(),
    videoAdIntervalSeconds = null,
    showTimerInCoinGoalSection = false,
    threeDotMenuItems = threeDotMenuItemsMock(),
    market = "test-market",
    attestationRequired = false,
    initializeApplovin = true,
    consentedToAnalytics = true,
    coinGoal = 0,
    coinGoalReached = true,
    giftBoxInsteadOfEarnings = false,
    cashoutButtonStyle = null,
    cashoutTimerSubtext = "$$$ Boosted",
    cashoutProgressBarMode = null,
    paymentProviderAvailable = false,
    useAmplitudeAnalytics = false,
    paymentProviderSurvey = null,
    incompleteCashoutRestoringMode = null,
    cashoutBonusCoins = null,
    faceScanPreScreen = null,
    privacyRegulation = null,
    coinGoalBarMode = null,
    milestonesConfig = emptyList(),
    cashoutPeriodId = "userId:3"
  )

  private fun threeDotMenuItemsMock() = listOf(
    ThreeDotMenuItem(ThreeDotMenuActionApiDto.routeToRewards(), "My Rewards"),
    ThreeDotMenuItem(ThreeDotMenuActionApiDto.contactUs(), "Help"),
    ThreeDotMenuItem(ThreeDotMenuActionApiDto.openLinkInPopUp("https://justplayapps.com/privacy-policy/"), "Privacy policy"),
    ThreeDotMenuItem(ThreeDotMenuActionApiDto.openLinkInPopUp("https://justplayapps.com/loyalty-program-rules/"), "Loyalty Program Rules"),
    ThreeDotMenuItem(ThreeDotMenuActionApiDto.tutorialHub(), "Tutorials"),
    ThreeDotMenuItem(ThreeDotMenuActionApiDto.accountDeletion(), "Request Account Deletion"),
  )
}
