package com.moregames.playtime.user.gamerank.calculators

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.messaging.dto.UserChallengeProgressDto
import com.moregames.base.util.ApplicationId
import com.moregames.playtime.user.challenge.progress.achievement.AchievementDto
import com.moregames.playtime.user.gamerank.GameRank
import com.moregames.playtime.user.gamerank.GameRankCalculator
import com.moregames.playtime.user.gamerank.GameRankConfig
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class LevelIdGameRankProgressCalculatorTest {

  private val underTest = LevelIdGameRankProgressCalculator()

  companion object {
    const val USER_ID = "test-user-id"

    val config = GameRankConfig(
      oneStarProgressMax = 10,
      twoStarProgressMax = 30,
      threeStarProgressMax = 100,
      calculator = GameRankCalculator.LEVEL_ID
    )
  }

  @Test
  fun `SHOULD return GameRank THREE with max progress WHEN current progress equals progressMax`() {
    val progressDto = UserChallengeProgressDto.LevelIdProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      levelId = "level_50"
    )
    val currentAchievement = AchievementDto(setOf("level_1", "level_2"))

    val result = underTest.calculateProgress(progressDto, config.progressMax, currentAchievement, config)

    assertThat(result.rank).isEqualTo(GameRank.THREE)
    assertThat(result.progress).isEqualTo(config.progressMax)
    assertThat(result.achievement).isEqualTo(currentAchievement)
  }

  @Test
  fun `SHOULD return previous achievement WHEN levels exceed MAX_LEVELS`() {
    val progressDto = UserChallengeProgressDto.LevelIdProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      levelId = "level_501"
    )
    // Create achievement with MAX_LEVELS levels to trigger the limit
    val largeLevelsSet = (1..500).map { "level_$it" }.toSet()
    val currentAchievement = AchievementDto(largeLevelsSet)

    val result = underTest.calculateProgress(progressDto, 10, currentAchievement, config)

    assertThat(result.rank).isEqualTo(GameRank.THREE)
    assertThat(result.progress).isEqualTo(config.progressMax)
    assertThat(result.achievement).isEqualTo(currentAchievement)
  }

  @Test
  fun `SHOULD handle null currentAchievement correctly`() {
    val progressDto = UserChallengeProgressDto.LevelIdProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      levelId = "level_15"
    )

    val result = underTest.calculateProgress(progressDto, 0, null, config)

    assertThat(result.rank).isEqualTo(GameRank.ZERO) // 1 level -> rank ZERO (< 10)
    assertThat(result.progress).isEqualTo(1) // 1 level from levelId
    assertThat(result.achievement).isEqualTo(AchievementDto(setOf("level_15")))
  }

  @Test
  fun `SHOULD union previous and new achievements`() {
    val progressDto = UserChallengeProgressDto.LevelIdProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      levelId = "level_25"
    )
    val currentAchievement = AchievementDto(setOf("level_10", "level_20"))

    val result = underTest.calculateProgress(progressDto, 2, currentAchievement, config)

    assertThat(result.rank).isEqualTo(GameRank.ZERO) // 3 levels -> rank ZERO (< 10)
    assertThat(result.progress).isEqualTo(3) // 3 levels total: level_10, level_20, level_25
    assertThat(result.achievement).isEqualTo(AchievementDto(setOf("level_10", "level_20", "level_25")))
  }

  @ParameterizedTest
  @CsvSource(
    "9, ZERO",     // 9 levels -> rank ZERO (< 10)
    "10, ONE",     // 10 levels -> rank ONE (>= 10, < 30)
    "15, ONE",     // 15 levels -> rank ONE (>= 10, < 30)
    "29, ONE",     // 29 levels -> rank ONE (>= 10, < 30)
    "30, TWO",     // 30 levels -> rank TWO (>= 30, < 100)
    "50, TWO",     // 50 levels -> rank TWO (>= 30, < 100)
    "99, TWO",     // 99 levels -> rank TWO (>= 30, < 100)
    "100, THREE"   // 100 levels -> rank THREE (>= 100)
  )
  fun `SHOULD return correct rank based on number of achievement levels`(
    numLevels: Int,
    expectedRank: GameRank
  ) {
    // Create achievement with specific number of levels
    val levels = (1..numLevels).map { "level_$it" }.toSet()
    val currentAchievement = AchievementDto(levels)

    val progressDto = UserChallengeProgressDto.LevelIdProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      levelId = "level_1" // Level that already exists in achievement
    )

    val result = underTest.calculateProgress(progressDto, numLevels, currentAchievement, config)

    assertThat(result.rank).isEqualTo(expectedRank)
    assertThat(result.progress).isEqualTo(numLevels)
  }

  @Test
  fun `SHOULD cap progress at progressMax`() {
    val progressDto = UserChallengeProgressDto.LevelIdProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      levelId = "level_150"
    )
    // Create achievement that would result in more than progressMax levels
    val largeLevelsSet = (1..120).map { "level_$it" }.toSet()
    val currentAchievement = AchievementDto(largeLevelsSet)

    val result = underTest.calculateProgress(progressDto, 50, currentAchievement, config)

    assertThat(result.rank).isEqualTo(GameRank.THREE)
    assertThat(result.progress).isEqualTo(config.progressMax) // Should be capped at 100
  }

  @Test
  fun `SHOULD return empty achievement WHEN progressDto is not LevelIdProgressDto`() {
    val progressDto = UserChallengeProgressDto.MilestoneProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      milestone = 50
    )

    val result = underTest.calculateProgress(progressDto, 5, null, config)

    assertThat(result.rank).isEqualTo(GameRank.ZERO)
    assertThat(result.progress).isEqualTo(0) // No new achievement, so no progress change
    assertThat(result.achievement).isEqualTo(AchievementDto.empty())
  }

  @Test
  fun `SHOULD handle ScoreProgressDto correctly`() {
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      score = 1000
    )

    val result = underTest.calculateProgress(progressDto, 5, null, config)

    assertThat(result.rank).isEqualTo(GameRank.ZERO)
    assertThat(result.progress).isEqualTo(0) // No new achievement, so no progress change
    assertThat(result.achievement).isEqualTo(AchievementDto.empty())
  }

  @Test
  fun `SHOULD preserve existing achievement when no new levelId`() {
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      score = 100
    )
    val currentAchievement = AchievementDto(setOf("level_10", "level_20"))

    val result = underTest.calculateProgress(progressDto, 2, currentAchievement, config)

    assertThat(result.rank).isEqualTo(GameRank.ZERO) // 2 levels -> rank ZERO (< 10)
    assertThat(result.progress).isEqualTo(2) // Same as current progress since no new achievement
    assertThat(result.achievement).isEqualTo(currentAchievement)
  }

  @Test
  fun `SHOULD handle level progression correctly`() {
    // Start with existing achievement
    val currentAchievement =
      AchievementDto(setOf("level_1", "level_2", "level_3", "level_4", "level_5", "level_6", "level_7", "level_8", "level_9")) // 9 levels

    val progressDto = UserChallengeProgressDto.LevelIdProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      levelId = "level_10" // Add 10th level
    )

    val result = underTest.calculateProgress(progressDto, 9, currentAchievement, config)

    assertThat(result.rank).isEqualTo(GameRank.ONE) // 10 levels -> rank ONE (>= 10, < 30)
    assertThat(result.progress).isEqualTo(10) // 10 levels total
    assertThat(result.achievement).isEqualTo(
      AchievementDto(
        setOf(
          "level_1",
          "level_2",
          "level_3",
          "level_4",
          "level_5",
          "level_6",
          "level_7",
          "level_8",
          "level_9",
          "level_10"
        )
      )
    )
  }

  @Test
  fun `SHOULD handle duplicate levelId correctly`() {
    val progressDto = UserChallengeProgressDto.LevelIdProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      levelId = "level_10" // Level that already exists
    )
    val currentAchievement = AchievementDto(setOf("level_10", "level_20"))

    val result = underTest.calculateProgress(progressDto, 2, currentAchievement, config)

    assertThat(result.rank).isEqualTo(GameRank.ZERO) // 2 levels -> rank ZERO (< 10)
    assertThat(result.progress).isEqualTo(2) // No change in level count due to duplicate
    assertThat(result.achievement).isEqualTo(AchievementDto(setOf("level_10", "level_20")))
  }

  @Test
  fun `SHOULD handle numeric levelId correctly`() {
    val progressDto = UserChallengeProgressDto.LevelIdProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      levelId = "123"
    )

    val result = underTest.calculateProgress(progressDto, 0, null, config)

    assertThat(result.rank).isEqualTo(GameRank.ZERO) // 1 level -> rank ZERO (< 10)
    assertThat(result.progress).isEqualTo(1) // 1 level from levelId
    assertThat(result.achievement).isEqualTo(AchievementDto(setOf("123")))
  }

  @Test
  fun `SHOULD handle special character levelId correctly`() {
    val progressDto = UserChallengeProgressDto.LevelIdProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      levelId = "level-special_123@test"
    )

    val result = underTest.calculateProgress(progressDto, 0, null, config)

    assertThat(result.rank).isEqualTo(GameRank.ZERO) // 1 level -> rank ZERO (< 10)
    assertThat(result.progress).isEqualTo(1) // 1 level from levelId
    assertThat(result.achievement).isEqualTo(AchievementDto(setOf("level-special_123@test")))
  }

  @ParameterizedTest
  @CsvSource(
    "level_5, ZERO",    // 1 level -> rank ZERO (< 10)
    "level_10, ZERO",   // 1 level -> rank ZERO (< 10)
    "level_25, ZERO",   // 1 level -> rank ZERO (< 10)
    "level_30, ZERO",   // 1 level -> rank ZERO (< 10)
    "level_50, ZERO",   // 1 level -> rank ZERO (< 10)
    "level_100, ZERO",  // 1 level -> rank ZERO (< 10)
    "level_150, ZERO"   // 1 level -> rank ZERO (< 10)
  )
  fun `SHOULD return correct rank based on single levelId progress`(
    levelId: String,
    expectedRank: GameRank
  ) {
    val progressDto = UserChallengeProgressDto.LevelIdProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      levelId = levelId
    )

    val result = underTest.calculateProgress(progressDto, 0, null, config)

    assertThat(result.rank).isEqualTo(expectedRank)
    assertThat(result.progress).isEqualTo(1) // Always 1 level from single levelId
  }

  @Test
  fun `SHOULD handle empty string levelId correctly`() {
    val progressDto = UserChallengeProgressDto.LevelIdProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      levelId = ""
    )

    val result = underTest.calculateProgress(progressDto, 0, null, config)

    assertThat(result.rank).isEqualTo(GameRank.ZERO) // 1 level -> rank ZERO (< 10)
    assertThat(result.progress).isEqualTo(1) // 1 level from empty levelId
    assertThat(result.achievement).isEqualTo(AchievementDto(setOf("")))
  }

  @Test
  fun `SHOULD handle AmountMilestoneProgressDto correctly`() {
    val progressDto = UserChallengeProgressDto.AmountMilestoneProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      amount = 5,
      milestone = 10
    )

    val result = underTest.calculateProgress(progressDto, 5, null, config)

    assertThat(result.rank).isEqualTo(GameRank.ZERO)
    assertThat(result.progress).isEqualTo(0) // No new achievement, so no progress change
    assertThat(result.achievement).isEqualTo(AchievementDto.empty())
  }

  @Test
  fun `SHOULD handle TmProgressDto correctly`() {
    val progressDto = UserChallengeProgressDto.TmProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      score = 1000,
      isBoss = false
    )

    val result = underTest.calculateProgress(progressDto, 5, null, config)

    assertThat(result.rank).isEqualTo(GameRank.ZERO)
    assertThat(result.progress).isEqualTo(0) // No new achievement, so no progress change
    assertThat(result.achievement).isEqualTo(AchievementDto.empty())
  }

  @Test
  fun `SHOULD handle rank transition from ZERO to ONE`() {
    // Start with 9 levels (rank ZERO)
    val currentAchievement = AchievementDto((1..9).map { "level_$it" }.toSet())

    val progressDto = UserChallengeProgressDto.LevelIdProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      levelId = "level_10" // This should push to rank ONE
    )

    val result = underTest.calculateProgress(progressDto, 9, currentAchievement, config)

    assertThat(result.rank).isEqualTo(GameRank.ONE) // 10 levels -> rank ONE
    assertThat(result.progress).isEqualTo(10)
    assertThat(result.achievement?.levels?.size).isEqualTo(10)
  }

  @Test
  fun `SHOULD handle rank transition from ONE to TWO`() {
    // Start with 29 levels (rank ONE)
    val currentAchievement = AchievementDto((1..29).map { "level_$it" }.toSet())

    val progressDto = UserChallengeProgressDto.LevelIdProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      levelId = "level_30" // This should push to rank TWO
    )

    val result = underTest.calculateProgress(progressDto, 29, currentAchievement, config)

    assertThat(result.rank).isEqualTo(GameRank.TWO) // 30 levels -> rank TWO
    assertThat(result.progress).isEqualTo(30)
    assertThat(result.achievement?.levels?.size).isEqualTo(30)
  }

  @Test
  fun `SHOULD handle rank transition from TWO to THREE`() {
    // Start with 99 levels (rank TWO)
    val currentAchievement = AchievementDto((1..99).map { "level_$it" }.toSet())

    val progressDto = UserChallengeProgressDto.LevelIdProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      levelId = "level_100" // This should push to rank THREE
    )

    val result = underTest.calculateProgress(progressDto, 99, currentAchievement, config)

    assertThat(result.rank).isEqualTo(GameRank.THREE) // 100 levels -> rank THREE
    assertThat(result.progress).isEqualTo(100)
    assertThat(result.achievement?.levels?.size).isEqualTo(100)
  }

  @Test
  fun `SHOULD handle very large levelId values correctly`() {
    val progressDto = UserChallengeProgressDto.LevelIdProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      levelId = "level_999999999"
    )

    val result = underTest.calculateProgress(progressDto, 0, null, config)

    assertThat(result.rank).isEqualTo(GameRank.ZERO) // 1 level -> rank ZERO (< 10)
    assertThat(result.progress).isEqualTo(1) // 1 level from levelId
    assertThat(result.achievement).isEqualTo(AchievementDto(setOf("level_999999999")))
  }
}