package com.moregames.playtime.user.gamerank

import assertk.assertThat
import assertk.assertions.isEqualTo
import assertk.assertions.isNull
import com.moregames.base.table.DatabaseExtension
import com.moregames.base.util.prepareUser
import kotlinx.coroutines.runBlocking
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(DatabaseExtension::class)
class UserGameRankPersistenceServiceTest(private val database: Database) {

  private val underTest = UserGameRankPersistenceService(database)

  companion object {
    private const val GAME_ID = 12345
    private const val PROGRESS_VALUE = 50
    private const val ACHIEVEMENT_VALUE = "level_10_completed"
  }

  @Test
  fun `SHOULD return default entity WHEN no record exists`() {
    val userId = database.prepareUser()

    val result = runBlocking {
      underTest.getUserGameRankEntity(userId, GAME_ID)
    }

    assertThat(result.userId).isEqualTo(userId)
    assertThat(result.gameId).isEqualTo(GAME_ID)
    assertThat(result.rank).isEqualTo(GameRank.ZERO)
    assertThat(result.progress).isEqualTo(0)
    assertThat(result.achievement).isNull()
  }

  @Test
  fun `SHOULD return existing entity WHEN record exists`() {
    val userId = database.prepareUser()

    // Insert test data directly into database
    transaction(database) {
      UserGameRankTable.insert {
        it[UserGameRankTable.userId] = userId
        it[gameId] = GAME_ID
        it[rank] = GameRank.TWO
        it[progress] = PROGRESS_VALUE
        it[achievement] = ACHIEVEMENT_VALUE
      }
    }

    val result = runBlocking {
      underTest.getUserGameRankEntity(userId, GAME_ID)
    }

    assertThat(result.userId).isEqualTo(userId)
    assertThat(result.gameId).isEqualTo(GAME_ID)
    assertThat(result.rank).isEqualTo(GameRank.TWO)
    assertThat(result.progress).isEqualTo(PROGRESS_VALUE)
    assertThat(result.achievement).isEqualTo(ACHIEVEMENT_VALUE)
  }

  @Test
  fun `SHOULD save new entity WHEN no record exists`() {
    val userId = database.prepareUser()
    val entity = UserGameRankEntity(
      userId = userId,
      gameId = GAME_ID,
      rank = GameRank.ONE,
      progress = PROGRESS_VALUE,
      achievement = ACHIEVEMENT_VALUE
    )

    runBlocking {
      underTest.saveUserGameRank(entity)
    }

    // Verify data was saved correctly
    transaction(database) {
      val savedRecord = UserGameRankTable.select {
        (UserGameRankTable.userId eq userId) and (UserGameRankTable.gameId eq GAME_ID)
      }.single()

      assertThat(savedRecord[UserGameRankTable.userId]).isEqualTo(userId)
      assertThat(savedRecord[UserGameRankTable.gameId]).isEqualTo(GAME_ID)
      assertThat(savedRecord[UserGameRankTable.rank]).isEqualTo(GameRank.ONE)
      assertThat(savedRecord[UserGameRankTable.progress]).isEqualTo(PROGRESS_VALUE)
      assertThat(savedRecord[UserGameRankTable.achievement]).isEqualTo(ACHIEVEMENT_VALUE)
    }
  }

  @Test
  fun `SHOULD update existing entity WHEN record already exists`() {
    val userId = database.prepareUser()

    // Insert initial data
    transaction(database) {
      UserGameRankTable.insert {
        it[UserGameRankTable.userId] = userId
        it[gameId] = GAME_ID
        it[rank] = GameRank.ZERO
        it[progress] = 10
        it[achievement] = "initial_achievement"
      }
    }

    val updatedEntity = UserGameRankEntity(
      userId = userId,
      gameId = GAME_ID,
      rank = GameRank.THREE,
      progress = 100,
      achievement = "final_achievement"
    )

    runBlocking {
      underTest.saveUserGameRank(updatedEntity)
    }

    // Verify data was updated correctly
    transaction(database) {
      val updatedRecord = UserGameRankTable.select {
        (UserGameRankTable.userId eq userId) and (UserGameRankTable.gameId eq GAME_ID)
      }.single()

      assertThat(updatedRecord[UserGameRankTable.userId]).isEqualTo(userId)
      assertThat(updatedRecord[UserGameRankTable.gameId]).isEqualTo(GAME_ID)
      assertThat(updatedRecord[UserGameRankTable.rank]).isEqualTo(GameRank.THREE)
      assertThat(updatedRecord[UserGameRankTable.progress]).isEqualTo(100)
      assertThat(updatedRecord[UserGameRankTable.achievement]).isEqualTo("final_achievement")
    }
  }

  @Test
  fun `SHOULD handle null achievement WHEN saving entity`() {
    val userId = database.prepareUser()
    val entity = UserGameRankEntity(
      userId = userId,
      gameId = GAME_ID,
      rank = GameRank.TWO,
      progress = PROGRESS_VALUE,
      achievement = null
    )

    runBlocking {
      underTest.saveUserGameRank(entity)
    }

    // Verify null achievement was saved correctly
    transaction(database) {
      val savedRecord = UserGameRankTable.select {
        (UserGameRankTable.userId eq userId) and (UserGameRankTable.gameId eq GAME_ID)
      }.single()

      assertThat(savedRecord[UserGameRankTable.achievement]).isNull()
    }
  }

  @Test
  fun `SHOULD handle different game IDs for same user`() {
    val userId = database.prepareUser()
    val gameId1 = 111
    val gameId2 = 222

    val entity1 = UserGameRankEntity(userId, gameId1, GameRank.ONE, 25, "game1_achievement")
    val entity2 = UserGameRankEntity(userId, gameId2, GameRank.TWO, 75, "game2_achievement")

    runBlocking {
      underTest.saveUserGameRank(entity1)
      underTest.saveUserGameRank(entity2)
    }

    val result1 = runBlocking { underTest.getUserGameRankEntity(userId, gameId1) }
    val result2 = runBlocking { underTest.getUserGameRankEntity(userId, gameId2) }

    assertThat(result1.rank).isEqualTo(GameRank.ONE)
    assertThat(result1.progress).isEqualTo(25)
    assertThat(result1.achievement).isEqualTo("game1_achievement")

    assertThat(result2.rank).isEqualTo(GameRank.TWO)
    assertThat(result2.progress).isEqualTo(75)
    assertThat(result2.achievement).isEqualTo("game2_achievement")
  }
}