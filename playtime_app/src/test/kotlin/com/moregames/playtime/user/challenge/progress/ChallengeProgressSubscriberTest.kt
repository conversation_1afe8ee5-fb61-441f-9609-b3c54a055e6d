package com.moregames.playtime.user.challenge.progress

import com.moregames.base.messaging.dto.UserChallengeProgressDto
import com.moregames.base.util.ApplicationId
import com.moregames.base.util.mock
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.gamerank.UserGameRankProgressService
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking

class ChallengeProgressSubscriberTest {
  private val challengeProgressService: ChallengeProgressService = mock()
  private val userService: UserService = mock()
  private val userGameRankProgressService: UserGameRankProgressService = mock()

  private val underTest = ChallengeProgressSubscriber(challengeProgressService, userService, userGameRankProgressService)

  @Test
  fun `SHOULD call challenge progress service`() {
    userService.mock({ userExists("userId") }, true)

    val dto = UserChallengeProgressDto.ScoreProgressDto(
      userId = "userId",
      applicationId = ApplicationId.TREASURE_MASTER_APP_ID,
      score = 12,
    )
    runBlocking { underTest.handle(dto) }
    verifyBlocking(challengeProgressService) { handleUserChallengeProgress(dto) }
    verifyBlocking(userGameRankProgressService) { handleUserProgress(dto) }
  }
}