package com.moregames.playtime.user

import com.justplayapps.base.Common
import com.justplayapps.playtime.proto.em3CoinsRevenueCheckMessage
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.AbTestingService.IsExperimentParticipant.No
import com.moregames.base.abtesting.AbTestingService.IsExperimentParticipant.Yes
import com.moregames.base.abtesting.Variations.EM2_BOILED_FROG_SOFT
import com.moregames.base.bus.MessageBus
import com.moregames.base.coins.UserCurrentCoinsGoalBalance
import com.moregames.base.dto.AppPlatform
import com.moregames.base.dto.AppPlatform.ANDROID
import com.moregames.base.exceptions.UserRecordNotFoundException
import com.moregames.base.messaging.UserBalanceUpdateDto
import com.moregames.base.messaging.commands.CommandIdContextElement
import com.moregames.base.util.mock
import com.moregames.base.util.throwException
import com.moregames.playtime.buseffects.PushNotificationEffect
import com.moregames.playtime.coins.UserCoinsAddedEventHandler.Em3UserGameProgressUpdatedEffect
import com.moregames.playtime.notifications.NotificationType.RATING_PROMPT
import com.moregames.playtime.notifications.PushNotification.CrossPlatformPushNotification.RatingPromptCommand
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.kotlin.*


class PlaytimeBalanceUpdateManagerTest {

  private val userService: UserService = mock {
    onBlocking { hasPreviousNotification(any(), eq(RATING_PROMPT)) } doReturn false
  }
  private val messageBus: MessageBus = mock()
  private val abTestingService: AbTestingService = mock {
    onBlocking { isEm3Participant(userDtoStub.id) } doReturn false
  }
  private val rewardingFacade: RewardingFacade = mock()

  private val underTest = PlaytimeBalanceUpdateManager(
    userService = userService,
    messageBus = messageBus,
    abTestingService = abTestingService,
    rewardingFacade = rewardingFacade,
  )

  @Test
  fun `SHOULD send balance update ON user balance update`() {
    val user = mock<UserDto> {
      whenever(it.id).thenReturn("userId")
      whenever(it.appPlatform).thenReturn(ANDROID)
    }
    rewardingFacade.mock({ getUserCurrentCoinsBalance("userId", ANDROID) }, UserCurrentCoinsGoalBalance(700, 100, 0))
    userService.mock({ getUser(user.id) }, user)

    runBlocking {
      underTest.onUserBalanceUpdate(UserBalanceUpdateDto(user.id, 200))
    }

    verifyBlocking(messageBus) { publishAsync(PushNotificationEffect(RatingPromptCommand("userId"))) }
    verifyNoMoreInteractions(messageBus)
    verifyBlocking(userService) { trackNotification("userId", RATING_PROMPT) }
  }

  @Test
  fun `SHOULD send balance update ON user balance update WHEN em2 coins`() {
    val user = mock<UserDto> {
      whenever(it.id).thenReturn("userId")
      whenever(it.appPlatform).thenReturn(ANDROID)
    }
    rewardingFacade.mock({ getUserCurrentCoinsBalance("userId", ANDROID) }, UserCurrentCoinsGoalBalance(700, 100, 0))
    userService.mock({ getUser(user.id) }, user)

    runBlocking {
      underTest.onUserBalanceUpdate(UserBalanceUpdateDto(user.id, coinsEarned = 0, coinsEarnedEm2 = 200.toBigDecimal()))
    }
    
    verifyBlocking(messageBus) { publishAsync(PushNotificationEffect(RatingPromptCommand("userId"))) }
    verifyNoMoreInteractions(messageBus)
    verifyBlocking(userService) { trackNotification("userId", RATING_PROMPT) }
  }

  @Test
  fun `SHOULD send balance update ON user balance update WHEN em3 coins`() {
    val user = mock<UserDto> {
      whenever(it.id).thenReturn("userId")
      whenever(it.appPlatform).thenReturn(ANDROID)
    }
    rewardingFacade.mock({ getUserCurrentCoinsBalance("userId", ANDROID) }, UserCurrentCoinsGoalBalance(700, 100, 0))
    userService.mock({ getUser(user.id) }, user)
    abTestingService.mock({ isEm3Participant(user.id) }, true)

    runBlocking {
      underTest.onUserBalanceUpdate(UserBalanceUpdateDto(user.id, coinsEarned = 0, coinsEarnedEm2 = 200.toBigDecimal()))
    }
    
    verifyBlocking(messageBus) { publishAsync(PushNotificationEffect(RatingPromptCommand("userId"))) }
    verifyBlocking(messageBus) {
      publish(
        em3CoinsRevenueCheckMessage {
          userId = "userId"
          platform = Common.AppPlatformProto.ANDROID
        }
      )
    }
    verifyBlocking(messageBus) { publishAsync(Em3UserGameProgressUpdatedEffect("userId", null, false, null)) }
    verifyNoMoreInteractions(messageBus)
  }

  @Test
  fun `SHOULD send balance update ON user balance update WHEN em3 coins AND zero coins balance`() {
    val user = mock<UserDto> {
      whenever(it.id).thenReturn("userId")
      whenever(it.appPlatform).thenReturn(ANDROID)
    }
    rewardingFacade.mock({ getUserCurrentCoinsBalance("userId", ANDROID) }, UserCurrentCoinsGoalBalance(0, 100, 0))
    
    userService.mock({ getUser(user.id) }, user)
    abTestingService.mock({ isEm3Participant(user.id) }, true)

    runBlocking {
      underTest.onUserBalanceUpdate(UserBalanceUpdateDto(user.id, coinsEarned = 0, coinsEarnedEm2 = 200.toBigDecimal()))
    }

    verifyBlocking(messageBus) { publishAsync(PushNotificationEffect(RatingPromptCommand("userId"))) }
    verifyBlocking(messageBus) {
      publish(
        em3CoinsRevenueCheckMessage {
          userId = "userId"
          platform = Common.AppPlatformProto.ANDROID
        }
      )
    }
    verifyBlocking(messageBus) { publishAsync(Em3UserGameProgressUpdatedEffect("userId", null, false, null)) }
    verifyNoMoreInteractions(messageBus)
    verifyBlocking(userService) { trackNotification("userId", RATING_PROMPT) }
  }

  @Test
  fun `SHOULD send balance update ON user balance update WHEN em3 coins AND isInGameBalance`() {
    val user = mock<UserDto> {
      whenever(it.id).thenReturn("userId")
      whenever(it.appPlatform).thenReturn(ANDROID)
    }
    rewardingFacade.mock({ getUserCurrentCoinsBalance("userId", ANDROID) }, UserCurrentCoinsGoalBalance(700, 100, 0))
    userService.mock({ getUser(user.id) }, user)
    abTestingService.mock({ isEm3Participant(user.id) }, true)
    abTestingService.mock({ isGameBalanceUpdateNotificationParticipant(user.id, ANDROID, true) }, true)

    runBlocking(CommandIdContextElement("commandId")) {
      underTest.onUserBalanceUpdate(UserBalanceUpdateDto(user.id, 200, commandId = "commandId"))
    }

    verifyBlocking(messageBus) { publishAsync(PushNotificationEffect(RatingPromptCommand("userId"))) }
    verifyBlocking(messageBus) {
      publish(
        em3CoinsRevenueCheckMessage {
          userId = "userId"
          platform = Common.AppPlatformProto.ANDROID
        }
      )
    }
    verifyBlocking(messageBus) { publishAsync(Em3UserGameProgressUpdatedEffect("userId", "commandId", false, null)) }
    verifyNoMoreInteractions(messageBus)
    verifyBlocking(userService) { trackNotification("userId", RATING_PROMPT) }
  }

  @Test
  fun `SHOULD send balance update ON user balance update WHEN em3 coins AND hasActiveOffers`() {
    val user = mock<UserDto> {
      whenever(it.id).thenReturn("userId")
      whenever(it.appPlatform).thenReturn(ANDROID)
    }
    rewardingFacade.mock({ getUserCurrentCoinsBalance("userId", ANDROID) }, UserCurrentCoinsGoalBalance(700, 100, 0))

    userService.mock({ getUser(user.id) }, user)
    abTestingService.mock({ isEm3Participant(user.id) }, true)

    runBlocking {
      underTest.onUserBalanceUpdate(UserBalanceUpdateDto(user.id, gameId = 42, coinsEarned = 0, coinsEarnedEm2 = 200.toBigDecimal(), forceCommandNotification = true))
    }

    verifyBlocking(messageBus) { publishAsync(PushNotificationEffect(RatingPromptCommand("userId"))) }
    verifyBlocking(messageBus) {
      publish(
        em3CoinsRevenueCheckMessage {
          userId = "userId"
          platform = Common.AppPlatformProto.ANDROID
        }
      )
    }
    verifyBlocking(messageBus) { publishAsync(Em3UserGameProgressUpdatedEffect("userId", null, true, 42)) }
    verifyNoMoreInteractions(messageBus)
    verifyBlocking(userService) { trackNotification("userId", RATING_PROMPT) }
  }

  @Test
  fun `SHOULD send balance update via effect bus ON user balance update`() {
    val user = mock<UserDto> {
      whenever(it.id).thenReturn("userId")
      whenever(it.appPlatform).thenReturn(ANDROID)
    }
    rewardingFacade.mock({ getUserCurrentCoinsBalance("userId", ANDROID) }, UserCurrentCoinsGoalBalance(700, 100, 0))

    abTestingService.mock({ isGameBalanceUpdateNotificationParticipant(user.id, ANDROID, true) }, true)
    userService.mock({ getUser(user.id) }, user)

    runBlocking(CommandIdContextElement("commandId")) {
      underTest.onUserBalanceUpdate(UserBalanceUpdateDto(user.id, 200, commandId = "commandId"))
    }

    verifyBlocking(messageBus) { publishAsync(PushNotificationEffect(RatingPromptCommand("userId"))) }
    verifyBlocking(userService) { trackNotification("userId", RATING_PROMPT) }
    verifyNoMoreInteractions(messageBus)
  }

  @Test
  fun `SHOULD cashout offer send balance update via effect bus ON user balance update when user has active cashout offer`() {
    val user = mock<UserDto> {
      whenever(it.id).thenReturn("userId")
      whenever(it.appPlatform).thenReturn(ANDROID)
    }
    rewardingFacade.mock({ getUserCurrentCoinsBalance("userId", ANDROID) }, UserCurrentCoinsGoalBalance(700, 100, 0))

    abTestingService.mock({ isGameBalanceUpdateNotificationParticipant(user.id, ANDROID, true) }, false)
    userService.mock({ getUser(user.id) }, user)

    runBlocking {
      underTest.onUserBalanceUpdate(UserBalanceUpdateDto(user.id, 200, commandId = "commandId", gameId = 42))
    }

    verifyBlocking(messageBus) { publishAsync(PushNotificationEffect(RatingPromptCommand("userId"))) }
    verifyBlocking(userService) { trackNotification("userId", RATING_PROMPT) }
    verifyNoMoreInteractions(messageBus)
  }

  @Test
  fun `SHOULD send balance update via effect bus ON user balance update WHEN user is not exp participant but force flag is true`() {
    val user = mock<UserDto> {
      whenever(it.id).thenReturn("userId")
      whenever(it.appPlatform).thenReturn(ANDROID)
    }
    rewardingFacade.mock({ getUserCurrentCoinsBalance("userId", ANDROID) }, UserCurrentCoinsGoalBalance(700, 100, 0))

    abTestingService.mock({ isGameBalanceUpdateNotificationParticipant(user.id, ANDROID, true) }, false)
    userService.mock({ getUser(user.id) }, user)

    runBlocking(CommandIdContextElement("commandId")) {
      underTest.onUserBalanceUpdate(UserBalanceUpdateDto(user.id, 200, commandId = "commandId", forceCommandNotification = true))
    }

    verifyBlocking(messageBus) { publishAsync(PushNotificationEffect(RatingPromptCommand("userId"))) }
    verifyBlocking(userService) { trackNotification("userId", RATING_PROMPT) }
    verifyNoMoreInteractions(messageBus)
  }

  @Test
  fun `SHOULD NOT send balance update ON user balance update WHEN user balance is non-positive`() {
    val user = mock<UserDto> {
      whenever(it.id).thenReturn("userId")
      whenever(it.appPlatform).thenReturn(ANDROID)
    }
    rewardingFacade.mock({ getUserCurrentCoinsBalance("userId", ANDROID) }, UserCurrentCoinsGoalBalance(0, 0, 0))

    userService.mock({ getUser(user.id) }, user)

    runBlocking {
      underTest.onUserBalanceUpdate(UserBalanceUpdateDto(user.id, 200))
    }

    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD NOT send balance update ON user balance update WHEN users is not found`() {
    val userId = "userId"
    userService.throwException({ getUser(userId) }, UserRecordNotFoundException(userId))

    runBlocking {
      underTest.onUserBalanceUpdate(UserBalanceUpdateDto(userId, 200))
    }

    verifyNoInteractions(messageBus)
  }

  @ParameterizedTest
  @CsvSource(
    "IOS,0,false",
    "IOS,100,false",
    "IOS,0,false",
    "IOS,100,false",
    "IOS,0,false",
    "IOS,100,false",
    "ANDROID,100,true",
    "ANDROID,0,false",
    "ANDROID,100,true",
  )
  fun `SHOULD send rating prompt notification ON onUserBalanceUpdate WHEN depends on conditions`(
    appPlatform: AppPlatform, gameCoins: Int, notificationShouldBeSent: Boolean
  ) {
    val user = userDtoStub.copy(
      appPlatform = appPlatform,
    )
    rewardingFacade.mock({ getUserCurrentCoinsBalance("userId", appPlatform) }, UserCurrentCoinsGoalBalance(700, gameCoins, 0))

    userService.mock({ getUser(user.id) }, user)

    runBlocking {
      underTest.onUserBalanceUpdate(UserBalanceUpdateDto(user.id, 200))
    }

    if (notificationShouldBeSent) {
      verifyBlocking(messageBus) { publishAsync(PushNotificationEffect(RatingPromptCommand("userId"))) }
      verifyBlocking(userService) { trackNotification("userId", RATING_PROMPT) }
    } else {
      verifyBlocking(messageBus, never()) { publishAsync(PushNotificationEffect(RatingPromptCommand("userId"))) }
      verifyBlocking(userService, never()) { trackNotification("userId", RATING_PROMPT) }
    }
  }

  @Test
  fun `SHOULD not send rating prompt notification ON onUserBalanceUpdate WHEN prompt already sent`() {
    userService.mock({ hasPreviousNotification(userDtoStub.id, RATING_PROMPT) }, true)
    val user = userDtoStub.copy(
      appPlatform = ANDROID,
    )
    rewardingFacade.mock({ getUserCurrentCoinsBalance("userId", ANDROID) }, UserCurrentCoinsGoalBalance(700, 100, 0))

    userService.mock({ getUser(user.id) }, user)

    runBlocking {
      underTest.onUserBalanceUpdate(UserBalanceUpdateDto(user.id, 200))
    }

    verifyBlocking(messageBus, never()) { publishAsync(PushNotificationEffect(RatingPromptCommand("userId"))) }
    verifyBlocking(userService, never()) { trackNotification("userId", RATING_PROMPT) }
  }

  @Test
  fun `SHOULD NOT launch coins revenue task ON onUserBalanceUpdate WHEN em1 participant`() {
    val user = mock<UserDto> {
      whenever(it.id).thenReturn("userId")
      whenever(it.appPlatform).thenReturn(ANDROID)
    }
    rewardingFacade.mock({ getUserCurrentCoinsBalance("userId", ANDROID) }, UserCurrentCoinsGoalBalance(700, 100, 0))

    userService.mock({ getUser(user.id) }, user)
    abTestingService.mock({ getEm2Participation(user.id) }, No)

    runBlocking {
      underTest.onUserBalanceUpdate(UserBalanceUpdateDto(user.id, 200))
    }

    verifyBlocking(messageBus, never()) {
      publish(
        em3CoinsRevenueCheckMessage {
          userId = "userId"
          platform = Common.AppPlatformProto.ANDROID
        }
      )
    }
  }

  @Test
  fun `SHOULD NOT launch coins revenue task ON onUserBalanceUpdate WHEN em2 participant`() {
    val user = mock<UserDto> {
      whenever(it.id).thenReturn("userId")
      whenever(it.appPlatform).thenReturn(ANDROID)
    }
    rewardingFacade.mock({ getUserCurrentCoinsBalance("userId", ANDROID) }, UserCurrentCoinsGoalBalance(700, 100, 0))

    userService.mock({ getUser(user.id) }, user)
    abTestingService.mock({ getEm2Participation(user.id) }, Yes(variation = EM2_BOILED_FROG_SOFT))

    runBlocking {
      underTest.onUserBalanceUpdate(UserBalanceUpdateDto(user.id, 200))
    }

    verifyBlocking(messageBus, never()) {
      publish(
        em3CoinsRevenueCheckMessage {
          userId = "userId"
          platform = Common.AppPlatformProto.ANDROID
        }
      )
    }
  }

  @Test
  fun `SHOULD launch coins revenue task ON onUserBalanceUpdate WHEN em3 participant`() {
    val user = mock<UserDto> {
      whenever(it.id).thenReturn("userId")
      whenever(it.appPlatform).thenReturn(ANDROID)
    }
    rewardingFacade.mock({ getUserCurrentCoinsBalance("userId", ANDROID) }, UserCurrentCoinsGoalBalance(700, 100, 0))

    userService.mock({ getUser(user.id) }, user)
    abTestingService.mock({ isEm3Participant(user.id) }, true)

    runBlocking {
      underTest.onUserBalanceUpdate(UserBalanceUpdateDto(user.id, 200))
    }

    verifyBlocking(messageBus) {
      publish(
        em3CoinsRevenueCheckMessage {
          userId = "userId"
          platform = Common.AppPlatformProto.ANDROID
        }
      )
    }
  }

}
