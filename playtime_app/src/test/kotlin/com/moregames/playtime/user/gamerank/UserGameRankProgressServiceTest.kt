package com.moregames.playtime.user.gamerank

import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform
import com.moregames.base.messaging.dto.UserChallengeProgressDto
import com.moregames.base.util.ApplicationId
import com.moregames.base.util.mock
import com.moregames.playtime.buseffects.PushNotificationEffect
import com.moregames.playtime.games.GamesService
import com.moregames.base.dto.Game
import com.moregames.playtime.notifications.PushNotification.AndroidPushNotification.GameRankUpdatedNotification
import com.moregames.playtime.translations.TranslationService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.UserPersistenceService.UserDto
import com.moregames.playtime.user.challenge.progress.achievement.AchievementDto
import com.moregames.playtime.user.gamerank.calculators.GameRankProgress
import com.moregames.playtime.user.gamerank.calculators.GameRankProgressCalculator
import com.moregames.playtime.util.defaultJsonConverter
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.Json
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.*
import java.util.*

class UserGameRankProgressServiceTest {
  private val gamesService: GamesService = mock()
  private val userGameRankPersistenceService: UserGameRankPersistenceService = mock()
  private val calculators: Map<GameRankCalculator, GameRankProgressCalculator> = mock()
  private val json: Json = defaultJsonConverter
  private val messageBus: MessageBus = mock()
  private val translationService: TranslationService = mock()
  private val userService: UserService = mock()

  private val underTest = UserGameRankProgressService(
    gamesService = gamesService,
    userGameRankPersistenceService = userGameRankPersistenceService,
    calculators = calculators,
    json = json,
    messageBus = messageBus,
    translationService = translationService,
    userService = userService
  )

  companion object {
    const val USER_ID = "test-user-id"
    const val APPLICATION_ID = ApplicationId.SOLITAIRE_VERSE_APP_ID
    const val GAME_ID = 200044 // solitaire from GameRankConfigHolder
    const val GAME_NAME = "Solitaire"
    const val TRANSLATED_GAME_NAME = "Solitaire Translated"

    val progressDto = UserChallengeProgressDto.ScoreProgressDto(
      userId = USER_ID,
      applicationId = APPLICATION_ID,
      score = 50
    )

    val currentEntity = UserGameRankEntity(
      userId = USER_ID,
      gameId = GAME_ID,
      rank = GameRank.ZERO,
      progress = 0,
      achievement = null
    )

    val achievement = AchievementDto(setOf("level_1", "level_2"))
    val achievementJson = """{"levels":["level_1","level_2"]}"""

    val updatedProgress = GameRankProgress(
      rank = GameRank.ONE,
      progress = 50,
      achievement = achievement
    )

    val game = Game(
      id = GAME_ID,
      applicationId = "com.example.solitaire",
      name = GAME_NAME,
      activityName = "MainActivity",
      description = "A classic solitaire game",
      iconFilename = "icon.png",
      imageFilename = "banner.png",
      orderKey = 1,
      applovinApiKey = "test-key",
      showInstallImage = true,
      installImageFilename = "install.png",
      videoPreviewFilename = null,
      showVideoPreview = false,
      infoTextInstallTop = "Install top",
      infoTextInstallBottom = "Install bottom",
      expImageFilename = null,
      backGroundColor = null,
      publisherId = 1,
      showForLat = true,
      iosApplicationId = null,
      iosGameUrl = null,
      platform = AppPlatform.ANDROID,
      webglUrl = null,
      demoGameUrl = null,
      doNotShow = false,
      webAppAdjustLink = null
    )

    val userDto = UserDto(
      id = USER_ID,
      appPlatform = AppPlatform.ANDROID,
      locale = Locale.ENGLISH,
      isDeleted = false
    )
  }

  @BeforeEach
  fun setUp() {
    reset(gamesService, userGameRankPersistenceService, calculators, messageBus, translationService, userService)
  }

  @Test
  fun `SHOULD handle user progress successfully with rank change and send push notification`() = runTest {
    // Given
    val config = GameRankConfigHolder.gamesConfig[GAME_ID]!!
    val calculator: GameRankProgressCalculator = mock()

    gamesService.mock({ getGameId(APPLICATION_ID, AppPlatform.ANDROID) }, GAME_ID)
    gamesService.mock({ getGameById(AppPlatform.ANDROID, GAME_ID) }, game)
    userGameRankPersistenceService.mock({ getUserGameRankEntity(USER_ID, GAME_ID) }, currentEntity)
    userService.mock({ getUser(USER_ID) }, userDto)
    translationService.mock({ tryTranslate(GAME_NAME, Locale.ENGLISH) }, TRANSLATED_GAME_NAME)
    whenever(calculators[config.calculator]).thenReturn(calculator)
    whenever(calculator.calculateProgress(progressDto, currentEntity.progress, null, config))
      .thenReturn(updatedProgress)

    // When
    underTest.handleUserProgress(progressDto)

    // Then
    verify(gamesService).getGameId(APPLICATION_ID, AppPlatform.ANDROID)
    verify(gamesService).getGameById(AppPlatform.ANDROID, GAME_ID)
    verify(userGameRankPersistenceService).getUserGameRankEntity(USER_ID, GAME_ID)
    verify(userService).getUser(USER_ID)
    verify(translationService).tryTranslate(GAME_NAME, Locale.ENGLISH)
    verify(calculator).calculateProgress(progressDto, currentEntity.progress, null, config)
    verify(messageBus).publishAsync(
      PushNotificationEffect(
        GameRankUpdatedNotification(
          userId = USER_ID,
          gameName = TRANSLATED_GAME_NAME,
          updatedRank = GameRank.ONE
        )
      )
    )
    verify(userGameRankPersistenceService).saveUserGameRank(
      currentEntity.copy(
        rank = updatedProgress.rank,
        progress = updatedProgress.progress,
        achievement = achievementJson
      )
    )
  }

  @Test
  fun `SHOULD handle user progress successfully without rank change and not send push notification`() = runTest {
    // Given
    val config = GameRankConfigHolder.gamesConfig[GAME_ID]!!
    val calculator: GameRankProgressCalculator = mock()
    val updatedProgressSameRank = updatedProgress.copy(rank = GameRank.ZERO) // same as current

    gamesService.mock({ getGameId(APPLICATION_ID, AppPlatform.ANDROID) }, GAME_ID)
    userGameRankPersistenceService.mock({ getUserGameRankEntity(USER_ID, GAME_ID) }, currentEntity)
    whenever(calculators[config.calculator]).thenReturn(calculator)
    whenever(calculator.calculateProgress(progressDto, currentEntity.progress, null, config))
      .thenReturn(updatedProgressSameRank)

    // When
    underTest.handleUserProgress(progressDto)

    // Then
    verify(gamesService).getGameId(APPLICATION_ID, AppPlatform.ANDROID)
    verify(userGameRankPersistenceService).getUserGameRankEntity(USER_ID, GAME_ID)
    verify(calculator).calculateProgress(progressDto, currentEntity.progress, null, config)
    verifyNoInteractions(messageBus) // No push notification should be sent
    verify(userGameRankPersistenceService).saveUserGameRank(
      currentEntity.copy(
        rank = updatedProgressSameRank.rank,
        progress = updatedProgressSameRank.progress,
        achievement = achievementJson
      )
    )
  }

  @Test
  fun `SHOULD return early WHEN gameId is null`() = runTest {
    // Given
    gamesService.mock({ getGameId(APPLICATION_ID, AppPlatform.ANDROID) }, null)

    // When
    underTest.handleUserProgress(progressDto)

    // Then
    verify(gamesService).getGameId(APPLICATION_ID, AppPlatform.ANDROID)
    verifyNoInteractions(userGameRankPersistenceService)
    verifyNoInteractions(calculators)
  }

  @Test
  fun `SHOULD return early WHEN no config found for gameId`() = runTest {
    // Given
    val unknownGameId = 999999
    gamesService.mock({ getGameId(APPLICATION_ID, AppPlatform.ANDROID) }, unknownGameId)

    // When
    underTest.handleUserProgress(progressDto)

    // Then
    verify(gamesService).getGameId(APPLICATION_ID, AppPlatform.ANDROID)
    verifyNoInteractions(userGameRankPersistenceService)
    verifyNoInteractions(calculators)
  }

  @Test
  fun `SHOULD return early WHEN no calculator found for config`() = runTest {
    // Given
    val config = GameRankConfigHolder.gamesConfig[GAME_ID]!!
    gamesService.mock({ getGameId(APPLICATION_ID, AppPlatform.ANDROID) }, GAME_ID)
    whenever(calculators[config.calculator]).thenReturn(null)

    // When
    underTest.handleUserProgress(progressDto)

    // Then
    verify(gamesService).getGameId(APPLICATION_ID, AppPlatform.ANDROID)
    verifyNoInteractions(userGameRankPersistenceService)
  }

  @Test
  fun `SHOULD handle existing achievement correctly`() = runTest {
    // Given
    val config = GameRankConfigHolder.gamesConfig[GAME_ID]!!
    val calculator: GameRankProgressCalculator = mock()
    val entityWithAchievement = currentEntity.copy(achievement = achievementJson)

    gamesService.mock({ getGameId(APPLICATION_ID, AppPlatform.ANDROID) }, GAME_ID)
    userGameRankPersistenceService.mock({ getUserGameRankEntity(USER_ID, GAME_ID) }, entityWithAchievement)
    whenever(calculators[config.calculator]).thenReturn(calculator)
    whenever(calculator.calculateProgress(progressDto, entityWithAchievement.progress, achievement, config))
      .thenReturn(updatedProgress)

    // When
    underTest.handleUserProgress(progressDto)

    // Then
    verify(calculator).calculateProgress(progressDto, entityWithAchievement.progress, achievement, config)
    verify(userGameRankPersistenceService).saveUserGameRank(
      entityWithAchievement.copy(
        rank = updatedProgress.rank,
        progress = updatedProgress.progress,
        achievement = achievementJson
      )
    )
  }

  @Test
  fun `SHOULD handle invalid achievement JSON gracefully`() = runTest {
    // Given
    val config = GameRankConfigHolder.gamesConfig[GAME_ID]!!
    val calculator: GameRankProgressCalculator = mock()
    val entityWithInvalidAchievement = currentEntity.copy(achievement = "invalid-json")

    gamesService.mock({ getGameId(APPLICATION_ID, AppPlatform.ANDROID) }, GAME_ID)
    userGameRankPersistenceService.mock({ getUserGameRankEntity(USER_ID, GAME_ID) }, entityWithInvalidAchievement)
    whenever(calculators[config.calculator]).thenReturn(calculator)
    whenever(calculator.calculateProgress(progressDto, entityWithInvalidAchievement.progress, null, config))
      .thenReturn(updatedProgress)

    // When
    underTest.handleUserProgress(progressDto)

    // Then
    verify(calculator).calculateProgress(progressDto, entityWithInvalidAchievement.progress, null, config)
    verify(userGameRankPersistenceService).saveUserGameRank(
      entityWithInvalidAchievement.copy(
        rank = updatedProgress.rank,
        progress = updatedProgress.progress,
        achievement = achievementJson
      )
    )
  }

  @Test
  fun `SHOULD handle blank achievement string`() = runTest {
    // Given
    val config = GameRankConfigHolder.gamesConfig[GAME_ID]!!
    val calculator: GameRankProgressCalculator = mock()
    val entityWithBlankAchievement = currentEntity.copy(achievement = "   ")

    gamesService.mock({ getGameId(APPLICATION_ID, AppPlatform.ANDROID) }, GAME_ID)
    userGameRankPersistenceService.mock({ getUserGameRankEntity(USER_ID, GAME_ID) }, entityWithBlankAchievement)
    whenever(calculators[config.calculator]).thenReturn(calculator)
    whenever(calculator.calculateProgress(progressDto, entityWithBlankAchievement.progress, null, config))
      .thenReturn(updatedProgress)

    // When
    underTest.handleUserProgress(progressDto)

    // Then
    verify(calculator).calculateProgress(progressDto, entityWithBlankAchievement.progress, null, config)
  }

  @Test
  fun `SHOULD handle different UserChallengeProgressDto types`() = runTest {
    // Given
    val config = GameRankConfigHolder.gamesConfig[GAME_ID]!!
    val calculator: GameRankProgressCalculator = mock()
    val milestoneProgressDto = UserChallengeProgressDto.MilestoneProgressDto(
      userId = USER_ID,
      applicationId = APPLICATION_ID,
      milestone = 10
    )

    gamesService.mock({ getGameId(APPLICATION_ID, AppPlatform.ANDROID) }, GAME_ID)
    userGameRankPersistenceService.mock({ getUserGameRankEntity(USER_ID, GAME_ID) }, currentEntity)
    whenever(calculators[config.calculator]).thenReturn(calculator)
    whenever(calculator.calculateProgress(milestoneProgressDto, currentEntity.progress, null, config))
      .thenReturn(updatedProgress)

    // When
    underTest.handleUserProgress(milestoneProgressDto)

    // Then
    verify(calculator).calculateProgress(milestoneProgressDto, currentEntity.progress, null, config)
    verify(userGameRankPersistenceService).saveUserGameRank(
      currentEntity.copy(
        rank = updatedProgress.rank,
        progress = updatedProgress.progress,
        achievement = achievementJson
      )
    )
  }

  @Test
  fun `SHOULD handle null achievement in updated progress`() = runTest {
    // Given
    val config = GameRankConfigHolder.gamesConfig[GAME_ID]!!
    val calculator: GameRankProgressCalculator = mock()
    val updatedProgressWithNullAchievement = updatedProgress.copy(achievement = null)

    gamesService.mock({ getGameId(APPLICATION_ID, AppPlatform.ANDROID) }, GAME_ID)
    userGameRankPersistenceService.mock({ getUserGameRankEntity(USER_ID, GAME_ID) }, currentEntity)
    whenever(calculators[config.calculator]).thenReturn(calculator)
    whenever(calculator.calculateProgress(progressDto, currentEntity.progress, null, config))
      .thenReturn(updatedProgressWithNullAchievement)

    // When
    underTest.handleUserProgress(progressDto)

    // Then
    verify(userGameRankPersistenceService).saveUserGameRank(
      currentEntity.copy(
        rank = updatedProgressWithNullAchievement.rank,
        progress = updatedProgressWithNullAchievement.progress,
        achievement = null
      )
    )
  }

  @Test
  fun `SHOULD handle ScoreCompletedProgressDto correctly`() = runTest {
    // Given
    val config = GameRankConfigHolder.gamesConfig[GAME_ID]!!
    val calculator: GameRankProgressCalculator = mock()
    val scoreCompletedDto = UserChallengeProgressDto.ScoreCompletedProgressDto(
      userId = USER_ID,
      applicationId = APPLICATION_ID,
      score = 75,
      isHighScore = true
    )

    gamesService.mock({ getGameId(APPLICATION_ID, AppPlatform.ANDROID) }, GAME_ID)
    gamesService.mock({ getGameById(AppPlatform.ANDROID, GAME_ID) }, game)
    userGameRankPersistenceService.mock({ getUserGameRankEntity(USER_ID, GAME_ID) }, currentEntity)
    userService.mock({ getUser(USER_ID) }, userDto)
    translationService.mock({ tryTranslate(GAME_NAME, Locale.ENGLISH) }, TRANSLATED_GAME_NAME)
    whenever(calculators[config.calculator]).thenReturn(calculator)
    whenever(calculator.calculateProgress(scoreCompletedDto, currentEntity.progress, null, config))
      .thenReturn(updatedProgress)

    // When
    underTest.handleUserProgress(scoreCompletedDto)

    // Then
    verify(gamesService).getGameId(APPLICATION_ID, AppPlatform.ANDROID)
    verify(userGameRankPersistenceService).getUserGameRankEntity(USER_ID, GAME_ID)
    verify(calculator).calculateProgress(scoreCompletedDto, currentEntity.progress, null, config)
    verify(userGameRankPersistenceService).saveUserGameRank(
      currentEntity.copy(
        rank = updatedProgress.rank,
        progress = updatedProgress.progress,
        achievement = achievementJson
      )
    )
  }

  @Test
  fun `SHOULD not send push notification WHEN game is not found`() = runTest {
    // Given
    val config = GameRankConfigHolder.gamesConfig[GAME_ID]!!
    val calculator: GameRankProgressCalculator = mock()

    gamesService.mock({ getGameId(APPLICATION_ID, AppPlatform.ANDROID) }, GAME_ID)
    gamesService.mock({ getGameById(AppPlatform.ANDROID, GAME_ID) }, null) // Game not found
    userGameRankPersistenceService.mock({ getUserGameRankEntity(USER_ID, GAME_ID) }, currentEntity)
    whenever(calculators[config.calculator]).thenReturn(calculator)
    whenever(calculator.calculateProgress(progressDto, currentEntity.progress, null, config))
      .thenReturn(updatedProgress)

    // When
    underTest.handleUserProgress(progressDto)

    // Then
    verify(gamesService).getGameById(AppPlatform.ANDROID, GAME_ID)
    verifyNoInteractions(userService, translationService)
    verifyNoInteractions(messageBus) // No push notification should be sent
    verify(userGameRankPersistenceService).saveUserGameRank(any()) // Progress should still be saved
  }

  @Test
  fun `SHOULD send push notification with correct game name translation`() = runTest {
    // Given
    val config = GameRankConfigHolder.gamesConfig[GAME_ID]!!
    val calculator: GameRankProgressCalculator = mock()
    val frenchLocale = Locale.FRENCH
    val frenchGameName = "Solitaire Français"
    val userWithFrenchLocale = userDto.copy(locale = frenchLocale)

    gamesService.mock({ getGameId(APPLICATION_ID, AppPlatform.ANDROID) }, GAME_ID)
    gamesService.mock({ getGameById(AppPlatform.ANDROID, GAME_ID) }, gameDto)
    userGameRankPersistenceService.mock({ getUserGameRankEntity(USER_ID, GAME_ID) }, currentEntity)
    userService.mock({ getUser(USER_ID) }, userWithFrenchLocale)
    translationService.mock({ tryTranslate(GAME_NAME, frenchLocale) }, frenchGameName)
    whenever(calculators[config.calculator]).thenReturn(calculator)
    whenever(calculator.calculateProgress(progressDto, currentEntity.progress, null, config))
      .thenReturn(updatedProgress)

    // When
    underTest.handleUserProgress(progressDto)

    // Then
    verify(translationService).tryTranslate(GAME_NAME, frenchLocale)
    verify(messageBus).publishAsync(
      PushNotificationEffect(
        GameRankUpdatedNotification(
          userId = USER_ID,
          gameName = frenchGameName,
          updatedRank = GameRank.ONE
        )
      )
    )
  }

  @Test
  fun `SHOULD send push notification for different rank upgrades`() = runTest {
    // Given
    val config = GameRankConfigHolder.gamesConfig[GAME_ID]!!
    val calculator: GameRankProgressCalculator = mock()
    val updatedProgressToThree = updatedProgress.copy(rank = GameRank.THREE)

    gamesService.mock({ getGameId(APPLICATION_ID, AppPlatform.ANDROID) }, GAME_ID)
    gamesService.mock({ getGameById(AppPlatform.ANDROID, GAME_ID) }, gameDto)
    userGameRankPersistenceService.mock({ getUserGameRankEntity(USER_ID, GAME_ID) }, currentEntity)
    userService.mock({ getUser(USER_ID) }, userDto)
    translationService.mock({ tryTranslate(GAME_NAME, Locale.ENGLISH) }, TRANSLATED_GAME_NAME)
    whenever(calculators[config.calculator]).thenReturn(calculator)
    whenever(calculator.calculateProgress(progressDto, currentEntity.progress, null, config))
      .thenReturn(updatedProgressToThree)

    // When
    underTest.handleUserProgress(progressDto)

    // Then
    verify(messageBus).publishAsync(
      PushNotificationEffect(
        GameRankUpdatedNotification(
          userId = USER_ID,
          gameName = TRANSLATED_GAME_NAME,
          updatedRank = GameRank.THREE
        )
      )
    )
  }

  @Test
  fun `SHOULD send push notification for rank downgrade`() = runTest {
    // Given
    val config = GameRankConfigHolder.gamesConfig[GAME_ID]!!
    val calculator: GameRankProgressCalculator = mock()
    val currentEntityWithHighRank = currentEntity.copy(rank = GameRank.TWO)
    val downgradedProgress = updatedProgress.copy(rank = GameRank.ONE)

    gamesService.mock({ getGameId(APPLICATION_ID, AppPlatform.ANDROID) }, GAME_ID)
    gamesService.mock({ getGameById(AppPlatform.ANDROID, GAME_ID) }, gameDto)
    userGameRankPersistenceService.mock({ getUserGameRankEntity(USER_ID, GAME_ID) }, currentEntityWithHighRank)
    userService.mock({ getUser(USER_ID) }, userDto)
    translationService.mock({ tryTranslate(GAME_NAME, Locale.ENGLISH) }, TRANSLATED_GAME_NAME)
    whenever(calculators[config.calculator]).thenReturn(calculator)
    whenever(calculator.calculateProgress(progressDto, currentEntityWithHighRank.progress, null, config))
      .thenReturn(downgradedProgress)

    // When
    underTest.handleUserProgress(progressDto)

    // Then
    verify(messageBus).publishAsync(
      PushNotificationEffect(
        GameRankUpdatedNotification(
          userId = USER_ID,
          gameName = TRANSLATED_GAME_NAME,
          updatedRank = GameRank.ONE
        )
      )
    )
  }
}