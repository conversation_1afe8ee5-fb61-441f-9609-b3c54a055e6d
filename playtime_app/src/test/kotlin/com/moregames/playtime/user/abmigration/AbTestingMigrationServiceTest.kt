package com.moregames.playtime.user.abmigration

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.abtesting.AbTestingEventsService
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.dto.Experiment
import com.moregames.base.abtesting.dto.Variation
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.user.abmigration.AbTestingMigrationPersistenceService.AllowedOfwPlacement
import com.moregames.playtime.user.abmigration.AbTestingMigrationPersistenceService.ForceAssigningParticipant
import com.moregames.playtime.user.appVersionDto
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.*
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit

class AbTestingMigrationServiceTest {

  private val abTestingMigrationPersistenceService: AbTestingMigrationPersistenceService = mock()
  private val abTestingService: AbTestingService = mock()
  private val abTestingEventsService: AbTestingEventsService = mock()
  private val timeService: TimeService = mock()

  val service = AbTestingMigrationService(
    abTestingMigrationPersistenceService = abTestingMigrationPersistenceService,
    abTestingService = abTestingService,
    abTestingEventsService = abTestingEventsService,
    timeService = timeService
  )

  companion object {
    private val now: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    private val experimentIds = listOf(1, 2, 3)
    private val experiments = experimentIds.map { Experiment(it, "someExp", true, 55, now, now, null) }
    private val variations = experiments.map { Variation(101, "someVariation", BigDecimal("0.5"), it) }
    private val otherExperiment = Experiment(4, "someOtherExp", true, 55, now, now, null)
    private val otherVariations = listOf(Variation(101, "someVariation", BigDecimal("0.50"), otherExperiment))
    const val USER_ID = "userId"
  }


  @BeforeEach
  fun init() {
    abTestingMigrationPersistenceService.mock({ getForceAssignmentExperimentIds() }, experimentIds)
    timeService.mock({ now() }, now)
  }

  @Test
  fun `SHOULD return allowed offerwall placement ids for a user ON getAllowedOfwPlacementIds`() {
    val placements = listOf(
      AllowedOfwPlacement("placement1", 10),
      AllowedOfwPlacement("placement2", 100)
    )
    abTestingMigrationPersistenceService.mock({ getAllowedOfwPlacementIds(USER_ID) }, placements)

    runBlocking {
      service.getAllowedOfwPlacementIds(USER_ID)
    }.let { assertThat(it).isEqualTo(placements) }

    verifyBlocking(abTestingMigrationPersistenceService) { getAllowedOfwPlacementIds(USER_ID) }
  }

  @Test
  fun `SHOULD do nothing on assignUserToExperimentsWithForcedAssignment WHEN we have no active force assignment experiments`() {
    abTestingMigrationPersistenceService.mock({ getForceAssignmentExperimentIds() }, emptyList())

    runBlocking {
      service.assignUserToExperimentsWithForcedAssignment(userDtoStub)
    }

    verifyBlocking(abTestingMigrationPersistenceService) { getForceAssignmentExperimentIds() }
    verifyNoMoreInteractions(abTestingMigrationPersistenceService)
    verifyNoInteractions(abTestingService, abTestingEventsService)
  }

  @Test
  fun `SHOULD do nothing on assignUserToExperimentsWithForcedAssignment WHEN user assigned to all experiments with force assignment`() {
    abTestingService.mock({ loadVariationsToAssign(userDtoStub.id, userDtoStub.appVersionDto(), userCountry = null) }, otherVariations)

    runBlocking {
      service.assignUserToExperimentsWithForcedAssignment(userDtoStub)
    }

    verifyBlocking(abTestingMigrationPersistenceService) { getForceAssignmentExperimentIds() }
    verifyBlocking(abTestingService) { loadVariationsToAssign(userDtoStub.id, userDtoStub.appVersionDto(), userCountry = null) }
    verifyNoMoreInteractions(abTestingMigrationPersistenceService)
    verifyNoInteractions(abTestingEventsService)
  }

  @Test
  fun `SHOULD try to assign user to all experiments with force assignment WHEN user has some unassigned experiments`() {
    abTestingService.mock(
      { loadVariationsToAssign(userDtoStub.id, userDtoStub.appVersionDto(), userCountry = null) },
      variations + otherVariations
    )
    abTestingMigrationPersistenceService.mock({ tryCreateParticipationsReturnExperimentIds(USER_ID, variations) }, experimentIds)

    runBlocking {
      service.assignUserToExperimentsWithForcedAssignment(userDtoStub)
    }

    verifyBlocking(abTestingMigrationPersistenceService) { getForceAssignmentExperimentIds() }
    verifyBlocking(abTestingMigrationPersistenceService) { tryCreateParticipationsReturnExperimentIds(USER_ID, variations) }
    verifyBlocking(abTestingService) { loadVariationsToAssign(userDtoStub.id, userDtoStub.appVersionDto(), userCountry = null) }
    verifyBlocking(abTestingEventsService) { emitUserVariationAssignedEvents(USER_ID, variations) }
  }

  @Test
  fun `SHOULD not emit additional events WHEN on assignment phase it turned out we already assigned the user in other thread`() {
    abTestingService.mock(
      { loadVariationsToAssign(userDtoStub.id, userDtoStub.appVersionDto(), userCountry = null) },
      variations + otherVariations
    )
    abTestingMigrationPersistenceService.mock(
      { tryCreateParticipationsReturnExperimentIds(USER_ID, variations) },
      experimentIds.take(1)
    )

    runBlocking {
      service.assignUserToExperimentsWithForcedAssignment(userDtoStub)
    }

    verifyBlocking(abTestingMigrationPersistenceService) { getForceAssignmentExperimentIds() }
    verifyBlocking(abTestingMigrationPersistenceService) { tryCreateParticipationsReturnExperimentIds(USER_ID, variations) }
    verifyBlocking(abTestingService) { loadVariationsToAssign(userDtoStub.id, userDtoStub.appVersionDto(), userCountry = null) }
    verifyBlocking(abTestingEventsService) { emitUserVariationAssignedEvents(USER_ID, variations.filter { it.experiment.id == 1 }) }
  }

  @Test
  fun `SHOULD send no events WHEN on assignment phase it turned out user was assigned to all the experiments in other thread`() {
    abTestingService.mock(
      { loadVariationsToAssign(userDtoStub.id, userDtoStub.appVersionDto(), userCountry = null) },
      variations + otherVariations
    )
    abTestingMigrationPersistenceService.mock(
      { tryCreateParticipationsReturnExperimentIds(USER_ID, variations) },
      emptyList()
    )

    runBlocking {
      service.assignUserToExperimentsWithForcedAssignment(userDtoStub)
    }

    verifyBlocking(abTestingMigrationPersistenceService) { getForceAssignmentExperimentIds() }
    verifyBlocking(abTestingMigrationPersistenceService) { tryCreateParticipationsReturnExperimentIds(USER_ID, variations) }
    verifyBlocking(abTestingService) { loadVariationsToAssign(userDtoStub.id, userDtoStub.appVersionDto(), userCountry = null) }
    verifyBlocking(abTestingEventsService) { emitUserVariationAssignedEvents(USER_ID, emptyList()) }
  }

  @Test
  fun `SHOULD trigger BQ events sending on processBatchOfForceAssigningUsers WHEN users were already assigned to some variation`() {
    val batchSize = 10
    val experiments = listOf(
      Experiment(1, "experimentKey1", false, null, null, null, null),
      Experiment(2, "experimentKey2", false, null, null, null, null),
    )
    val variations = listOf(
      Variation(101, "variation1", BigDecimal.ONE, experiments[0]),
      Variation(102, "variation2", BigDecimal.ZERO, experiments[0]),
      Variation(103, "variation3", BigDecimal.ONE, experiments[1]),
      Variation(104, "variation4", BigDecimal.ZERO, experiments[1]),
      Variation(105, "variation5", BigDecimal.ZERO, experiments[1]),
    )
    abTestingService.mock({ loadAllExpVariations() }, variations)
    val forceAssigningUsers = listOf(
      ForceAssigningParticipant("userId1", experimentId = 1, variationId = 101),
      ForceAssigningParticipant("userId2", experimentId = 1, variationId = 101),
      ForceAssigningParticipant("userId3", experimentId = 1, variationId = 101),
      ForceAssigningParticipant("userId4", experimentId = 1, variationId = 101),
      ForceAssigningParticipant("userId5", experimentId = 1, variationId = 102),
    )
    abTestingMigrationPersistenceService.mock({ getBatchOfForceAssigningUsers(batchSize) }, forceAssigningUsers)

    runBlocking {
      service.processBatchOfForceAssigningUsers(batchSize)
    }.let { assertThat(it).isEqualTo(5) }

    verifyBlocking(abTestingMigrationPersistenceService) { getBatchOfForceAssigningUsers(batchSize) }
    verifyBlocking(abTestingEventsService) {
      emitUserVariationAssignedEvents(
        userIds = forceAssigningUsers.map { it.userId }.take(4),
        variation = variations.first { it.id == 101 }
      )
    }
    verifyBlocking(abTestingMigrationPersistenceService) {
      markForceAssignmentProcessed(
        userIds = forceAssigningUsers.map { it.userId }.take(4),
        experimentId = 1,
        processedAt = now
      )
    }
    verifyBlocking(abTestingEventsService) {
      emitUserVariationAssignedEvents(
        userIds = forceAssigningUsers.map { it.userId }.takeLast(1),
        variation = variations.first { it.id == 102 }
      )
    }
    verifyBlocking(abTestingMigrationPersistenceService) {
      markForceAssignmentProcessed(
        userIds = forceAssigningUsers.map { it.userId }.takeLast(1),
        experimentId = 1,
        processedAt = now
      )
    }

    verifyNoMoreInteractions(abTestingMigrationPersistenceService)
  }

  @Test
  fun `SHOULD force assign users and emit batch of BQ events ON processBatchOfForceAssigningUsers WHEN the users were not assigned to variation before`() {
    val batchSize = 10
    val experiments = listOf(
      Experiment(1, "experimentKey1", false, null, null, null, null),
      Experiment(2, "experimentKey2", false, null, null, null, null),
    )
    val variations = listOf(
      Variation(101, "variation1", BigDecimal.ONE, experiments[0]),
      Variation(102, "variation2", BigDecimal.ZERO, experiments[0]),
      Variation(103, "variation3", BigDecimal.ONE, experiments[1]),
      Variation(104, "variation4", BigDecimal.ZERO, experiments[1]),
      Variation(105, "variation5", BigDecimal.ZERO, experiments[1]),
    )
    abTestingService.mock({ loadAllExpVariations() }, variations)
    val forceAssigningUsers = listOf(
      ForceAssigningParticipant("userId1", experimentId = 1, variationId = null),
      ForceAssigningParticipant("userId2", experimentId = 2, variationId = null),
      ForceAssigningParticipant("userId3", experimentId = 2, variationId = null),
      ForceAssigningParticipant("userId4", experimentId = 1, variationId = null),
      ForceAssigningParticipant("userId5", experimentId = 1, variationId = null),
    )
    abTestingMigrationPersistenceService.mock({ getBatchOfForceAssigningUsers(batchSize) }, forceAssigningUsers)
    abTestingService.mock({ calculateVariationToAssign(variations.take(2)) }, variations[0])
    abTestingService.mock({ calculateVariationToAssign(variations.takeLast(3)) }, variations[2])

    runBlocking {
      service.processBatchOfForceAssigningUsers(batchSize)
    }.let { assertThat(it).isEqualTo(5) }


    verifyBlocking(abTestingMigrationPersistenceService, times(1)) {
      makeParticipantsTrackForceAssignment(
        userIds = listOf("userId1", "userId4", "userId5"),
        experimentId = 1,
        variationId = 101
      )
    }
    verifyBlocking(abTestingEventsService, times(1)) {
      emitUserVariationAssignedEvents(listOf("userId1", "userId4", "userId5"), variations[0])
    }
    verifyBlocking(abTestingMigrationPersistenceService, times(1)) {
      markForceAssignmentProcessed(
        userIds = listOf("userId1", "userId4", "userId5"),
        experimentId = 1,
        processedAt = now
      )
    }

    verifyBlocking(abTestingMigrationPersistenceService, times(1)) {
      makeParticipantsTrackForceAssignment(
        userIds = listOf("userId2", "userId3"),
        experimentId = 2,
        variationId = 103
      )
    }
    verifyBlocking(abTestingEventsService, times(1)) {
      emitUserVariationAssignedEvents(listOf("userId2", "userId3"), variations[2])
    }
    verifyBlocking(abTestingMigrationPersistenceService, times(1)) {
      markForceAssignmentProcessed(
        userIds = listOf("userId2", "userId3"),
        experimentId = 2,
        processedAt = now
      )
    }
  }

  @Test
  fun `SHOULD trigger persistence method ON cleanAbForceAssignmentProcessed`() {
    val longAgo = now.minus(7, ChronoUnit.DAYS)
    runBlocking {
      service.cleanAbForceAssignmentProcessed(500)
    }

    verifyBlocking(abTestingMigrationPersistenceService) {
      removeObsoleteForceAssignmentProcessedTracks(500, longAgo)
    }
  }

}