package com.moregames.playtime.user.pregamescreen

import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.DEFAULT
import com.moregames.base.abtesting.variations.AndroidPreGameScreenVariation
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.app.IMAGES_ROOT
import com.moregames.playtime.app.ImageService
import com.moregames.playtime.user.cashout.CashoutPeriodsService
import com.moregames.playtime.user.cashout.dto.CashoutPeriodDto
import com.moregames.playtime.user.timezone.UserTimeZoneService
import com.moregames.playtime.user.usergame.UserGamePersistenceService
import com.moregames.playtime.utils.androidGameOfferListStub
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.mockito.kotlin.*
import java.time.Instant
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit
import kotlin.test.assertEquals

class AndroidPreGameScreenServiceTest {
  private val timeService: TimeService = mock()
  private val userTimeZoneService: UserTimeZoneService = mock()
  private val abTestingService: AbTestingService = mock()
  private val cashoutPeriodsService: CashoutPeriodsService = mock()
  private val userGamePersistenceService: UserGamePersistenceService = mock()
  private val imageService: ImageService = mock {
    on { toUrl(any()) } doAnswer { IMAGES_ROOT + it.arguments[0] as String }
  }

  companion object {
    const val USER_ID = "testUserId"
  }

  private val underTest = AndroidPreGameScreenService(
    timeService,
    userTimeZoneService,
    abTestingService,
    cashoutPeriodsService,
    userGamePersistenceService = userGamePersistenceService,
    imageService = imageService
  )

  @Test
  fun `SHOULD return the same game list`() {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.ANDROID_PRE_GAME_SCREEN) }, DEFAULT)
    runBlocking {
      underTest.applyExperiment(USER_ID, androidGameOfferListStub)
    }.let {
      assertEquals(androidGameOfferListStub, it)
    }
  }

  @Test
  fun `SHOULD return games list with correct preGameScreenMode for PRE_GAME_SCREEN_INTRO_BEFORE_INSTALL variation`() = runTest {
    val now = Instant.now()
    timeService.mock({ now() }, now)
    userTimeZoneService.mockUserTime(now)
    abTestingService.mock(
      { assignedVariationValue(USER_ID, ClientExperiment.ANDROID_PRE_GAME_SCREEN) },
      AndroidPreGameScreenVariation.PreGameScreenIntroBeforeInstall
    )
    val cashoutPeriod = CashoutPeriodDto(USER_ID, now, now.plus(3, ChronoUnit.HOURS), 100, 175, 0, coinGoalMilestones = listOf(1, 2, 3))
    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriod)
    val timerEnd = now.plus(3, ChronoUnit.HOURS).epochSecond
    //tm, solitaire, ballbounce and unknown game
    val games = androidGameOfferListStub.subList(0, 3) + androidGameOfferListStub[3].copy(applicationId = "unknown")

    val result = underTest.applyExperiment(USER_ID, games)
    assertEquals(games.size, result.size)
    assertEquals(
      games[0].copy(
        preGameScreenMode = AndroidPreGameScreenContent(
          backgroundImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_tm_image.png",
          amountImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_tm_amount.png",
          badgeText = "Top earners made this week",
          badgeTextColor = "#1276FD",
          buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
          buttonSubText = "Your Adventure Awaits!",
          timerEnd = timerEnd,
          mode = AndroidPreGameScreenModeDto.BEFORE_INSTALL
        )
      ), result[0]
    )
    assertEquals(
      games[1].copy(
        preGameScreenMode = AndroidPreGameScreenContent(
          backgroundImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_solitaireverse_image.png",
          amountImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_solitaireverse_amount.png",
          badgeText = "Top earners made this week",
          badgeTextColor = "#1276FD",
          buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
          buttonSubText = "Your Adventure Awaits!",
          timerEnd = timerEnd,
          mode = AndroidPreGameScreenModeDto.BEFORE_INSTALL
        )
      ), result[1]
    )
    assertEquals(
      games[2].copy(
        preGameScreenMode = AndroidPreGameScreenContent(
          backgroundImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_ballbounce_image.png",
          amountImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_ballbounce_amount.png",
          badgeText = "Top earners made this week",
          badgeTextColor = "#1276FD",
          buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
          buttonSubText = "Your Adventure Awaits!",
          timerEnd = timerEnd,
          mode = AndroidPreGameScreenModeDto.BEFORE_INSTALL
        )
      ), result[2]
    )
    assertEquals(
      games[3].copy(
        preGameScreenMode = AndroidPreGameScreenContent(
          backgroundImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_justplay_image.png",
          amountImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_justplay_amount.png",
          badgeText = "Top earners made this week",
          badgeTextColor = "#1276FD",
          buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
          buttonSubText = "Your Adventure Awaits!",
          timerEnd = timerEnd,
          mode = AndroidPreGameScreenModeDto.BEFORE_INSTALL
        )
      ), result[3]
    )
  }

  @Test
  fun `SHOULD return games list with correct preGameScreenMode for PRE_GAME_SCREEN_INTRO_PERMANENT variation`() = runTest {
    val now = Instant.now()
    timeService.mock({ now() }, now)
    userTimeZoneService.mockUserTime(now)
    abTestingService.mock(
      { assignedVariationValue(USER_ID, ClientExperiment.ANDROID_PRE_GAME_SCREEN) },
      AndroidPreGameScreenVariation.PreGameScreenIntroPermanent
    )
    val cashoutPeriod = CashoutPeriodDto(USER_ID, now, now.plus(3, ChronoUnit.HOURS), 100, 175, 0, coinGoalMilestones = listOf(1, 2, 3))
    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriod)
    val timerEnd = now.plus(3, ChronoUnit.HOURS).epochSecond
    //tm, solitaire, ballbounce and unknown game
    val games = androidGameOfferListStub.subList(0, 3) + androidGameOfferListStub[3].copy(applicationId = "unknown")


    val result = underTest.applyExperiment(USER_ID, games)
    assertEquals(games.size, result.size)
    assertEquals(
      games[0].copy(
        preGameScreenMode = AndroidPreGameScreenContent(
          backgroundImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_tm_image.png",
          amountImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_tm_amount.png",
          badgeText = "Top earners made this week",
          badgeTextColor = "#1276FD",
          buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
          buttonSubText = "Your Adventure Awaits!",
          timerEnd = timerEnd,
          mode = AndroidPreGameScreenModeDto.PERMANENT
        )
      ), result[0]
    )
    assertEquals(
      games[1].copy(
        preGameScreenMode = AndroidPreGameScreenContent(
          backgroundImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_solitaireverse_image.png",
          amountImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_solitaireverse_amount.png",
          badgeText = "Top earners made this week",
          badgeTextColor = "#1276FD",
          buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
          buttonSubText = "Your Adventure Awaits!",
          timerEnd = timerEnd,
          mode = AndroidPreGameScreenModeDto.PERMANENT
        )
      ), result[1]
    )
    assertEquals(
      games[2].copy(
        preGameScreenMode = AndroidPreGameScreenContent(
          backgroundImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_ballbounce_image.png",
          amountImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_ballbounce_amount.png",
          badgeText = "Top earners made this week",
          badgeTextColor = "#1276FD",
          buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
          buttonSubText = "Your Adventure Awaits!",
          timerEnd = timerEnd,
          mode = AndroidPreGameScreenModeDto.PERMANENT
        )
      ), result[2]
    )
    assertEquals(
      games[3].copy(
        preGameScreenMode = AndroidPreGameScreenContent(
          backgroundImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_justplay_image.png",
          amountImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_justplay_amount.png",
          badgeText = "Top earners made this week",
          badgeTextColor = "#1276FD",
          buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
          buttonSubText = "Your Adventure Awaits!",
          timerEnd = timerEnd,
          mode = AndroidPreGameScreenModeDto.PERMANENT
        )
      ), result[3]
    )
  }

  @Test
  fun `SHOULD return games list with correct preGameScreenMode for PRE_GAME_SCREEN_INTRO_PERMANENT_3H_TIMER variation`() = runTest {
    val now = Instant.now()
    timeService.mock({ now() }, now)
    userTimeZoneService.mockUserTime(now)
    abTestingService.mock(
      { assignedVariationValue(USER_ID, ClientExperiment.ANDROID_PRE_GAME_SCREEN) },
      AndroidPreGameScreenVariation.PreGameScreenIntroPermanent3hTimer
    )
    //tm, solitaire, ballbounce and unknown game
    val games = androidGameOfferListStub.subList(0, 3) + androidGameOfferListStub[3].copy(applicationId = "unknown")

    val result = underTest.applyExperiment(USER_ID, games)
    assertEquals(games.size, result.size)
    assertEquals(
      games[0].copy(
        preGameScreenMode = AndroidPreGameScreenContent(
          backgroundImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_tm_image.png",
          amountImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_tm_amount.png",
          badgeText = "Top earners made this week",
          badgeTextColor = "#1276FD",
          buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
          buttonSubText = "Your Adventure Awaits!",
          timerEnd = now.plus(3, ChronoUnit.HOURS).epochSecond,
          mode = AndroidPreGameScreenModeDto.PERMANENT_3H
        )
      ), result[0]
    )
    assertEquals(
      games[1].copy(
        preGameScreenMode = AndroidPreGameScreenContent(
          backgroundImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_solitaireverse_image.png",
          amountImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_solitaireverse_amount.png",
          badgeText = "Top earners made this week",
          badgeTextColor = "#1276FD",
          buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
          buttonSubText = "Your Adventure Awaits!",
          timerEnd = now.plus(3, ChronoUnit.HOURS).epochSecond,
          mode = AndroidPreGameScreenModeDto.PERMANENT_3H
        )
      ), result[1]
    )
    assertEquals(
      games[2].copy(
        preGameScreenMode = AndroidPreGameScreenContent(
          backgroundImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_ballbounce_image.png",
          amountImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_ballbounce_amount.png",
          badgeText = "Top earners made this week",
          badgeTextColor = "#1276FD",
          buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
          buttonSubText = "Your Adventure Awaits!",
          timerEnd = now.plus(3, ChronoUnit.HOURS).epochSecond,
          mode = AndroidPreGameScreenModeDto.PERMANENT_3H
        )
      ), result[2]
    )
    assertEquals(
      games[3].copy(
        preGameScreenMode = AndroidPreGameScreenContent(
          backgroundImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_justplay_image.png",
          amountImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_justplay_amount.png",
          badgeText = "Top earners made this week",
          badgeTextColor = "#1276FD",
          buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
          buttonSubText = "Your Adventure Awaits!",
          timerEnd = now.plus(3, ChronoUnit.HOURS).epochSecond,
          mode = AndroidPreGameScreenModeDto.PERMANENT_3H
        )
      ), result[3]
    )
  }

  @Test
  fun `SHOULD return games list with correct preGameScreenMode for PRE_GAME_SCREEN_INTRO_PERMANENT_3H_TIMER variation AND tracked opened event`() = runTest {
    val now = Instant.now()
    val trackedTime = now.minus(1, ChronoUnit.HOURS)
    timeService.mock({ now() }, now)
    userTimeZoneService.mockUserTime(trackedTime)
    abTestingService.mock(
      { assignedVariationValue(USER_ID, ClientExperiment.ANDROID_PRE_GAME_SCREEN) },
      AndroidPreGameScreenVariation.PreGameScreenIntroPermanent3hTimer
    )
    userGamePersistenceService.mock({ getLastTimeUserOpenedPreGameScreen(USER_ID) }, trackedTime)
    //tm, solitaire, ballbounce and unknown game
    val games = androidGameOfferListStub.subList(0, 3) + androidGameOfferListStub[3].copy(applicationId = "unknown")

    val result = underTest.applyExperiment(USER_ID, games)
    assertEquals(games.size, result.size)
    assertEquals(
      games[0].copy(
        preGameScreenMode = AndroidPreGameScreenContent(
          backgroundImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_tm_image.png",
          amountImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_tm_amount.png",
          badgeText = "Top earners made this week",
          badgeTextColor = "#1276FD",
          buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
          buttonSubText = "Your Adventure Awaits!",
          timerEnd = trackedTime.plus(3, ChronoUnit.HOURS).epochSecond,
          mode = AndroidPreGameScreenModeDto.PERMANENT_3H
        )
      ), result[0]
    )
    assertEquals(
      games[1].copy(
        preGameScreenMode = AndroidPreGameScreenContent(
          backgroundImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_solitaireverse_image.png",
          amountImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_solitaireverse_amount.png",
          badgeText = "Top earners made this week",
          badgeTextColor = "#1276FD",
          buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
          buttonSubText = "Your Adventure Awaits!",
          timerEnd = trackedTime.plus(3, ChronoUnit.HOURS).epochSecond,
          mode = AndroidPreGameScreenModeDto.PERMANENT_3H
        )
      ), result[1]
    )
    assertEquals(
      games[2].copy(
        preGameScreenMode = AndroidPreGameScreenContent(
          backgroundImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_ballbounce_image.png",
          amountImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_ballbounce_amount.png",
          badgeText = "Top earners made this week",
          badgeTextColor = "#1276FD",
          buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
          buttonSubText = "Your Adventure Awaits!",
          timerEnd = trackedTime.plus(3, ChronoUnit.HOURS).epochSecond,
          mode = AndroidPreGameScreenModeDto.PERMANENT_3H
        )
      ), result[2]
    )
    assertEquals(
      games[3].copy(
        preGameScreenMode = AndroidPreGameScreenContent(
          backgroundImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_justplay_image.png",
          amountImage = IMAGES_ROOT + "games/pre_game_screen/android_pre_game_screen_justplay_amount.png",
          badgeText = "Top earners made this week",
          badgeTextColor = "#1276FD",
          buttonText = "Earn <span style=\"color: #0047A6;\">$$$</span> Now",
          buttonSubText = "Your Adventure Awaits!",
          timerEnd = trackedTime.plus(3, ChronoUnit.HOURS).epochSecond,
          mode = AndroidPreGameScreenModeDto.PERMANENT_3H
        )
      ), result[3]
    )
  }

  private suspend fun UserTimeZoneService.mockUserTime(now: Instant) {
    whenever(now.atUserZone(USER_ID)) doReturn now.atOffset(ZoneOffset.UTC)
  }


}