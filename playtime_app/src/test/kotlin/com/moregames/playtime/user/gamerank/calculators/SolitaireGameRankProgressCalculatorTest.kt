package com.moregames.playtime.user.gamerank.calculators

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.messaging.dto.UserChallengeProgressDto
import com.moregames.base.util.ApplicationId
import com.moregames.playtime.user.challenge.progress.achievement.AchievementDto
import com.moregames.playtime.user.gamerank.GameRank
import com.moregames.playtime.user.gamerank.GameRankCalculator
import com.moregames.playtime.user.gamerank.GameRankConfig
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class SolitaireGameRankProgressCalculatorTest {

  private val underTest = SolitaireGameRankProgressCalculator()

  companion object {
    const val USER_ID = "test-user-id"

    val config = GameRankConfig(
      oneStarProgressMax = 10,
      twoStarProgressMax = 30,
      threeStarProgressMax = 100,
      calculator = GameRankCalculator.SOLITAIRE
    )
  }

  @Test
  fun `SHOULD return GameRank THREE with max progress WHEN current progress equals progressMax`() {
    val progressDto = UserChallengeProgressDto.MilestoneProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      milestone = 50
    )
    val currentAchievement = AchievementDto(setOf("level_1", "level_2"))

    val result = underTest.calculateProgress(progressDto, config.progressMax, currentAchievement, config)

    assertThat(result.rank).isEqualTo(GameRank.THREE)
    assertThat(result.progress).isEqualTo(config.progressMax)
    assertThat(result.achievement).isEqualTo(currentAchievement)
  }

  @Test
  fun `SHOULD return previous achievement WHEN levels exceed MAX_LEVELS`() {
    val progressDto = UserChallengeProgressDto.MilestoneProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      milestone = 50
    )
    // Create achievement with MAX_LEVELS levels to trigger the limit
    val largeLevelsSet = (1..500).map { it.toString() }.toSet()
    val currentAchievement = AchievementDto(largeLevelsSet)

    val result = underTest.calculateProgress(progressDto, 10, currentAchievement, config)

    assertThat(result.rank).isEqualTo(GameRank.THREE)
    assertThat(result.progress).isEqualTo(config.progressMax)
    assertThat(result.achievement).isEqualTo(currentAchievement)
  }

  @ParameterizedTest
  @CsvSource(
    "5, ZERO",    // 1 level (milestone 5) -> progress 1 -> rank ZERO (< 10)
    "10, ZERO",   // 1 level (milestone 10) -> progress 1 -> rank ZERO (< 10)
    "25, ZERO",   // 1 level (milestone 25) -> progress 1 -> rank ZERO (< 10)
    "30, ZERO",   // 1 level (milestone 30) -> progress 1 -> rank ZERO (< 10)
    "50, ZERO",   // 1 level (milestone 50) -> progress 1 -> rank ZERO (< 10)
    "100, ZERO",  // 1 level (milestone 100) -> progress 1 -> rank ZERO (< 10)
    "150, ZERO"   // 1 level (milestone 150) -> progress 1 -> rank ZERO (< 10)
  )
  fun `SHOULD return correct rank based on single milestone progress`(
    milestone: Int,
    expectedRank: GameRank
  ) {
    val progressDto = UserChallengeProgressDto.MilestoneProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      milestone = milestone
    )

    val result = underTest.calculateProgress(progressDto, 0, null, config)

    assertThat(result.rank).isEqualTo(expectedRank)
    assertThat(result.progress).isEqualTo(1) // Always 1 level from single milestone
  }

  @Test
  fun `SHOULD handle null currentAchievement correctly`() {
    val progressDto = UserChallengeProgressDto.MilestoneProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      milestone = 15
    )

    val result = underTest.calculateProgress(progressDto, 0, null, config)

    assertThat(result.rank).isEqualTo(GameRank.ZERO) // 1 level -> rank ZERO (< 10)
    assertThat(result.progress).isEqualTo(1) // 1 level from milestone 15
    assertThat(result.achievement).isEqualTo(AchievementDto(setOf("15")))
  }

  @Test
  fun `SHOULD union previous and new achievements`() {
    val progressDto = UserChallengeProgressDto.MilestoneProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      milestone = 25
    )
    val currentAchievement = AchievementDto(setOf("10", "20"))

    val result = underTest.calculateProgress(progressDto, 2, currentAchievement, config)

    assertThat(result.rank).isEqualTo(GameRank.ZERO) // 3 levels -> rank ZERO (< 10)
    assertThat(result.progress).isEqualTo(3) // 3 levels total: 10, 20, 25
    assertThat(result.achievement).isEqualTo(AchievementDto(setOf("10", "20", "25")))
  }

  @ParameterizedTest
  @CsvSource(
    "9, ZERO",     // 9 levels -> rank ZERO (< 10)
    "10, ONE",     // 10 levels -> rank ONE (>= 10, < 30)
    "15, ONE",     // 15 levels -> rank ONE (>= 10, < 30)
    "29, ONE",     // 29 levels -> rank ONE (>= 10, < 30)
    "30, TWO",     // 30 levels -> rank TWO (>= 30, < 100)
    "50, TWO",     // 50 levels -> rank TWO (>= 30, < 100)
    "99, TWO",     // 99 levels -> rank TWO (>= 30, < 100)
    "100, THREE"   // 100 levels -> rank THREE (>= 100)
  )
  fun `SHOULD return correct rank based on number of achievement levels`(
    numLevels: Int,
    expectedRank: GameRank
  ) {
    // Create achievement with specific number of levels
    val levels = (1..numLevels).map { it.toString() }.toSet()
    val currentAchievement = AchievementDto(levels)

    val progressDto = UserChallengeProgressDto.MilestoneProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      milestone = 0 // No new milestone, just test existing achievement
    )

    val result = underTest.calculateProgress(progressDto, numLevels, currentAchievement, config)

    assertThat(result.rank).isEqualTo(expectedRank)
    assertThat(result.progress).isEqualTo(numLevels)
  }

  @Test
  fun `SHOULD cap progress at progressMax`() {
    val progressDto = UserChallengeProgressDto.MilestoneProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      milestone = 150
    )
    // Create achievement that would result in more than progressMax levels
    val largeLevelsSet = (1..120).map { it.toString() }.toSet()
    val currentAchievement = AchievementDto(largeLevelsSet)

    val result = underTest.calculateProgress(progressDto, 50, currentAchievement, config)

    assertThat(result.rank).isEqualTo(GameRank.THREE)
    assertThat(result.progress).isEqualTo(config.progressMax) // Should be capped at 100
  }

  @Test
  fun `SHOULD return empty achievement WHEN milestone is zero`() {
    val progressDto = UserChallengeProgressDto.MilestoneProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      milestone = 0
    )

    val result = underTest.calculateProgress(progressDto, 5, null, config)

    assertThat(result.rank).isEqualTo(GameRank.ZERO)
    assertThat(result.progress).isEqualTo(0) // No new achievement, so no progress change
    assertThat(result.achievement).isEqualTo(AchievementDto.empty())
  }

  @Test
  fun `SHOULD return empty achievement WHEN progressDto is not MilestoneProgressDto`() {
    val progressDto = UserChallengeProgressDto.LevelIdProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      levelId = "level_5"
    )

    val result = underTest.calculateProgress(progressDto, 5, null, config)

    assertThat(result.rank).isEqualTo(GameRank.ZERO)
    assertThat(result.progress).isEqualTo(0) // No new achievement, so no progress change
    assertThat(result.achievement).isEqualTo(AchievementDto.empty())
  }

  @Test
  fun `SHOULD handle ScoreProgressDto correctly`() {
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      score = 1000
    )

    val result = underTest.calculateProgress(progressDto, 5, null, config)

    assertThat(result.rank).isEqualTo(GameRank.ZERO)
    assertThat(result.progress).isEqualTo(0) // No new achievement, so no progress change
    assertThat(result.achievement).isEqualTo(AchievementDto.empty())
  }

  @Test
  fun `SHOULD preserve existing achievement when no new milestone`() {
    val progressDto = UserChallengeProgressDto.MilestoneProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      milestone = 0
    )
    val currentAchievement = AchievementDto(setOf("10", "20"))

    val result = underTest.calculateProgress(progressDto, 2, currentAchievement, config)

    assertThat(result.rank).isEqualTo(GameRank.ZERO) // 2 levels -> rank ZERO (< 10)
    assertThat(result.progress).isEqualTo(2) // Same as current progress since no new achievement
    assertThat(result.achievement).isEqualTo(currentAchievement)
  }

  @Test
  fun `SHOULD handle milestone progression correctly`() {
    // Start with existing achievement
    val currentAchievement = AchievementDto(setOf("1", "2", "3", "4", "5", "6", "7", "8", "9")) // 9 levels

    val progressDto = UserChallengeProgressDto.MilestoneProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      milestone = 10 // Add 10th level
    )

    val result = underTest.calculateProgress(progressDto, 9, currentAchievement, config)

    assertThat(result.rank).isEqualTo(GameRank.ONE) // 10 levels -> rank ONE (>= 10, < 30)
    assertThat(result.progress).isEqualTo(10) // 10 levels total
    assertThat(result.achievement).isEqualTo(AchievementDto(setOf("1", "2", "3", "4", "5", "6", "7", "8", "9", "10")))
  }

  @Test
  fun `SHOULD handle large milestone values correctly`() {
    val progressDto = UserChallengeProgressDto.MilestoneProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      milestone = 999999
    )

    val result = underTest.calculateProgress(progressDto, 0, null, config)

    assertThat(result.rank).isEqualTo(GameRank.ZERO) // 1 level -> rank ZERO (< 10)
    assertThat(result.progress).isEqualTo(1) // 1 level from milestone
    assertThat(result.achievement).isEqualTo(AchievementDto(setOf("999999")))
  }

  @Test
  fun `SHOULD handle negative milestone values correctly`() {
    val progressDto = UserChallengeProgressDto.MilestoneProgressDto(
      userId = USER_ID,
      applicationId = ApplicationId.SOLITAIRE_VERSE_APP_ID,
      milestone = -5
    )

    val result = underTest.calculateProgress(progressDto, 0, null, config)

    assertThat(result.rank).isEqualTo(GameRank.ZERO) // 1 level -> rank ZERO (< 10)
    assertThat(result.progress).isEqualTo(1) // 1 level from milestone
    assertThat(result.achievement).isEqualTo(AchievementDto(setOf("-5")))
  }
}