package com.moregames.playtime.user.usergame

import assertk.assertThat
import assertk.assertions.isEqualTo
import assertk.assertions.isFalse
import assertk.assertions.isTrue
import com.moregames.base.table.DatabaseExtension
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.user.prepareUser
import com.moregames.playtime.user.usergame.table.UserPreGameScreenOpenedTable
import kotlinx.coroutines.runBlocking
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mockito.mock
import java.time.Instant
import java.time.temporal.ChronoUnit
import kotlin.test.assertNotEquals

@ExtendWith(DatabaseExtension::class)
class UserGamePersistenceServiceTest(private val database: Database) {
  private val timeService: TimeService = mock()
  private val service = UserGamePersistenceService(timeService, database)

  @Test
  fun `SHOULD insert new record ON trackUserOpenedPreGameScreenReturnNew WHEN NOT user tracked and update after that`() {
    val userId = database.prepareUser()
    val now = Instant.now()

    runBlocking { service.trackUserOpenedPreGameScreenReturnNew(userId) }.let { actual ->
      assertThat(actual).isTrue()
    }

    val first = runBlocking {
      service.getLastTimeUserOpenedPreGameScreen(userId)
    }

    Thread.sleep(1000)

    timeService.mock({ now() }, now)

    runBlocking { service.trackUserOpenedPreGameScreenReturnNew(userId) }.let { actual ->
      assertThat(actual).isFalse()
    }

    val second = runBlocking {
      service.getLastTimeUserOpenedPreGameScreen(userId)
    }

    assertThat(first).isEqualTo(second)

    Thread.sleep(1000)

    timeService.mock({ now() }, now.plus(1, ChronoUnit.DAYS))

    runBlocking { service.trackUserOpenedPreGameScreenReturnNew(userId) }.let { actual ->
      assertThat(actual).isFalse()
    }

    val third = runBlocking {
      service.getLastTimeUserOpenedPreGameScreen(userId)
    }

    assertNotEquals(second, third)

    val count = transaction(database) {
      UserPreGameScreenOpenedTable
        .slice(UserPreGameScreenOpenedTable.userId)
        .select { UserPreGameScreenOpenedTable.userId eq userId }
        .count()
    }
    assertThat(count).isEqualTo(1)
  }
}