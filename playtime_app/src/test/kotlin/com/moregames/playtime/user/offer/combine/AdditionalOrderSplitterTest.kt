package com.moregames.playtime.user.offer.combine

import assertk.assertThat
import assertk.assertions.containsExactly
import com.moregames.base.offers.dto.OfferAction
import com.moregames.playtime.utils.additionalOfferStub
import org.junit.jupiter.api.Test

class AdditionalOrderSplitterTest {

  private val underTest = AdditionalOfferSplitter()

  @Test
  fun `SHOULD get two lists ON splitAdditionalOffers`() {
    val a1 = additionalOfferStub
    val a2 = additionalOfferStub.copy(id = 2)
    val a3 = additionalOfferStub.copy(id = 3, action = OfferAction.WELCOME_COINS)
    val a4 = additionalOfferStub.copy(id = 4, action = OfferAction.GAID_OFFER)
    val a5 = additionalOfferStub.copy(id = 5, action = OfferAction.GAID_OFFER)
    val a6 = additionalOfferStub.copy(id = 6)
    val additionalOfferList = listOf(a1, a2, a3, a4, a5, a6)
    val result = underTest.splitAdditionalOffers(additionalOfferList)
    assertThat(result.first).containsExactly(a3, a4, a5)
    assertThat(result.second).containsExactly(a1, a2, a6)
  }
}