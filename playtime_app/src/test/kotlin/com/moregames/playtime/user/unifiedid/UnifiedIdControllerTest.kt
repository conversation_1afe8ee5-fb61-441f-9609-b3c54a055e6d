package com.moregames.playtime.user.unifiedid

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.playtime.user.unifiedid.client.UnifiedIdTokenResponse
import com.moregames.playtime.util.installDefaultContentNegotiation
import io.ktor.application.*
import io.ktor.http.*
import io.ktor.routing.*
import io.ktor.server.testing.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import org.mockito.kotlin.stub
import java.time.Instant

class UnifiedIdControllerTest {
  private val unifiedIdService: UnifiedIdService = mock()

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD return correct status ON unifiedId status call`(status: Boolean) = withTestApplication(controller()) {
    unifiedIdService.stub {
      onBlocking { checkStatus("123") } doReturn status
    }

    val result = handleRequest(
      method = HttpMethod.Get,
      uri = "/unifiedid/status?userId=123"
    )

    assertThat(result.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(result.response.content).isEqualTo("{\"shouldUseUnifiedId\":$status}")
  }

  @Test
  fun `SHOULD return correctly WHEN generate token call`() = withTestApplication(controller()) {
    val now = Instant.EPOCH
    val tokenResponse = UnifiedIdTokenResponse.UnifiedIdTokenResponseBody(
      advertisingToken = "advertisingToken",
      identityExpiresMillis = now.toEpochMilli(),
      refreshExpiresMillis = now.toEpochMilli(),
      refreshFromMillis = now.toEpochMilli(),
      "refreshResponseKey",
      "refreshTokenValue"
    )
    unifiedIdService.stub {
      onBlocking { generateToken("123") } doReturn GenerateTokenResult.Success(tokenResponse)
    }

    val result = handleRequest(
      method = HttpMethod.Get,
      uri = "/unifiedid/token?userId=123"
    )

    assertThat(result.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(result.response.content).isEqualTo("{\"advertisingToken\":\"advertisingToken\",\"identityExpires\":\"1970-01-01T00:00:00Z\",\"refreshExpires\":\"1970-01-01T00:00:00Z\",\"refreshFrom\":\"1970-01-01T00:00:00Z\"}")
  }

  @Test
  fun `SHOULD return correctly WHEN refresh token call`() = withTestApplication(controller()) {
    val now = Instant.EPOCH
    val tokenResponse = UnifiedIdTokenResponse.UnifiedIdTokenResponseBody(
      advertisingToken = "advertisingToken",
      identityExpiresMillis = now.toEpochMilli(),
      refreshExpiresMillis = now.toEpochMilli(),
      refreshFromMillis = now.toEpochMilli(),
      "refreshResponseKey",
      "refreshTokenValue"
    )
    unifiedIdService.stub {
      onBlocking { refreshToken("123") } doReturn RefreshTokenResult.Success(tokenResponse)
    }

    val result = handleRequest(
      method = HttpMethod.Get,
      uri = "/unifiedid/refresh?userId=123"
    )

    assertThat(result.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(result.response.content).isEqualTo("{\"advertisingToken\":\"advertisingToken\",\"identityExpires\":\"1970-01-01T00:00:00Z\",\"refreshExpires\":\"1970-01-01T00:00:00Z\",\"refreshFrom\":\"1970-01-01T00:00:00Z\"}")
  }

  @Test
  fun `SHOULD return 400 on Expired refresh token`() = withTestApplication(controller()) {
    unifiedIdService.stub {
      onBlocking { refreshToken("123") } doReturn RefreshTokenResult.Expired
    }

    val result = handleRequest(
      method = HttpMethod.Get,
      uri = "/unifiedid/refresh?userId=123"
    )

    assertThat(result.response.status()).isEqualTo(HttpStatusCode.BadRequest)
  }

  @Test
  fun `SHOULD return 412 on Optout refresh token`() = withTestApplication(controller()) {
    unifiedIdService.stub {
      onBlocking { refreshToken("123") } doReturn RefreshTokenResult.Optout
    }

    val result = handleRequest(
      method = HttpMethod.Get,
      uri = "/unifiedid/refresh?userId=123"
    )

    assertThat(result.response.status()).isEqualTo(HttpStatusCode.PreconditionFailed)
  }

  @Test
  fun `SHOULD return 412 on Optout generate token`() = withTestApplication(controller()) {
    unifiedIdService.stub {
      onBlocking { generateToken("123") } doReturn GenerateTokenResult.Optout
    }

    val result = handleRequest(
      method = HttpMethod.Get,
      uri = "/unifiedid/token?userId=123"
    )

    assertThat(result.response.status()).isEqualTo(HttpStatusCode.PreconditionFailed)
  }

  private fun controller(): Application.() -> Unit = {
    install(IgnoreTrailingSlash)
    installDefaultContentNegotiation()
    routing {
      UnifiedIdController(unifiedIdService).startRouting(this)
    }
  }
}