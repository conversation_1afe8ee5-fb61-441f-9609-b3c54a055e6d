package com.moregames.playtime.cashstreak


import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.app.BuildVariant
import com.moregames.base.bus.MessageBus
import com.moregames.base.user.UserBonusBalanceType
import com.moregames.base.util.TEST_IO_SCOPE
import com.moregames.base.util.mock
import com.moregames.playtime.buseffects.PushNotificationEffect
import com.moregames.playtime.cashstreak.model.CashStreakReward
import com.moregames.playtime.cashstreak.model.CashStreakRewardType
import com.moregames.playtime.notifications.PushNotification.AndroidPushNotification.DayStreakRewardReadyNotification
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.user.UserService
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.kotlin.*
import java.math.BigDecimal
import java.time.Instant
import kotlin.test.Test

@OptIn(ExperimentalCoroutinesApi::class)
class CashStreakRewardsServiceTest {
  private val cashStreakPersistenceService: UserCashStreakPersistenceService = mock()
  private val messageBus: MessageBus = mock()
  private val userService: UserService = mock()
  private val rewardingFacade: RewardingFacade = mock()

  private val service = CashStreakRewardsService(
    cashStreakPersistenceService = cashStreakPersistenceService,
    userService = userService,
    rewardingFacade = rewardingFacade,
    messageBus = messageBus,
    coroutineScope = { TEST_IO_SCOPE },
    buildVariantProvider = { BuildVariant.PRODUCTION }
  )

  companion object {
    const val USER_ID = "userId"
    private val reward = CashStreakReward(achievementDay = 1, type = CashStreakRewardType.COINS, value = BigDecimal.ONE, bigReward = false)
    private val allRewards = (1..50).map {
      when {
        it in listOf(5, 10, 15, 20) ->
          CashStreakReward(achievementDay = it, CashStreakRewardType.COINS, BigDecimal("5000") * it.toBigDecimal(), true)

        else ->
          CashStreakReward(achievementDay = it, CashStreakRewardType.COINS, BigDecimal("5000") * it.toBigDecimal(), false)
      }
    }
  }

  init {
    Dispatchers.setMain(StandardTestDispatcher())
  }

  @BeforeEach
  fun init() {
    cashStreakPersistenceService.mock({ loadAllRewardsOrdered() }, allRewards)
  }

  @Test
  fun `SHOULD trigger persistence layer ON dropRewards`() {
    runBlocking {
      service.dropRewards(USER_ID)
    }

    verifyBlocking(cashStreakPersistenceService) { dropRewards(USER_ID) }
  }

  @Test
  fun `SHOULD trigger persistence layer ON getUnclaimedRewards`() {
    cashStreakPersistenceService.mock({ getUnclaimedRewards(USER_ID) }, listOf(reward, reward))

    runBlocking {
      service.getUnclaimedRewards(USER_ID)
    }.let { assertThat(it).isEqualTo(listOf(reward, reward)) }

    verifyBlocking(cashStreakPersistenceService) { getUnclaimedRewards(USER_ID) }
  }


  @Test
  fun `SHOULD return 4 next rewards ON getNextRewards WHEN nog big offers among first 4 rewards`() = runTest {
    service.getNextRewards(0, 4)
      .let {
        assertThat(it).isEqualTo(
          listOf(
            reward.copy(achievementDay = 1, value = BigDecimal("5000")),
            reward.copy(achievementDay = 2, value = BigDecimal("10000")),
            reward.copy(achievementDay = 3, value = BigDecimal("15000")),
            reward.copy(achievementDay = 5, value = BigDecimal("25000"), bigReward = true),
          )
        )
      }

    verifyBlocking(cashStreakPersistenceService) { loadAllRewardsOrdered() }
  }

  @Test
  fun `SHOULD return 3 next rewards ON getNextRewards WHEN there is a big offer at the 4th place`() = runTest {
    service.getNextRewards(1, 4)
      .let {
        assertThat(it).isEqualTo(
          listOf(
            reward.copy(achievementDay = 2, value = BigDecimal("10000")),
            reward.copy(achievementDay = 3, value = BigDecimal("15000")),
            reward.copy(achievementDay = 4, value = BigDecimal("20000")),
            reward.copy(achievementDay = 5, value = BigDecimal("25000"), bigReward = true),
          )
        )
      }

    verifyBlocking(cashStreakPersistenceService) { loadAllRewardsOrdered() }
  }

  @Test
  fun `SHOULD return 4 next rewards including big ones ON getNextRewards`() = runTest {
    service.getNextRewards(9, 4)
      .let {
        assertThat(it).isEqualTo(
          listOf(
            reward.copy(achievementDay = 10, value = BigDecimal("50000"), bigReward = true),
            reward.copy(achievementDay = 11, value = BigDecimal("55000")),
            reward.copy(achievementDay = 12, value = BigDecimal("60000")),
            reward.copy(achievementDay = 15, value = BigDecimal("75000"), bigReward = true),
          )
        )
      }

    verifyBlocking(cashStreakPersistenceService) { loadAllRewardsOrdered() }
  }

  @Test
  fun `SHOULD return 3 next rewards ON getNextRewards WHEN we have no more rewards left`() = runTest {
    service.getNextRewards(47, 4)
      .let {
        assertThat(it).isEqualTo(
          listOf(
            reward.copy(achievementDay = 48, value = BigDecimal("240000")),
            reward.copy(achievementDay = 49, value = BigDecimal("245000")),
            reward.copy(achievementDay = 50, value = BigDecimal("250000")),
          )
        )
      }

    verifyBlocking(cashStreakPersistenceService) { loadAllRewardsOrdered() }
  }

  @Test
  fun `SHOULD return no rewards ON getNextRewards WHEN we have no more rewards left`() = runTest {
    service.getNextRewards(50, 4)
      .let {
        assertThat(it).isEqualTo(
          emptyList()
        )
      }

    verifyBlocking(cashStreakPersistenceService) { loadAllRewardsOrdered() }
  }

  @Test
  fun `SHOULD trigger persistence layer ON getCurrentBoosters`() {
    cashStreakPersistenceService.mock({ getCurrentEarningsBooster(USER_ID) }, reward)

    runBlocking {
      service.getCurrentBoosters(USER_ID)
    }.let { assertThat(it).isEqualTo(listOf(reward)) }

    verifyBlocking(cashStreakPersistenceService) { getCurrentEarningsBooster(USER_ID) }
  }

  @ParameterizedTest
  @ValueSource(strings = ["null", "zero"])
  fun `SHOULD trigger claim rewards but not add bonus coins ON claimAllUnclaimedRewards WHEN no bonus coins claimed`(option: String) {
    cashStreakPersistenceService.mock({ claimAllUnclaimedRewardsReturnBonusCoinsAmount(USER_ID) }, BigDecimal.ZERO.takeIf { option == "zero" })

    runBlocking {
      service.claimAllUnclaimedRewards(USER_ID)
    }

    verifyBlocking(cashStreakPersistenceService) { claimAllUnclaimedRewardsReturnBonusCoinsAmount(USER_ID) }
    verifyNoInteractions(rewardingFacade)
  }

  @Test
  fun `SHOULD trigger claim rewards and add bonus coins ON claimAllUnclaimedRewards WHEN bonus coins were claimed`() {
    cashStreakPersistenceService.mock({ claimAllUnclaimedRewardsReturnBonusCoinsAmount(USER_ID) }, BigDecimal("25000"))
    rewardingFacade.mock({ inflatingCoinsMultiplier(USER_ID) }, 200)
    userService.mock({ getUser(USER_ID) }, userDtoStub)

    runBlocking {
      service.claimAllUnclaimedRewards(USER_ID)
    }

    verifyBlocking(rewardingFacade) { addBonusCoinsIfNotExists(eq(USER_ID), eq(userDtoStub.appPlatform), eq(125), eq(UserBonusBalanceType.CASH_STREAK), any()) }
  }

  @Test
  fun `SHOULD give reward ON tryToGiveRewardByDay WHEN there is a reward for the day`() {
    val uniqueKey = Instant.now().toString()
    cashStreakPersistenceService.mock({ getRewardIdByAchievementDay(5) }, 51)

    runBlocking {
      service.tryToGiveRewardByDay(USER_ID, 5, uniqueKey)
    }

    verifyBlocking(cashStreakPersistenceService) { getRewardIdByAchievementDay(5) }
    verifyBlocking(cashStreakPersistenceService) { giveRewardToUser(USER_ID, 51, uniqueKey) }
    verifyBlocking(messageBus) { publishAsync(PushNotificationEffect(DayStreakRewardReadyNotification(USER_ID))) }
  }

  @Test
  fun `SHOULD give nothing ON tryToGiveRewardByDay WHEN there is no a reward for the day`() {
    val uniqueKey = Instant.now().toString()
    cashStreakPersistenceService.mock({ getRewardIdByAchievementDay(5) }, null)

    runBlocking {
      service.tryToGiveRewardByDay(USER_ID, 5, uniqueKey)
    }

    verifyBlocking(cashStreakPersistenceService) { getRewardIdByAchievementDay(5) }
    verifyNoMoreInteractions(cashStreakPersistenceService)
    verifyNoInteractions(messageBus)
  }

}