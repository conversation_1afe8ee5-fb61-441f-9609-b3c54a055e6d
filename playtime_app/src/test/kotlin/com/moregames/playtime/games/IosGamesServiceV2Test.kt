package com.moregames.playtime.games

import assertk.all
import assertk.assertThat
import assertk.assertions.hasSize
import assertk.assertions.index
import assertk.assertions.isEqualTo
import assertk.assertions.isNotNull
import com.moregames.base.util.mock
import com.moregames.playtime.app.IMAGES_ROOT
import com.moregames.playtime.app.ImageService
import com.moregames.playtime.games.webgl.WebglAdInfoService
import com.moregames.playtime.ios.IosQualifiedUserService
import com.moregames.playtime.ios.dto.GameApiV2Dto
import com.moregames.playtime.translations.UserTranslationService
import com.moregames.playtime.user.UserPersistenceService.GamePlayStatusDto
import com.moregames.playtime.user.UserService
import com.moregames.playtime.utils.iosPreGameScreenStub
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.kotlin.*
import java.time.Instant
import java.util.*

@OptIn(ExperimentalCoroutinesApi::class)
class IosGamesServiceV2Test {
  private val gamePersistenceService: GamePersistenceService = mock {
    onBlocking { loadIosPreGameScreens() } doReturn iosPreGameScreenStub
  }
  private val translationService: UserTranslationService = mock {
    onBlocking { tryTranslate(eq("description"), eq(Locale.ENGLISH), any()) } doReturn "descriptionTranslation"
    onBlocking { tryTranslate(eq("infoTextInstallTop"), eq(Locale.ENGLISH), any()) } doReturn "infoTextInstallTopTranslation"
  }
  private val imageService: ImageService = mock {
    on { toUrl(any()) } doAnswer { IMAGES_ROOT + it.getArgument(0) as String }
  }
  private val userService: UserService = mock()
  private val webglAdInfoService: WebglAdInfoService = mock()
  private val iosQualifiedUserService: IosQualifiedUserService = mock()

  private val underTest = IosGamesServiceV2(
    gamePersistenceService = gamePersistenceService,
    userService = userService,
    translationService = translationService,
    imageService = imageService,
    webglAdInfoService = webglAdInfoService,
    iosQualifiedUserService = iosQualifiedUserService
  )

  init {
    Dispatchers.setMain(StandardTestDispatcher())
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD return all games ON loadNewGames when no played games`(isUserQualified: Boolean) = runTest {
    val userId = UUID.randomUUID().toString()
    userService.mock({ loadUserGameCoins(userId) }, emptyMap())
    iosQualifiedUserService.mock({ isUserQualified(userId) }, isUserQualified)
    gamePersistenceService.mock(
      { loadVisibleIosGamesExcludeIds(emptySet()) }, listOf(
        iosGameOfferWithWebglStub.copy(applicationId = "com.gimica.treasuremaster"),
        iosGameOfferWithWebglStub.copy(id = 2),
        iosGameOfferWithWebglStub.copy(id = 3),
        iosGameOfferWithWebglStub.copy(id = 4),
        iosGameOfferWithWebglStub.copy(id = 5),
        iosGameOfferWithWebglStub.copy(id = 6),
        iosGameOfferWithWebglStub.copy(id = 7, webglGame = null),
      )
    )
    webglAdInfoService.mock(
      { findAdInfo(eq(userId), any()) }, WebglAdInfoService.WebglGameAdInfo(
        placements = WebglAdInfoService.AdInfo("bannerPlacement", "interstitialPlacement", "rewardedPlacement"),
        adUnitIds = WebglAdInfoService.AdInfo("bannerAdUnitId", "interstitialAdUnitId", "rewardedAdUnitId")
      )
    )

    val result = underTest.loadNewGames(userId, Locale.ENGLISH)

    assertThat(result).all {
      if (isUserQualified) {
        hasSize(7) // for qualified users, we show all games
      } else {
        hasSize(6)
      }
      index(0).all {
        transform { it.id }.isEqualTo(1)
        transform { it.name }.isEqualTo("name")
        transform { it.description }.isEqualTo("descriptionTranslation")
        transform { it.buttonText }.isEqualTo("infoTextInstallTopTranslation")
        transform { it.iconUrl }.isEqualTo(IMAGES_ROOT + "iconFilename")
        transform { it.landscapeImageUrl }.isEqualTo(IMAGES_ROOT + "imageFilename")
        transform { it.appStoreId }.isEqualTo("iosApplicationIdValue")
        transform { it.gameLaunchUrl }.isEqualTo("iosGameUrlValue")
        transform { it.portraitImageUrl }.isEqualTo(IMAGES_ROOT + "ios_treasuremaster_trending_icon.jpg")
        if (isUserQualified) {
          transform { it.mode }.isEqualTo(GameApiV2Dto.IosGameMode.NATIVE)
        } else {
          transform { it.mode }.isEqualTo(GameApiV2Dto.IosGameMode.WEB)
        }
        transform { it.webglGame }.isNotNull().all {
          transform { it.url }.isEqualTo("http://dummy")
          transform { it.adInfo }.isEqualTo(
            GameApiV2Dto.AdInfo(
              banner = GameApiV2Dto.AdTypeInfo("bannerAdUnitId", "bannerPlacement"),
              interstitial = GameApiV2Dto.AdTypeInfo("interstitialAdUnitId", "interstitialPlacement"),
              rewarded = GameApiV2Dto.AdTypeInfo("rewardedAdUnitId", "rewardedPlacement")
            )
          )
        }
      }
    }
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD return all games ON loadAllGames even if there are played game`(isUserQualified: Boolean) = runTest {
    val userId = UUID.randomUUID().toString()
    userService.mock(
      { loadUserGameCoins(userId) }, mapOf(
        1 to 1,
        2 to 1,
        3 to 1,
      )
    )
    iosQualifiedUserService.mock({ isUserQualified(userId) }, isUserQualified)
    gamePersistenceService.mock(
      { loadVisibleIosGamesExcludeIds(emptySet()) }, listOf(
        iosGameOfferWithWebglStub.copy(applicationId = "com.gimica.treasuremaster"),
        iosGameOfferWithWebglStub.copy(id = 2),
        iosGameOfferWithWebglStub.copy(id = 3),
        iosGameOfferWithWebglStub.copy(id = 4),
        iosGameOfferWithWebglStub.copy(id = 5),
        iosGameOfferWithWebglStub.copy(id = 6),
        iosGameOfferWithWebglStub.copy(id = 7, webglGame = null),
      )
    )
    webglAdInfoService.mock(
      { findAdInfo(eq(userId), any()) }, WebglAdInfoService.WebglGameAdInfo(
        placements = WebglAdInfoService.AdInfo("bannerPlacement", "interstitialPlacement", "rewardedPlacement"),
        adUnitIds = WebglAdInfoService.AdInfo("bannerAdUnitId", "interstitialAdUnitId", "rewardedAdUnitId")
      )
    )

    val result = underTest.loadAllGames(userId, Locale.ENGLISH)

    assertThat(result).all {
      if (isUserQualified) {
        hasSize(7) // for qualified users, we show all games
      } else {
        hasSize(6)
      }
      index(0).all {
        transform { it.id }.isEqualTo(1)
        transform { it.name }.isEqualTo("name")
        transform { it.description }.isEqualTo("descriptionTranslation")
        transform { it.buttonText }.isEqualTo("infoTextInstallTopTranslation")
        transform { it.iconUrl }.isEqualTo(IMAGES_ROOT + "iconFilename")
        transform { it.landscapeImageUrl }.isEqualTo(IMAGES_ROOT + "imageFilename")
        transform { it.appStoreId }.isEqualTo("iosApplicationIdValue")
        transform { it.gameLaunchUrl }.isEqualTo("iosGameUrlValue")
        transform { it.portraitImageUrl }.isEqualTo(IMAGES_ROOT + "ios_treasuremaster_trending_icon.jpg")
        if (isUserQualified) {
          transform { it.mode }.isEqualTo(GameApiV2Dto.IosGameMode.NATIVE)
        } else {
          transform { it.mode }.isEqualTo(GameApiV2Dto.IosGameMode.WEB)
        }
        transform { it.webglGame }.isNotNull().all {
          transform { it.url }.isEqualTo("http://dummy")
          transform { it.adInfo }.isEqualTo(
            GameApiV2Dto.AdInfo(
              banner = GameApiV2Dto.AdTypeInfo("bannerAdUnitId", "bannerPlacement"),
              interstitial = GameApiV2Dto.AdTypeInfo("interstitialAdUnitId", "interstitialPlacement"),
              rewarded = GameApiV2Dto.AdTypeInfo("rewardedAdUnitId", "rewardedPlacement")
            )
          )
        }
      }
    }
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD return played games ON loadPlayedGames`(isUserQualified: Boolean) = runTest {
    val userId = UUID.randomUUID().toString()
    userService.mock(
      { loadUserGameCoins(userId) }, mapOf(
        1 to GamePlayStatusDto(1, 1, true, null, Instant.parse("2021-01-01T00:00:00Z")),
        2 to GamePlayStatusDto(2, 1, true, null, Instant.parse("2021-01-02T00:00:00Z")),
        3 to GamePlayStatusDto(3, 1, true, null, Instant.parse("2021-01-03T00:00:00Z")),
      )
    )
    iosQualifiedUserService.mock({ isUserQualified(userId) }, isUserQualified)
    webglAdInfoService.mock(
      { findAdInfo(eq(userId), any()) }, WebglAdInfoService.WebglGameAdInfo(
        placements = WebglAdInfoService.AdInfo("bannerPlacement", "interstitialPlacement", "rewardedPlacement"),
        adUnitIds = WebglAdInfoService.AdInfo("bannerAdUnitId", "interstitialAdUnitId", "rewardedAdUnitId")
      )
    )
    gamePersistenceService.mock(
      { loadVisibleIosGamesByIds(setOf(1, 2, 3)) }, listOf(
        iosGameOfferWithWebglStub.copy(id = 1, applicationId = "com.gimica.treasuremaster"),
        iosGameOfferWithWebglStub.copy(id = 2, webglGame = null),
        iosGameOfferWithWebglStub.copy(id = 3, applicationId = "com.gimica.madsmash"),
      )
    )

    val actual = underTest.loadPlayedGames(userId, Locale.ENGLISH)

    assertThat(actual).all {
      if (isUserQualified) {
        hasSize(3)
        transform { game -> game.map { it.id } }.isEqualTo(listOf(3, 2, 1))
      } else {
        hasSize(2)
        transform { game -> game.map { it.id } }.isEqualTo(listOf(3, 1))
      }
      index(0).all {
        transform { it.id }.isEqualTo(3)
        transform { it.name }.isEqualTo("name")
        transform { it.description }.isEqualTo("descriptionTranslation")
        transform { it.buttonText }.isEqualTo("infoTextInstallTopTranslation")
        transform { it.iconUrl }.isEqualTo(IMAGES_ROOT + "iconFilename")
        transform { it.landscapeImageUrl }.isEqualTo(IMAGES_ROOT + "imageFilename")
        transform { it.appStoreId }.isEqualTo("iosApplicationIdValue")
        transform { it.gameLaunchUrl }.isEqualTo("iosGameUrlValue")
        transform { it.portraitImageUrl }.isEqualTo(IMAGES_ROOT + "ios_madsmash_trending_icon.jpg")
        if (isUserQualified) {
          transform { it.mode }.isEqualTo(GameApiV2Dto.IosGameMode.NATIVE)
        } else {
          transform { it.mode }.isEqualTo(GameApiV2Dto.IosGameMode.WEB)
        }
        transform { it.webglGame }.isNotNull().all {
          transform { it.url }.isEqualTo("http://dummy")
          transform { it.adInfo }.isEqualTo(
            GameApiV2Dto.AdInfo(
              banner = GameApiV2Dto.AdTypeInfo("bannerAdUnitId", "bannerPlacement"),
              interstitial = GameApiV2Dto.AdTypeInfo("interstitialAdUnitId", "interstitialPlacement"),
              rewarded = GameApiV2Dto.AdTypeInfo("rewardedAdUnitId", "rewardedPlacement")
            )
          )
        }
      }
    }
  }

  private companion object {
    val iosGameOfferWithWebglStub = IosGameOffer(
      id = 1,
      applicationId = "applicationId",
      name = "name",
      description = "description",
      iconFilename = "iconFilename",
      imageFilename = "imageFilename",
      orderKey = 1,
      infoTextInstall = "infoTextInstallTop",
      iosApplicationId = "iosApplicationIdValue",
      iosGameUrl = "iosGameUrlValue",
      webglGame = IosGameOffer.Webgl("http://dummy")
    )
  }
}