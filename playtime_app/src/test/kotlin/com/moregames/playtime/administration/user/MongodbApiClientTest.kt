package com.moregames.playtime.administration.user

import assertk.assertThat
import assertk.assertions.isEqualTo
import assertk.assertions.isNull
import com.moregames.base.secret.SecretService
import com.moregames.base.util.mock
import com.moregames.playtime.administration.user.MongodbApiClient.Companion.mongodbClusterName
import com.moregames.playtime.administration.user.MongodbApiClient.Companion.mongodbDataApiUrl
import com.moregames.playtime.app.PlaytimeSecrets
import io.ktor.client.*
import io.ktor.client.engine.mock.*
import io.ktor.client.features.json.*
import io.ktor.client.features.json.serializer.*
import io.ktor.http.*
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock

@Suppress("EXPERIMENTAL_API_USAGE")
class MongodbApiClientTest {
  private val secretService: SecretService = mock()

  private val service = MongodbApiClient(secretService, mockHttpClient())
  private val serviceWithExceptionOnCall = MongodbApiClient(secretService, mockHttpClientWithConnectionError())

  private val mongoDbApiKey = "mongodb-api-key"

  private val findRequest = MongodbApiClient.MongodbDataApiRequestDto(
    dataSource = mongodbClusterName,
    database = "facetec",
    collection = "Session",
    projection = mapOf("callData.tid" to 1),
    filter = mapOf("externalDatabaseRefID" to MongodbApiClient.MongodbDataApiRequestDto.FilterEntry(regex = "^user1"))
  )

  private val deleteOneRequest = MongodbApiClient.MongodbDataApiDeleteOneRequestDto(
    dataSource = mongodbClusterName,
    database = "facetec",
    collection = "Session",
    filter = mapOf("externalDatabaseRefID" to "session1")
  )

  @BeforeEach
  fun before() {
    secretService.mock({ secretValue(PlaytimeSecrets.FACETEC_MONGODB7_DATA_API_KEY) }, mongoDbApiKey)
  }

  @Test
  fun `SHOULD build correctly request without filter`() {
    val json = """{"dataSource":"$mongodbClusterName","database":"facetec","collection":"Session","filter":{"externalDatabaseRefID":"session1"}}""".trimMargin()
    val actual = Json.encodeToString(deleteOneRequest)
    assertThat(actual).isEqualTo(json)
  }

  @Test
  fun `SHOULD build correctly request with regex filter`() {
    val json =
      """{"dataSource":"$mongodbClusterName","database":"facetec","collection":"Session","projection":{"callData.tid":1},"filter":{"externalDatabaseRefID":{"${'$'}regex":"^user1"}}}""".trimMargin()
    val actual = Json.encodeToString(findRequest)
    assertThat(actual).isEqualTo(json)
  }

  @Test
  fun `SHOULD return users tids ON getUserImageTids`() {
    runBlocking { service.getUserImageTids("user1") }
      .also {
        assertThat(it).isEqualTo(listOf("abcde-123001", "abcde-123002"))
      }
  }

  @Test
  fun `SHOULD return 1 ON  deleteFace`() {
    runBlocking { service.deleteFace("session1") }
      .also {
        assertThat(it).isEqualTo(1)
      }
  }

  @Test
  fun `SHOULD return null ON deleteFace WHEN ConnectTimeoutException`() {
    runBlocking { serviceWithExceptionOnCall.deleteFace("session1") }
      .also {
        assertThat(it).isNull()
      }
  }

  private fun mockHttpClient() = HttpClient(MockEngine) {
    install(JsonFeature) {
      val json = kotlinx.serialization.json.Json {
        ignoreUnknownKeys = true
        encodeDefaults = false
      }
      serializer = KotlinxSerializer(json)
    }
    engine {
      addHandler { request ->
        when (request.url.toString()) {
          "$mongodbDataApiUrl/find" -> {
            val body = Json.decodeFromString<MongodbApiClient.MongodbDataApiRequestDto>(
              request.body.toByteArray().decodeToString()
            )

            assertThat(request.headers["api-key"]).isEqualTo(mongoDbApiKey)
            assertThat(body).isEqualTo(findRequest)

            respond(
              """
{
    "documents": [
        {
            "_id": "abc123001",
            "callData": {
                "tid": "abcde-123001"
            }
        },
        {
            "_id": "abc123002",
            "callData": {
                "tid": "abcde-123002"
            }
        }
    ]
}
              """.trimIndent(),
              HttpStatusCode.OK,
              headersOf("Content-Type", ContentType.Application.Json.toString())
            )
          }

          "$mongodbDataApiUrl/deleteOne" -> {
            val body = Json.decodeFromString<MongodbApiClient.MongodbDataApiDeleteOneRequestDto>(
              request.body.toByteArray().decodeToString()
            )

            assertThat(request.headers["api-key"]).isEqualTo(mongoDbApiKey)
            assertThat(body).isEqualTo(deleteOneRequest)

            respond(
              """
{
    "deletedCount": 1
}
              """.trimIndent(),
              HttpStatusCode.OK,
              headersOf("Content-Type", ContentType.Application.Json.toString())
            )
          }

          else -> error("Unhandled ${request.url.fullPath}")
        }
      }
    }
  }

  private fun mockHttpClientWithConnectionError() = HttpClient(MockEngine) {
    install(JsonFeature) {
      val json = kotlinx.serialization.json.Json {
        ignoreUnknownKeys = true
        encodeDefaults = false
      }
      serializer = KotlinxSerializer(json)
    }
    engine {
      addHandler { request ->
        when (request.url.toString()) {
          "$mongodbDataApiUrl/deleteOne" -> {
            throw io.ktor.network.sockets.ConnectTimeoutException("err")
          }

          else -> error("Unhandled ${request.url.fullPath}")
        }
      }
    }
  }
}