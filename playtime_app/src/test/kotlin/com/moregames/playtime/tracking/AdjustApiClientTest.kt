package com.moregames.playtime.tracking

import assertk.assertThat
import assertk.assertions.isEqualTo
import assertk.assertions.isNull
import com.google.common.net.HttpHeaders
import com.moregames.base.app.BuildVariant
import com.moregames.base.dto.AppPlatform.IOS
import com.moregames.base.dto.TrackingDataType.IDFV
import com.moregames.base.featureflags.FeatureFlagsFacade
import com.moregames.base.http.MockedHandler
import com.moregames.base.http.mockUrl
import com.moregames.base.http.mockedHttpClient
import com.moregames.base.messaging.dto.AdMarketEvent
import com.moregames.base.messaging.dto.AdMarketEvent.EventType.EARNED_WITHIN_2_DAYS_0_1_DOLLARS
import com.moregames.base.messaging.dto.AdMarketEvent.EventType.SPECIFIC_AMOUNT_REVENUE
import com.moregames.base.secret.BaseSecrets
import com.moregames.base.secret.SecretService
import com.moregames.base.util.mock
import com.moregames.playtime.app.PlaytimeFeatureFlags
import com.moregames.playtime.app.PlaytimeSecrets
import com.moregames.playtime.app.messaging.dto.AdjustCustomEvent
import com.moregames.playtime.util.defaultJsonConverter
import io.github.resilience4j.circuitbreaker.CallNotPermittedException
import io.github.resilience4j.circuitbreaker.CircuitBreaker.State
import io.ktor.client.*
import io.ktor.client.engine.mock.*
import io.ktor.client.features.*
import io.ktor.http.*
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.encodeToString
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import java.time.Instant
import kotlin.test.assertFailsWith

class AdjustApiClientTest {
  private val httpClient: HttpClient
  private val handler: MockedHandler = mock()
  private val secretService: SecretService = mock()
  private val featureFlagsFacade = mock<FeatureFlagsFacade> {
    on { boolValue(PlaytimeFeatureFlags.ENABLE_ADJUST_CB) } doReturn false
    on { boolValue(PlaytimeFeatureFlags.ENABLE_ADJUST_LATEST_SERVER_ENDPOINT) } doReturn false
  }

  private val client: AdjustApiClient

  companion object {
    const val adjustAuthToken = "adjust-auth-token"
    const val adjustAppToken = "adjust-s2s-app-token"
    const val adjustIosAppToken = "adjust-s2s-ios-app-token"
    const val adjustEventSpecificRevenueToken = "g6nmhk"
    const val adjustEventIosEarned2days01dollars = "yocxe9"
    val now = Instant.now()!!
    val adjustCustomEventSample = AdjustCustomEvent(
      eventType = SPECIFIC_AMOUNT_REVENUE,
      googleAdId = "googleAdId",
      idfa = null,
      adjustId = "adjustId",
      trackingId = null,
      trackingType = null,
      appPlatform = null,
      ipAddress = null,
      createdAt = null,
      userAgent = null
    )
    val adjustCustomEventIosSample = AdjustCustomEvent(
      eventType = EARNED_WITHIN_2_DAYS_0_1_DOLLARS,
      googleAdId = null,
      idfa = "idfa",
      adjustId = "adjustId",
      trackingId = "ca6a326e-3afd-43d3-8d6c-518bcab66625",
      trackingType = IDFV.name,
      appPlatform = IOS.name,
      ipAddress = null,
      createdAt = null,
      userAgent = null
    )
  }

  init {
    httpClient = mockedHttpClient(handler)
    client = AdjustApiClient(httpClient, secretService, BuildVariant.PRODUCTION, defaultJsonConverter, featureFlagsFacade)

    secretService.mock({ secretValue(PlaytimeSecrets.ADJUST_S2S_AUTH) }, adjustAuthToken)
    secretService.mock({ secretValue(BaseSecrets.ADJUST_APP_TOKEN) }, adjustAppToken)
    secretService.mock({ secretValue(BaseSecrets.ADJUST_IOS_APP_TOKEN) }, adjustIosAppToken)
  }

  @BeforeEach
  fun setUp() {
    AdjustApiClient.circuitBreaker.reset()
  }

  @Test
  fun `SHOULD receive 200 ok ON sendCustomEvent AND adid IS NOT NULL`() {
    handler.mockUrl(
      "https://app.adjust.io/event?" +
        "s2s=1&app_token=$adjustAppToken&event_token=$adjustEventSpecificRevenueToken" +
        "&gps_adid=${adjustCustomEventSample.googleAdId}" +
        "&adid=${adjustCustomEventSample.adjustId}",
    ) { request, scope ->
      assertThat(request.headers[HttpHeaders.AUTHORIZATION]).isEqualTo("Bearer $adjustAuthToken")
      scope.respondOk()
    }

    val actual = runBlocking {
      client.sendCustomEvent(adjustCustomEventSample)
    }

    assertThat(actual!!.status).isEqualTo(HttpStatusCode.OK)
  }

  @Test
  fun `SHOULD receive 200 ok ON sendCustomEvent AND adid IS NULL`() {
    handler.mockUrl(
      "https://app.adjust.io/event?" +
        "s2s=1&app_token=$adjustAppToken&event_token=$adjustEventSpecificRevenueToken" +
        "&gps_adid=${adjustCustomEventSample.googleAdId}",
    ) { request, scope ->
      assertThat(request.headers[HttpHeaders.AUTHORIZATION]).isEqualTo("Bearer $adjustAuthToken")
      scope.respondOk()
    }

    val actual = runBlocking {
      client.sendCustomEvent(
        AdjustCustomEvent(
          eventType = adjustCustomEventSample.eventType,
          googleAdId = adjustCustomEventSample.googleAdId,
          idfa = null,
          adjustId = null,
          trackingId = null,
          trackingType = adjustCustomEventSample.trackingType,
          appPlatform = adjustCustomEventSample.appPlatform,
          ipAddress = adjustCustomEventSample.ipAddress,
          createdAt = adjustCustomEventSample.createdAt,
          userAgent = adjustCustomEventSample.userAgent
        )
      )
    }

    assertThat(actual!!.status).isEqualTo(HttpStatusCode.OK)
  }

  @Test
  fun `SHOULD receive 200 ok ON sendCustomEvent WHEN iOS user AND adid IS NOT NULL`() {
    handler.mockUrl(
      "https://app.adjust.io/event?" +
        "s2s=1" +
        "&app_token=$adjustIosAppToken" +
        "&event_token=$adjustEventIosEarned2days01dollars" +
        "&idfv=${adjustCustomEventIosSample.trackingId}" +
        "&idfa=${adjustCustomEventIosSample.idfa}" +
        "&adid=${adjustCustomEventIosSample.adjustId}",
    ) { request, scope ->
      assertThat(request.headers[HttpHeaders.AUTHORIZATION]).isEqualTo("Bearer $adjustAuthToken")
      scope.respondOk()
    }

    val actual = runBlocking {
      client.sendCustomEvent(adjustCustomEventIosSample)
    }

    assertThat(actual!!.status).isEqualTo(HttpStatusCode.OK)
  }

  @ParameterizedTest
  @ValueSource(strings = ["IOS", "IOS_WEB"])
  fun `SHOULD receive 200 ok ON sendCustomEvent WHEN iOS user AND adid IS NULL`(platform: String) =
    with(adjustCustomEventIosSample.copy(appPlatform = platform)) {
    handler.mockUrl(
      "https://app.adjust.io/event?" +
        "s2s=1&app_token=$adjustIosAppToken&event_token=$adjustEventIosEarned2days01dollars" +
        "&idfv=$trackingId&idfa=$idfa",
    ) { request, scope ->
      assertThat(request.headers[HttpHeaders.AUTHORIZATION]).isEqualTo("Bearer $adjustAuthToken")
      scope.respondOk()
    }

    val actual = runBlocking {
      client.sendCustomEvent(
        AdjustCustomEvent(
          eventType = eventType,
          googleAdId = null,
          idfa = idfa,
          adjustId = null,
          trackingId = trackingId,
          trackingType = trackingType,
          appPlatform = appPlatform,
          ipAddress = ipAddress,
          createdAt = createdAt,
          userAgent = userAgent
        )
      )
    }

    assertThat(actual!!.status).isEqualTo(HttpStatusCode.OK)
  }

  @Test
  fun `SHOULD receive 200 ok ON sendCustomEvent WHEN extraParams passed`() {
    handler.mockUrl(
      "https://app.adjust.io/event?" +
        "s2s=1&app_token=$adjustAppToken&event_token=$adjustEventSpecificRevenueToken" +
        "&gps_adid=${adjustCustomEventSample.googleAdId}" +
        "&adid=${adjustCustomEventSample.adjustId}" +
        "&p1=v1&p2=1%2C2",
    ) { request, scope ->
      assertThat(request.headers[HttpHeaders.AUTHORIZATION]).isEqualTo("Bearer $adjustAuthToken")
      scope.respondOk()
    }

    val testClient = AdjustApiClient(httpClient, secretService, BuildVariant.TEST, defaultJsonConverter, featureFlagsFacade)

    val actual = runBlocking {
      testClient.sendCustomEvent(adjustCustomEventSample, mapOf("p1" to "v1", "p2" to "1,2"))
    }

    assertThat(actual!!.status).isEqualTo(HttpStatusCode.OK)
  }

  @Test
  fun `SHOULD receive 200 ok ON sendCustomEvent WHEN fields are null except gaid`() {
    handler.mockUrl(
      "https://app.adjust.io/event?" +
        "s2s=1&app_token=$adjustAppToken&event_token=$adjustEventSpecificRevenueToken" +
        "&gps_adid=${adjustCustomEventSample.googleAdId}",
    ) { request, scope ->
      assertThat(request.headers[HttpHeaders.AUTHORIZATION]).isEqualTo("Bearer $adjustAuthToken")
      scope.respondOk()
    }

    val actual = runBlocking {
      client.sendCustomEvent(
        AdjustCustomEvent(
          eventType = SPECIFIC_AMOUNT_REVENUE,
          googleAdId = adjustCustomEventSample.googleAdId,
          idfa = null,
          adjustId = null,
          trackingId = null,
          trackingType = null,
          appPlatform = null,
          ipAddress = null,
          createdAt = null,
          userAgent = null
        )
      )
    }

    assertThat(actual!!.status).isEqualTo(HttpStatusCode.OK)
  }

  @Test
  fun `SHOULD receive 200 ok ON sendCustomEvent WHEN fields are null except adjustId`() {
    handler.mockUrl(
      "https://app.adjust.io/event?" +
        "s2s=1&app_token=$adjustAppToken&event_token=$adjustEventSpecificRevenueToken" +
        "&adid=${adjustCustomEventSample.adjustId}",
    ) { request, scope ->
      assertThat(request.headers[HttpHeaders.AUTHORIZATION]).isEqualTo("Bearer $adjustAuthToken")
      scope.respondOk()
    }

    val actual = runBlocking {
      client.sendCustomEvent(
        AdjustCustomEvent(
          eventType = SPECIFIC_AMOUNT_REVENUE,
          googleAdId = null,
          idfa = null,
          adjustId = adjustCustomEventSample.adjustId,
          trackingId = null,
          trackingType = null,
          appPlatform = null,
          ipAddress = null,
          createdAt = null,
          userAgent = null
        )
      )
    }

    assertThat(actual!!.status).isEqualTo(HttpStatusCode.OK)
  }

  @Test
  fun `SHOULD receive 200 ok ON sendCustomEvent WHEN ip_address, created_at and user_agent is not null`() {
    val ipAddress = "127.0.0.1"
    val userAgent = URLEncoder.encode("Dalvik/2.1.0", StandardCharsets.UTF_8.name())
    handler.mockUrl(
      "https://app.adjust.io/event?" +
        "s2s=1" +
        "&app_token=$adjustAppToken" +
        "&event_token=$adjustEventSpecificRevenueToken" +
        "&adid=${adjustCustomEventSample.adjustId}" +
        "&ip_address=$ipAddress" +
        "&created_at_unix=0" +
        "&user_agent=$userAgent",
    ) { request, scope ->
      assertThat(request.headers[HttpHeaders.AUTHORIZATION]).isEqualTo("Bearer $adjustAuthToken")
      scope.respondOk()
    }

    val actual = runBlocking {
      client.sendCustomEvent(
        AdjustCustomEvent(
          eventType = SPECIFIC_AMOUNT_REVENUE,
          googleAdId = null,
          idfa = null,
          adjustId = adjustCustomEventSample.adjustId,
          trackingId = null,
          trackingType = null,
          appPlatform = null,
          ipAddress = ipAddress,
          createdAt = Instant.EPOCH,
          userAgent = "Dalvik/2.1.0"
        )
      )
    }

    assertThat(actual!!.status).isEqualTo(HttpStatusCode.OK)
  }

  @Test
  fun `SHOULD receive 200 ok ON sendCustomEvent WHEN ip_address, created_at and user_agent is not null and ios`() {
    val ipAddress = "127.0.0.1"
    val userAgent = URLEncoder.encode("Dalvik/2.1.0", StandardCharsets.UTF_8.name())
    handler.mockUrl(
      "https://app.adjust.io/event?" +
        "s2s=1" +
        "&app_token=$adjustIosAppToken" +
        "&event_token=$adjustEventIosEarned2days01dollars" +
        "&idfv=${adjustCustomEventIosSample.trackingId}" +
        "&adid=${adjustCustomEventIosSample.adjustId}" +
        "&ip_address=$ipAddress" +
        "&created_at_unix=0" +
        "&user_agent=$userAgent",
    ) { request, scope ->
      assertThat(request.headers[HttpHeaders.AUTHORIZATION]).isEqualTo("Bearer $adjustAuthToken")
      scope.respondOk()
    }

    val actual = runBlocking {
      client.sendCustomEvent(
        AdjustCustomEvent(
          eventType = EARNED_WITHIN_2_DAYS_0_1_DOLLARS,
          googleAdId = null,
          idfa = null,
          adjustId = "adjustId",
          trackingId = "ca6a326e-3afd-43d3-8d6c-518bcab66625",
          trackingType = IDFV.name,
          appPlatform = IOS.name,
          ipAddress = ipAddress,
          createdAt = Instant.EPOCH,
          userAgent = "Dalvik/2.1.0"
        )
      )
    }

    assertThat(actual!!.status).isEqualTo(HttpStatusCode.OK)
  }

  @Test
  fun `SHOULD throw ON sendCustomEvent WHEN ClientRequestException AND return code is not 404`() {
    handler.mockUrl(
      "https://app.adjust.io/event?" +
        "s2s=1&app_token=$adjustAppToken&event_token=$adjustEventSpecificRevenueToken" +
        "&gps_adid=${adjustCustomEventSample.googleAdId}" +
        "&adid=${adjustCustomEventSample.adjustId}",
    ) { _, scope ->
      scope.respondBadRequest()
    }

    assertThrows<ClientRequestException> {
      runBlocking { client.sendCustomEvent(adjustCustomEventSample) }
    }
  }

  @Test
  fun `SHOULD throw ON sendCustomEvent WHEN ClientRequestException AND unexpected response body`() {
    handler.mockUrl(
      "https://app.adjust.io/event?" +
        "s2s=1&app_token=$adjustAppToken&event_token=$adjustEventSpecificRevenueToken" +
        "&gps_adid=${adjustCustomEventSample.googleAdId}" +
        "&adid=${adjustCustomEventSample.adjustId}",
    ) { _, scope ->
      scope.respond(
        content = """{ "any": "thing" }""",
        status = HttpStatusCode.NotFound,
        headers = headersOf("Content-Type", ContentType.Application.Json.toString())
      )
    }

    assertThrows<ClientRequestException> {
      runBlocking { client.sendCustomEvent(adjustCustomEventSample) }
    }.let { exception ->
      assertThat(exception.response.status).isEqualTo(HttpStatusCode.NotFound)
      assertThat(exception.message).isEqualTo("""Client request(https://app.adjust.io/event?s2s=1&app_token=adjust-s2s-app-token&event_token=$adjustEventSpecificRevenueToken&gps_adid=googleAdId&adid=adjustId) invalid: 404 Not Found. Text: "{ "any": "thing" }"""")
    }
  }

  @Test
  fun `SHOULD throw ON sendCustomEvent WHEN ClientRequestException AND unexpected error`() {
    handler.mockUrl(
      "https://app.adjust.io/event?" +
        "s2s=1&app_token=$adjustAppToken&event_token=$adjustEventSpecificRevenueToken" +
        "&gps_adid=${adjustCustomEventSample.googleAdId}" +
        "&adid=${adjustCustomEventSample.adjustId}",
    ) { _, scope ->
      scope.respond(
        content = """{ "error": "other" }""",
        status = HttpStatusCode.NotFound,
        headers = headersOf("Content-Type", ContentType.Application.Json.toString())
      )
    }

    assertThrows<ClientRequestException> {
      runBlocking { client.sendCustomEvent(adjustCustomEventSample) }
    }.let { exception ->
      assertThat(exception.response.status).isEqualTo(HttpStatusCode.NotFound)
      assertThat(exception.message).isEqualTo("""Client request(https://app.adjust.io/event?s2s=1&app_token=adjust-s2s-app-token&event_token=$adjustEventSpecificRevenueToken&gps_adid=googleAdId&adid=adjustId) invalid: 404 Not Found. Text: "{ "error": "other" }"""")
    }
  }

  @Test
  fun `SHOULD process ON sendCustomEvent WHEN device not found error`() {
    handler.mockUrl(
      "https://app.adjust.io/event?" +
        "s2s=1&app_token=$adjustAppToken&event_token=$adjustEventSpecificRevenueToken" +
        "&gps_adid=${adjustCustomEventSample.googleAdId}" +
        "&adid=${adjustCustomEventSample.adjustId}",
    ) { _, scope ->
      scope.respond(
        content = """{ "error": "Event request failed (Device not found)" }""",
        status = HttpStatusCode.NotFound,
        headers = headersOf("Content-Type", ContentType.Application.Json.toString())
      )
    }

    runBlocking { client.sendCustomEvent(adjustCustomEventSample) }.let { actual ->
      assertThat(actual).isNull()
    }
  }

  @Test
  fun `SHOULD process ON sendCustomEvent WHEN device is opted out error`() {
    handler.mockUrl(
      "https://app.adjust.io/event?" +
        "s2s=1&app_token=$adjustAppToken&event_token=$adjustEventSpecificRevenueToken" +
        "&gps_adid=${adjustCustomEventSample.googleAdId}" +
        "&adid=${adjustCustomEventSample.adjustId}",
    ) { _, scope ->
      scope.respond(
        content = """{ "error": "Event request failed (Device is opted out)" }""",
        status = HttpStatusCode(451, "Unavailable For Legal Reasons"),
        headers = headersOf("Content-Type", ContentType.Application.Json.toString())
      )
    }

    runBlocking { client.sendCustomEvent(adjustCustomEventSample) }.let { actual ->
      assertThat(actual).isNull()
    }
  }

  @Test
  fun `SHOULD process ON sendCustomEvent WHEN earlier unique event tracked`() {
    handler.mockUrl(
      "https://app.adjust.io/event?" +
        "s2s=1&app_token=$adjustAppToken&event_token=$adjustEventSpecificRevenueToken" +
        "&gps_adid=${adjustCustomEventSample.googleAdId}" +
        "&adid=${adjustCustomEventSample.adjustId}",
    ) { _, scope ->
      scope.respond(
        content = """{ "error": "Event request failed (Ignoring event, earlier unique event tracked)" }""",
        status = HttpStatusCode(400, "Bad Request"),
        headers = headersOf("Content-Type", ContentType.Application.Json.toString())
      )
    }

    runBlocking { client.sendCustomEvent(adjustCustomEventSample) }.let { actual ->
      assertThat(actual).isNull()
    }
  }

  @Test
  fun `SHOULD URL encode partner_params WHEN present`() {
    val ipAddress = "127.0.0.1"
    val userAgent = URLEncoder.encode("Dalvik/2.1.0", StandardCharsets.UTF_8.name())
    val partnerParams = mapOf("sc_revenue" to "12", "sc_currency" to "USD")
    val partnerParamsEncoded = URLEncoder.encode(defaultJsonConverter.encodeToString(partnerParams), StandardCharsets.UTF_8.name())
    handler.mockUrl(
      "https://app.adjust.io/event?" +
        "s2s=1" +
        "&app_token=$adjustIosAppToken" +
        "&event_token=${AdMarketEvent.EventType.SC_WITH_REVENUE_PARTNER.eventTokenIos}" +
        "&idfv=${adjustCustomEventIosSample.trackingId}" +
        "&adid=${adjustCustomEventIosSample.adjustId}" +
        "&ip_address=$ipAddress" +
        "&created_at_unix=0" +
        "&user_agent=$userAgent" +
        "&partner_params=$partnerParamsEncoded",
    ) { request, scope ->
      assertThat(request.headers[HttpHeaders.AUTHORIZATION]).isEqualTo("Bearer $adjustAuthToken")
      scope.respondOk()
    }

    val actual = runBlocking {
      client.sendCustomEvent(
        AdjustCustomEvent(
          eventType = AdMarketEvent.EventType.SC_WITH_REVENUE_PARTNER,
          googleAdId = null,
          idfa = null,
          adjustId = "adjustId",
          trackingId = "ca6a326e-3afd-43d3-8d6c-518bcab66625",
          trackingType = IDFV.name,
          appPlatform = IOS.name,
          ipAddress = ipAddress,
          createdAt = Instant.EPOCH,
          userAgent = "Dalvik/2.1.0"
        ),
        partnerParams = partnerParams
      )
    }

    assertThat(actual!!.status).isEqualTo(HttpStatusCode.OK)
  }

  @Test
  fun `SHOULD use new s2s endpoint WHEN ff enabled`() {
    featureFlagsFacade.mock({ boolValue(PlaytimeFeatureFlags.ENABLE_ADJUST_LATEST_SERVER_ENDPOINT) }, true)
    val ipAddress = "127.0.0.1"
    val userAgent = URLEncoder.encode("Dalvik/2.1.0", StandardCharsets.UTF_8.name())
    val partnerParams = mapOf("sc_revenue" to "12", "sc_currency" to "USD")
    val partnerParamsEncoded = URLEncoder.encode(defaultJsonConverter.encodeToString(partnerParams), StandardCharsets.UTF_8.name())
    handler.mockUrl(
      "https://s2s.adjust.com/event?" +
        "s2s=1" +
        "&app_token=$adjustIosAppToken" +
        "&event_token=${AdMarketEvent.EventType.SC_WITH_REVENUE_PARTNER.eventTokenIos}" +
        "&idfv=${adjustCustomEventIosSample.trackingId}" +
        "&adid=${adjustCustomEventIosSample.adjustId}" +
        "&ip_address=$ipAddress" +
        "&created_at_unix=0" +
        "&user_agent=$userAgent" +
        "&partner_params=$partnerParamsEncoded",
    ) { request, scope ->
      assertThat(request.headers[HttpHeaders.AUTHORIZATION]).isEqualTo("Bearer $adjustAuthToken")
      scope.respondOk()
    }

    val actual = runBlocking {
      client.sendCustomEvent(
        AdjustCustomEvent(
          eventType = AdMarketEvent.EventType.SC_WITH_REVENUE_PARTNER,
          googleAdId = null,
          idfa = null,
          adjustId = "adjustId",
          trackingId = "ca6a326e-3afd-43d3-8d6c-518bcab66625",
          trackingType = IDFV.name,
          appPlatform = IOS.name,
          ipAddress = ipAddress,
          createdAt = Instant.EPOCH,
          userAgent = "Dalvik/2.1.0"
        ),
        partnerParams = partnerParams
      )
    }

    assertThat(actual!!.status).isEqualTo(HttpStatusCode.OK)
  }

  @Nested
  inner class CircuitBreaker {
    @BeforeEach
    fun setUp() {
      featureFlagsFacade.mock({ boolValue(PlaytimeFeatureFlags.ENABLE_ADJUST_CB) }, true)
    }

    @ParameterizedTest
    @CsvSource(
      "529, Too many requests",
      "503, Service unavailable",
    )
    fun `SHOULD open circuit breaker WHEN 5 of 10 calls fail`(
      statusCode: Int,
      statusDescription: String
    ) {
      // 5 failures
      repeat(5) {
        failingCall(statusCode, statusDescription)
      }

      // 5 success calls
      repeat(5) {
        successCall()
      }

      // this call should fail
      handler.mockUrl(
        "https://app.adjust.io/event?" +
          "s2s=1&app_token=$adjustAppToken&event_token=$adjustEventSpecificRevenueToken" +
          "&gps_adid=${adjustCustomEventSample.googleAdId}" +
          "&adid=${adjustCustomEventSample.adjustId}",
      ) { request, scope ->
        assertThat(request.headers[HttpHeaders.AUTHORIZATION]).isEqualTo("Bearer $adjustAuthToken")
        scope.respondOk()
      }

      assertFailsWith<CallNotPermittedException> {
        runBlocking {
          client.sendCustomEvent(adjustCustomEventSample)
        }
      }
      assertThat(AdjustApiClient.circuitBreaker.state).isEqualTo(State.OPEN)
    }

    @Test
    fun `SHOULD recover circuit breaker WHEN started with half open state and 5 calls succeed`() {
      // forcing to half_open state
      AdjustApiClient.circuitBreaker.transitionToOpenState()
      AdjustApiClient.circuitBreaker.transitionToHalfOpenState()

      // 5 success calls, after them circuit breaker should be closed
      repeat(5) {
        successCall()
      }

      // this call should succeed
      handler.mockUrl(
        "https://app.adjust.io/event?" +
          "s2s=1&app_token=$adjustAppToken&event_token=$adjustEventSpecificRevenueToken" +
          "&gps_adid=${adjustCustomEventSample.googleAdId}" +
          "&adid=${adjustCustomEventSample.adjustId}",
      ) { request, scope ->
        assertThat(request.headers[HttpHeaders.AUTHORIZATION]).isEqualTo("Bearer $adjustAuthToken")
        scope.respondOk()
      }


      runBlocking {
        client.sendCustomEvent(adjustCustomEventSample)
      }

      assertThat(AdjustApiClient.circuitBreaker.state).isEqualTo(State.CLOSED)
    }

    @Test
    fun `SHOULD leave circuit breaker in open state WHEN started with half open state and one of calls failed`() {
      // forcing to half_open state
      AdjustApiClient.circuitBreaker.transitionToOpenState()
      AdjustApiClient.circuitBreaker.transitionToHalfOpenState()

      // 3 failing call should leave circuit breaker in open state
      repeat(3) {
        failingCall()
      }

      // even though there are 2 success calls
      repeat(2) {
        successCall()
      }

      // this call should fail
      handler.mockUrl(
        "https://app.adjust.io/event?" +
          "s2s=1&app_token=$adjustAppToken&event_token=$adjustEventSpecificRevenueToken" +
          "&gps_adid=${adjustCustomEventSample.googleAdId}" +
          "&adid=${adjustCustomEventSample.adjustId}",
      ) { request, scope ->
        assertThat(request.headers[HttpHeaders.AUTHORIZATION]).isEqualTo("Bearer $adjustAuthToken")
        scope.respondOk()
      }

      assertFailsWith<CallNotPermittedException> {
        runBlocking {
          client.sendCustomEvent(adjustCustomEventSample)
        }
      }

      assertThat(AdjustApiClient.circuitBreaker.state).isEqualTo(State.OPEN)
    }

    private fun successCall() {
      handler.mockUrl(
        "https://app.adjust.io/event?" +
          "s2s=1&app_token=$adjustAppToken&event_token=$adjustEventSpecificRevenueToken" +
          "&gps_adid=${adjustCustomEventSample.googleAdId}" +
          "&adid=${adjustCustomEventSample.adjustId}",
      ) { request, scope ->
        assertThat(request.headers[HttpHeaders.AUTHORIZATION]).isEqualTo("Bearer $adjustAuthToken")
        scope.respondOk()
      }

      val actual = runBlocking {
        client.sendCustomEvent(adjustCustomEventSample)
      }

      assertThat(actual!!.status).isEqualTo(HttpStatusCode.OK)
    }

    private fun failingCall(statusCode: Int = 529, statusDescription: String = "Too many concurrent requests") {
      handler.mockUrl(
        "https://app.adjust.io/event?" +
          "s2s=1&app_token=$adjustAppToken&event_token=$adjustEventSpecificRevenueToken" +
          "&gps_adid=${adjustCustomEventSample.googleAdId}" +
          "&adid=${adjustCustomEventSample.adjustId}",
      ) { _, scope ->
        scope.respond(
          content = """{ "error": "other" }""",
          status = HttpStatusCode(statusCode, statusDescription),
          headers = headersOf("Content-Type", ContentType.Application.Json.toString())
        )
      }

      assertThrows<ServerResponseException> {
        runBlocking { client.sendCustomEvent(adjustCustomEventSample) }
      }
    }
  }
}
