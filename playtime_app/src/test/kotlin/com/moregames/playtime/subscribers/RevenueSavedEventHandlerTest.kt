package com.moregames.playtime.subscribers

import com.google.protobuf.Int32Value
import com.google.protobuf.StringValue
import com.justplayapps.base.Common
import com.justplayapps.playtime.proto.em3CoinsRevenueCheckMessage
import com.justplayapps.service.rewarding.earnings.proto.RewardingEvents
import com.justplayapps.service.rewarding.earnings.proto.copy
import com.justplayapps.service.rewarding.earnings.proto.revenueSavedEventProto
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.bus.MessageBus
import com.moregames.base.coins.UserQualityService
import com.moregames.base.dto.AppPlatform
import com.moregames.base.dto.TrackingDataType
import com.moregames.base.featureflags.FeatureFlagsFacade
import com.moregames.base.messaging.dto.AdImpressionPubSubEventDto
import com.moregames.base.messaging.dto.RevenueReceivedEventDto.RevenueSource
import com.moregames.base.util.TEST_IO_SCOPE
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.base.util.redis.SafeJedisClient
import com.moregames.base.util.toProto
import com.moregames.playtime.app.sendAdMonetizationEvents
import com.moregames.playtime.buseffects.SendOfferwallWithRevenueEventEffect
import com.moregames.playtime.games.GamesPlayingTimeTrackingService
import com.moregames.playtime.user.UserPersistenceService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.ShowAdAfterCashoutCoinsService
import com.moregames.playtime.user.challenge.ChallengeRevenueService
import com.moregames.playtime.user.dto.UserExternalIds
import com.moregames.playtime.user.temprestrictions.TemporalRestrictionsService
import com.moregames.playtime.user.tracking.TrackingData
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.EnumSource.Mode.EXCLUDE
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.kotlin.*
import redis.clients.jedis.Jedis
import redis.clients.jedis.JedisPool
import java.math.BigDecimal
import java.time.Instant

class RevenueSavedEventHandlerTest {
  private val gamesPlayingTimeTrackingService: GamesPlayingTimeTrackingService = mock()
  private val showAdAfterCashoutCoinsService: ShowAdAfterCashoutCoinsService = mock()
  private val temporalRestrictionsService: TemporalRestrictionsService = mock()
  private val messageBus: MessageBus = mock()
  private val abTestingService: AbTestingService = mock {
    onBlocking { isEm3Participant(any()) } doReturn false
  }
  private val userService: UserService = mock {
    onBlocking { userExists(any()) } doReturn true
    onBlocking { getUser(any(), any()) } doReturn userDtoStub
  }
  private val userQualityService: UserQualityService = mock()
  private val challengeRevenueService: ChallengeRevenueService = mock()
  private val userPersistenceService: UserPersistenceService = mock()
  private val featureFlagsFacade: FeatureFlagsFacade = mock()
  private val jedis: Jedis = mock()
  private val jedisPool: JedisPool = mock()
  private val safeJedisClient: SafeJedisClient = SafeJedisClient(jedisPool) { TEST_IO_SCOPE }
  private val timeService: TimeService = mock()

  private val subscriber = RevenueSavedEventHandler(
    userService = userService,
    gamesPlayingTimeTrackingService = gamesPlayingTimeTrackingService,
    showAdAfterCashoutCoinsService = showAdAfterCashoutCoinsService,
    temporalRestrictionsService = temporalRestrictionsService,
    messageBus = messageBus,
    abTestingService = abTestingService,
    userPersistenceService = userPersistenceService,
    featureFlagsFacade = featureFlagsFacade,
    safeJedisClient = safeJedisClient,
    timeService = timeService,
    userQualityService = userQualityService,
    challengeRevenueService = challengeRevenueService,
  )

  private companion object {
    val eventProto = revenueSavedEventProto {
      userId = "userId"
      source = RewardingEvents.RevenueSourceProto.APPLOVIN
      amount = BigDecimal("0.0005123").toProto()

      eventId = StringValue.of("8dc948013d71f04264b8e5c1c61933154b226e08")
      timestamp = Instant.ofEpochSecond(1546300800).toProto()
      adSource = StringValue.of("applovin")
      applovinRevenue = BigDecimal("0.0121").toProto()
      platform = StringValue.of("android")
      adUnitId = StringValue.of("9ad0816ac071552a")
      adUnitFormat = StringValue.of("banner")
    }

    val eventDto = RevenueSavedEventDto(
      userId = "userId",
      source = RevenueSource.APPLOVIN,
      amount = BigDecimal("0.0005123"),

      eventId = "8dc948013d71f04264b8e5c1c61933154b226e08",
      timestamp = Instant.ofEpochSecond(1546300800),
      adSource = "applovin",
      applovinRevenue = BigDecimal("0.0121"),
      platform = "android",
      adUnitId = "9ad0816ac071552a",
      adUnitFormat = "banner",
    )
  }

  init {
    Dispatchers.setMain(StandardTestDispatcher())
    whenever(jedisPool.resource).thenReturn(jedis)
  }

  @Test
  fun `SHOULD do nothing ON handle WHEN user doesn't exist`() = runTest {
    userService.mock({ userExists(any()) }, false)

    subscriber.handle(eventProto)

    verifyNoInteractions(temporalRestrictionsService, messageBus, gamesPlayingTimeTrackingService, showAdAfterCashoutCoinsService)
  }

  @Test
  fun `SHOULD save revenue ON handle`() = runTest {
    subscriber.handle(eventProto)

    verifyBlocking(userQualityService) { storeEcpmRevenue(eventDto.userId, eventDto.amount, eventDto.source, eventDto.adUnitFormat) }
    verifyBlocking(challengeRevenueService) { updateChallengeEventRevenue(eventDto) }
    verifyBlocking(gamesPlayingTimeTrackingService) { trackEstimatedPlayingTime(eventDto) }
    verifyBlocking(showAdAfterCashoutCoinsService) { addBonusCoinsOnSpecificRevenue(eventDto) }
    verifyNoInteractions(temporalRestrictionsService)
    verifyNoInteractions(messageBus)
  }

  @ParameterizedTest
  @ValueSource(strings = ["banner", "inter", "reward", "null"])
  fun `SHOULD trigger checkIfSuspiciousAutomation ON handle WHEN it is inter or reward applovin revenue`(option: String) = runTest {
    val mEventProto = eventProto.toBuilder().clearAdUnitFormat().build().copy { if (option != "null") adUnitFormat = StringValue.of(option) }
    val mEvent = eventDto.copy(adUnitFormat = option.takeIf { it != "null" })
    subscriber.handle(mEventProto)

    verifyBlocking(gamesPlayingTimeTrackingService) { trackEstimatedPlayingTime(mEvent) }
    verifyBlocking(showAdAfterCashoutCoinsService) { addBonusCoinsOnSpecificRevenue(mEvent) }
    if (option == "inter" || option == "reward") {
      verifyBlocking(temporalRestrictionsService) { checkIfSuspiciousAutomation(mEvent.userId) }
    } else {
      verifyNoInteractions(temporalRestrictionsService)
    }

  }

  @Test
  fun `SHOULD publish async effect WHEN offerwall revenue message`() = runTest {
    val offerwallEvent = revenueSavedEventProto {
      userId = "userId"
      source = RewardingEvents.RevenueSourceProto.ADJOE
      amount = BigDecimal("0.0005123").toProto()
    }

    subscriber.handle(offerwallEvent)
    verifyBlocking(messageBus) { publishAsync(SendOfferwallWithRevenueEventEffect(userId = offerwallEvent.userId, amount = BigDecimal("0.0005123"))) }
    verifyNoMoreInteractions(messageBus)
  }

  @Test
  fun `SHOULD NOT publish async effect WHEN Android App revenue message`() = runTest {
    val appRevenueEvent = revenueSavedEventProto {
      userId = "userId"
      source = RewardingEvents.RevenueSourceProto.APPLOVIN
      amount = BigDecimal("0.0005123").toProto()
      gameId = Int32Value.of(1000001)
      platform = StringValue.of("android")
    }

    subscriber.handle(appRevenueEvent)
    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD launch coins calculation ON handle WHEN applovin revenue AND em3 participant`() = runTest {
    abTestingService.mock({ isEm3Participant("userId") }, true)


    subscriber.handle(eventProto)

    verifyBlocking(messageBus) {
      publish(
        em3CoinsRevenueCheckMessage {
          userId = "userId"
          platform = Common.AppPlatformProto.ANDROID
        }
      )
    }
  }

  @ParameterizedTest
  @EnumSource(value = RevenueSource::class, names = ["APPLOVIN"], mode = EXCLUDE)
  fun `SHOULD NOT launch coins calculation ON handle WHEN NOT applovin revenue`(source: RevenueSource) = runTest {
    val event = eventProto.copy { this.source = source.toProto() }

    abTestingService.mock({ isEm3Participant("userId") }, true)

    subscriber.handle(event)

    verifyBlocking(messageBus, never()) {
      publish(
        em3CoinsRevenueCheckMessage {
          userId = "userId"
          platform = Common.AppPlatformProto.ANDROID
        }
      )
    }
  }

  @Test
  fun `SHOULD NOT launch coins calculation ON handle WHEN NOT em3 participant`() = runTest {
    abTestingService.mock({ isEm3Participant("userId") }, false)

    subscriber.handle(eventProto)

    verifyBlocking(messageBus, never()) {
      publish(
        em3CoinsRevenueCheckMessage {
          userId = "userId"
          platform = Common.AppPlatformProto.ANDROID
        }
      )
    }
  }

  @Test
  fun `SHOULD not send event to GA ON handle if ff false`() = runTest {
    userService.mock({ userExists(any()) }, false)

    subscriber.handle(eventProto)

    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD publish event ON handle if ff true`() = runTest {
    userService.mock({ userExists(any()) }, false)
    featureFlagsFacade.mock({ sendAdMonetizationEvents() }, true)
    val userTrackingData = TrackingData(id = "trackingId", type = TrackingDataType.IDFA, platform = AppPlatform.ANDROID)
    userPersistenceService.mock({ fetchExternalIds("userId") }, UserExternalIds("userId", "googleAdId", "idfa", "adjustId", "firebaseAppId", userTrackingData))

    subscriber.handle(eventProto)

    verifyBlocking(messageBus) {
      publish(
        AdImpressionPubSubEventDto(
          firebaseAppId = "firebaseAppId",
          userId = "userId",
          timestamp = eventDto.timestamp!!,
          adPlatform = "appLovin",
          adUnitId = eventDto.adUnitId!!,
          adFormat = eventDto.adUnitFormat!!,
          adSource = eventDto.adSource!!,
          platform = eventDto.platform!!,
          value = eventDto.applovinRevenue!!,
        )
      )
    }

    verifyNoMoreInteractions(messageBus)
  }

  @Test
  fun `SHOULD read firebaseAppId from cache AND publish event ON handle if ff true`() = runTest {
    userService.mock({ userExists(any()) }, false)
    featureFlagsFacade.mock({ sendAdMonetizationEvents() }, true)
    val userTrackingData = TrackingData(id = "trackingId", type = TrackingDataType.IDFA, platform = AppPlatform.ANDROID)
    userPersistenceService.mock({ fetchExternalIds("userId") }, UserExternalIds("userId", "googleAdId", "idfa", "adjustId", "firebaseAppId", userTrackingData))
    jedis.mock({ get("FirebaseAppId:userId") }, "abc-123")

    subscriber.handle(eventProto)

    verifyBlocking(messageBus) {
      publish(
        AdImpressionPubSubEventDto(
          firebaseAppId = "abc-123",
          userId = "userId",
          timestamp = eventDto.timestamp!!,
          adPlatform = "appLovin",
          adUnitId = eventDto.adUnitId!!,
          adFormat = eventDto.adUnitFormat!!,
          adSource = eventDto.adSource!!,
          platform = eventDto.platform!!,
          value = eventDto.applovinRevenue!!,
        )
      )
    }

    verifyNoMoreInteractions(messageBus)
  }

  @Test
  fun `SHOULD not publish event ON handle if ff true and source != APPLOVIN`() = runTest {
    userService.mock({ userExists(any()) }, false)
    featureFlagsFacade.mock({ sendAdMonetizationEvents() }, true)
    val userTrackingData = TrackingData(id = "trackingId", type = TrackingDataType.IDFA, platform = AppPlatform.ANDROID)
    userPersistenceService.mock({ fetchExternalIds("userId") }, UserExternalIds("userId", "googleAdId", "idfa", "adjustId", "firebaseAppId", userTrackingData))

    subscriber.handle(eventProto.copy { source = RewardingEvents.RevenueSourceProto.TAPJOY })

    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD not publish event ON handle if ff true and revenue is empty`() = runTest {
    userService.mock({ userExists(any()) }, false)
    featureFlagsFacade.mock({ sendAdMonetizationEvents() }, true)
    val userTrackingData = TrackingData(id = "trackingId", type = TrackingDataType.IDFA, platform = AppPlatform.ANDROID)
    userPersistenceService.mock({ fetchExternalIds("userId") }, UserExternalIds("userId", "googleAdId", "idfa", "adjustId", "firebaseAppId", userTrackingData))

    subscriber.handle(eventProto.toBuilder().clearApplovinRevenue().build())

    verifyNoInteractions(messageBus)
  }
}