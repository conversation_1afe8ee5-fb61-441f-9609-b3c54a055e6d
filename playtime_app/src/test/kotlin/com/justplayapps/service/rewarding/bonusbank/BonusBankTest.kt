package com.justplayapps.service.rewarding.bonusbank

import com.moregames.base.dto.AppPlatform
import com.moregames.base.util.mock
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.mockito.kotlin.argThat
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import kotlin.test.assertFailsWith

class BonusBankTest {

  private val bonusBankStorage: BonusBankStorage = mock()
  private val USER_ID = "userId"
  private val PLATFORM = AppPlatform.ANDROID

  @Test
  fun `SHOULD add new coins to bank ON cutCoins FOR BonusCashBarBank`() {
    val bank = BonusCashBarBank(
      coinGoal = 1_000_000.toBigDecimal(),
      milestones = listOf(
        MilestoneWithReward(250_000.toBigDecimal(), EM2_COINS(15_000.toBigDecimal())),
        MilestoneWithReward(500_000.toBigDecimal(), EM2_COINS(15_000.toBigDecimal())),
        MilestoneWithReward(750_000.toBigDecimal(), EM2_COINS(25_000.toBigDecimal())),
      ),
      reward = SingleReward(EM2_COINS(200_000.toBigDecimal()))
    ).also {
      it.init(USER_ID, PLATFORM, bonusBankStorage)
    }

    mockBalance(10_000, 0)

    runBlocking {
      bank.cutCoins(10_000.toBigDecimal())
    }
    verifyNewBalance(
      bankBalance = 18_000,
      lastClaimedBalance = 0,
    )
  }

  @Test
  fun `SHOULD add em2 coins to user ON claimReward FOR BonusCashBarBank`() {
    val bank = BonusCashBarBank(
      coinGoal = 1_000_000.toBigDecimal(),
      milestones = listOf(
        MilestoneWithReward(250_000.toBigDecimal(), EM2_COINS(15_000.toBigDecimal())),
        MilestoneWithReward(500_000.toBigDecimal(), EM2_COINS(15_000.toBigDecimal())),
        MilestoneWithReward(750_000.toBigDecimal(), EM2_COINS(25_000.toBigDecimal())),
      ),
      reward = SingleReward(EM2_COINS(200_000.toBigDecimal()))
    ).also {
      it.init(USER_ID, PLATFORM, bonusBankStorage)
    }

    mockBalance(2_300_000, 700_000)

    runBlocking {
      bank.claimReward()
    }

    verifyNewBalance(
      bankBalance = 300_000,
      lastClaimedBalance = 300_000,
    )
    verifyEm2CoinsClaimReward(495_000)
  }

  @Test
  fun `SHOULD add em2 coins to user ON claimReward FOR BonusCashBarBank IF only milestones reached`() {
    val bank = BonusCashBarBank(
      coinGoal = 1_000_000.toBigDecimal(),
      milestones = listOf(
        MilestoneWithReward(250_000.toBigDecimal(), EM2_COINS(15_000.toBigDecimal())),
        MilestoneWithReward(500_000.toBigDecimal(), EM2_COINS(15_000.toBigDecimal())),
        MilestoneWithReward(750_000.toBigDecimal(), EM2_COINS(25_000.toBigDecimal())),
      ),
      reward = SingleReward(EM2_COINS(200_000.toBigDecimal()))
    ).also {
      it.init(USER_ID, PLATFORM, bonusBankStorage)
    }

    mockBalance(800_000, 400_000)

    runBlocking {
      bank.claimReward()
    }

    verifyNewBalance(
      bankBalance = 800_000,
      lastClaimedBalance = 800_000,
    )
    verifyEm2CoinsClaimReward(40_000)
  }

  @Test
  fun `SHOULD add em2 coins to user valuables storage ON claimReward FOR PiggyBank`() {
    val bank = PiggyBank(
      coinGoal = 1_000_000.toBigDecimal(),
      reward = SingleReward(EM2_COINS(250_000.toBigDecimal()))
    ).also {
      it.init(USER_ID, PLATFORM, bonusBankStorage)
    }

    mockBalance(2_300_000, 0)

    runBlocking {
      bank.claimReward()
    }

    verifyNewBalance(
      bankBalance = 300_000,
      lastClaimedBalance = 300_000,
    )
    verifyEm2AddedToStorage(500_000)
  }

  @Test
  fun `SHOULD add em2 coins to user ON claimReward FOR CarouselBank`() {
    val bank = CarouselBank(
      coinGoal = 1_000_000.toBigDecimal(),
      reward = ChanceBasedReward(
        listOf(
          ChanceBased(1.toBigDecimal(), EM2_COINS(25_000.toBigDecimal())),
        )
      )
    ).also {
      it.init(USER_ID, PLATFORM, bonusBankStorage)
    }

    mockBalance(3_200_000, 0)

    runBlocking {
      bank.claimReward()
    }

    verifyNewBalance(
      bankBalance = 200_000,
      lastClaimedBalance = 200_000,
    )
    verifyEm2CoinsClaimReward(75_000)
  }

  @Test
  fun `SHOULD throw exception ON claimReward FOR CarouselBank IF balance less than coinGoal`() {
    val bank = CarouselBank(
      coinGoal = 1_000_000.toBigDecimal(),
      reward = ChanceBasedReward(
        listOf(
          ChanceBased(1.toBigDecimal(), EM2_COINS(25_000.toBigDecimal())),
        )
      )
    ).also {
      it.init(USER_ID, PLATFORM, bonusBankStorage)
    }

    mockBalance(900_000, 0)

    assertFailsWith<IllegalStateException> {
      runBlocking { bank.claimReward() }
    }
  }

  private fun mockBalance(bankBalance: Int, lastClaimedBalance: Int) {
    bonusBankStorage.mock({ getUserBank(USER_ID) }, BonusBank(USER_ID, bankBalance.toBigDecimal(), lastClaimedBalance.toBigDecimal()))
  }

  private fun verifyNewBalance(bankBalance: Int, lastClaimedBalance: Int) {
    verifyBlocking(bonusBankStorage) {
      saveUserBank(argThat { bonusBank ->
        bonusBank.userId == USER_ID &&
          bonusBank.bankBalance.compareTo(bankBalance.toBigDecimal()) == 0 &&
          bonusBank.lastClaimedBalance.compareTo(lastClaimedBalance.toBigDecimal()) == 0
      })
    }
  }

  private fun verifyEm2CoinsClaimReward(reward: Int) {
    verifyBlocking(bonusBankStorage) {
      claimReward(
        eq(USER_ID),
        eq(PLATFORM),
        argThat { rewards ->
          rewards.map {
            when (it) {
              is EM2_COINS -> {
                it.coins.compareTo(reward.toBigDecimal()) == 0
              }

              is EARNINGS -> true
            }
          }.all { it }
        }
      )
    }
  }

  private fun verifyEm2AddedToStorage(coins: Int) {
    verifyBlocking(bonusBankStorage) {
      storeUserCoins(
        eq(USER_ID),
        argThat { reward ->
          reward.compareTo(coins.toBigDecimal()) == 0
        }
      )
    }
  }
}