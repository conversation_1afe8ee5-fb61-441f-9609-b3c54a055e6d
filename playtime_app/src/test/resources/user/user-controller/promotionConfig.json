{"id": "TEST_PROMO", "startTime": 157600000, "endTime": 157770000, "timestamp": 154400000, "top": {"backgroundImage": "Christmas_back.jpg", "foregroundImage": "Christmas_fore.png", "gradientTop": "#FF0000", "gradientBottom": "FF0033", "cashoutButtonColor": "#FF0057", "durationInMillis": 3000}, "mainTop": [{"backgroundImage": "Christmas_back.jpg", "foregroundImage": "Christmas_fore.png", "gradientTop": "#FF0000", "gradientBottom": "FF0033", "cashoutButtonColor": "#FF0057", "durationInMillis": 3000, "order": 1}], "challengesTop": [{"backgroundImage": "Christmas_back.jpg", "foregroundImage": "Christmas_fore.png", "gradientTop": "#FF0000", "gradientBottom": "FF0033", "cashoutButtonColor": "#FF0057", "durationInMillis": 3000, "order": 1}], "offerModifier": [{"offerId": "2000089", "offerImage": "treasure_master_override.jpg", "badge": {"color": "#FFFF00", "displaySpecialBadge": true, "text": "<strikethrough>$5 daily</strikethrough><br><font color=\"0x00FF00\">Now!</font><br>"}}], "translations": {"earn_playing_games": "Begrenztes Angebot!", "notificationBalanceUpdateText": "Sie haben jetzt %s erhöhte Münzen", "notificationBalanceUpdateTitle": "<PERSON>rhöhte Münzen!", "balance_title": "<PERSON>r<PERSON>öhte Münzen:"}, "countDownBanners": {"startPosition": 1, "step": 3, "max": 2, "title": "Banner title", "backgroundImage": "background_image.jpg", "infoImages": ["image1.jgp", "image2.jpg"], "infoTitle": "Info title", "infoSections": [{"subTitle": "SubTitle", "subText": "SubText"}], "infoButtonClickAction": {"name": "OPEN_FIRST_FOUND_OFFERWALL", "parameters": ["parameter1", "parameter2"]}, "infoButtonText": "Info button text", "endTime": 157770000}, "infoBar": {"content": [{"text": "PLAYERS_ONLINE"}, {"text": "Text with {DYNAMIC_VALUE}$ amount in it!", "parameters": {"showDuringSec": 5, "sliding": true, "dynamicValueConfiguration": {"baseValue": 1000000, "updateEachSec": 5, "updateValueBy": -200, "randomnessPercentage": 5}}}]}, "expectedAppVersion": 75, "announcementDetails": {"title": "t", "description": "d", "image": "i", "buttonText": "Jetzt aktualisieren", "themeColor": "tc", "buttonClickAction": {"name": "ROUTE_TO_MAIN"}}}