create table `playspot-server-dev`.analytics_test.`adjust-updated-attribution`
(
    campaignName         STRING(500),
    trackerId            STRING(10),
    trackerName          STRING(500),
    adNetwork            STRING(200),
    googleAdId           STRING(36),
    userId               STRING(36),
    adjustId             STRING(32),
    ip                   STRING(100),
    countryCode          STRING(2),
    installedAt          TIMESTAMP not null,
    packageName          STRING(200),
    osVersion            STRING(100),
    device               STRING(50),
    userAgent            STRING(200),
    limitAdTracking      BOOL,
    isOrganic            BOOL,
    adgroupName          STRING(500),
    creativeName         STRING(500),
    googleStoreReferrer  STRING(200),
    appSetId             STRING(36),
    outdatedTracker      STRING(500),
    outdatedTrackerName  STRING(500),
    attributionUpdatedAt TIMESTAMP,
    activityKind         STRING(200),
    createdAt            TIMESTAMP
)
    PARTITION BY DATE (createdAt)
    OPTIONS (
        require_partition_filter = true
        );