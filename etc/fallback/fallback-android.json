{"markets": [{"market": "us", "statusMessage": {"default": "Service is temporarily unavailable", "en-US": "Service is temporarily unavailable"}, "playGamesTitle": {"default": "You still can play our awesome games. All our scientists are already working to resolve the issue", "en-US": "You still can play our awesome games. All our scientists are already working to resolve the issue"}, "items": [{"id": 200039, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Treasure Master 🤸‍♂️"}, "subtitle": {"default": "Defeat monsters for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/treasure_master.png", "applicationId": "com.gimica.treasuremaster", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Treasure Master"}, "infoTextInstallBottom": {"default": "Defeat monsters for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.treasuremaster"}, {"id": 200044, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Solitaire Verse"}, "subtitle": {"default": "The more games you win the more loyalty coins you get"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/solitaire_verse.jpg", "applicationId": "com.gimica.solitaireverse", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Solitaire Verse"}, "infoTextInstallBottom": {"default": "The more games you win the more loyalty coins you get"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.solitaireverse"}, {"id": 200052, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Sugar Rush Adventure"}, "subtitle": {"default": "Match 3 to make sweet treats for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/sugar_rush_icon.jpg", "applicationId": "com.gimica.sugarmatch", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Sugar Rush Adventure"}, "infoTextInstallBottom": {"default": "Match 3 to make sweet treats for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.sugarmatch"}, {"id": 200048, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON>"}, "subtitle": {"default": "Have a blast and merge blocks to survive as long as you can for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/merge_blast_icon.jpg", "applicationId": "com.gimica.mergeblast", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Merge Blast"}, "infoTextInstallBottom": {"default": "Have a blast and merge blocks to survive as long as you can for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mergeblast"}, {"id": 200042, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON>"}, "subtitle": {"default": "Destroy the blocks and collect pick ups for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/ball_bounce.jpg", "applicationId": "com.gimica.ballbounce", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Ball Bounce"}, "infoTextInstallBottom": {"default": "Destroy the blocks and collect pick ups for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.ballbounce"}, {"id": 200051, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON>zle <PERSON>"}, "subtitle": {"default": "Enter a state of bliss and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/wooden_puzzle_bliss_icon.jpg", "applicationId": "com.gimica.zentiles", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON><PERSON> Puzzle Bliss"}, "infoTextInstallBottom": {"default": "Enter a state of bliss and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.zentiles"}, {"id": 200041, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON>"}, "subtitle": {"default": "Smash through level after level for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/mad_smash.jpg", "applicationId": "com.gimica.madsmash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Mad Smash"}, "infoTextInstallBottom": {"default": "Smash through level after level for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.madsmash"}, {"id": 200045, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Puzzle Pop Blaster"}, "subtitle": {"default": "Match colored blocks to make loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/puzzle_pop_blaster.jpg", "applicationId": "com.gimica.puzzlepopblaster", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Puzzle Pop Blaster"}, "infoTextInstallBottom": {"default": "Match colored blocks to make loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.puzzlepopblaster"}, {"id": 200057, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Bubble Pop: Wild Rescue"}, "subtitle": {"default": "Solve puzzles and rescue the animals for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/bubble_pop_icon.jpg", "applicationId": "com.gimica.bubblepop", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Bubble Pop: Wild Rescue"}, "infoTextInstallBottom": {"default": "Solve puzzles and rescue the animals for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.bubblepop"}, {"id": 200049, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Emoji <PERSON>lickers"}, "subtitle": {"default": "Tap and bounce your way to more loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/emoji_clickers_icon.jpg", "applicationId": "com.gimica.emojiclickers", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Emoji <PERSON>"}, "infoTextInstallBottom": {"default": "Tap and bounce your way to more loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.emojiclickers"}, {"id": 200040, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Mix Blox"}, "subtitle": {"default": "Make number matches for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/mix_blox_icon.jpg", "applicationId": "com.gimica.mixblox", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Mix Blox"}, "infoTextInstallBottom": {"default": "Make number matches for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mixblox"}, {"id": 200047, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Trivia Madness"}, "subtitle": {"default": "Put your general knowledge to the test and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/trivia_madness_icon.jpg", "applicationId": "com.gimica.triviamadness", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Trivia Madness"}, "infoTextInstallBottom": {"default": "Put your general knowledge to the test and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.triviamadness"}, {"id": 200065, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Crystal Crush"}, "subtitle": {"default": "Mine precious gems and earn yourself some loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/crystal_crush_icon.jpg", "applicationId": "com.gimica.crystalcrush", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Crystal Crush"}, "infoTextInstallBottom": {"default": "Mine precious gems and earn yourself some loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.crystalcrush"}, {"id": 200053, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Cars Merge and Defend"}, "subtitle": {"default": "Merge and build your team to defeat your enemies for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/carsmerge_icon.jpg", "applicationId": "com.gimica.carsmerge", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Cars Merge and Defend"}, "infoTextInstallBottom": {"default": "Merge and build your team to defeat your enemies for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.carsmerge"}, {"id": 200054, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Block Hole Clash"}, "subtitle": {"default": "Remove the correct blocks for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/blockhole_icon.jpg", "applicationId": "com.gimica.blockholeclash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Block Hole Clash"}, "infoTextInstallBottom": {"default": "Remove the correct blocks for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.blockholeclash"}, {"id": 200055, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Idle Merge <PERSON>"}, "subtitle": {"default": "Merge your way to improved production and loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/idlemerge_icon.jpg", "applicationId": "com.gimica.idlemergefun", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Idle Merge Fun"}, "infoTextInstallBottom": {"default": "Merge your way to improved production and loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.idlemergefun"}, {"id": 200056, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Brick Slider"}, "subtitle": {"default": "Survive the wall by making complete rows and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/block_slider_icon.jpg", "applicationId": "com.gimica.blockslider", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON> Slider"}, "infoTextInstallBottom": {"default": "Survive the wall by making complete rows and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.blockslider"}, {"id": 200060, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON><PERSON>"}, "subtitle": {"default": "Earn loyalty coins and relax with a classic"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/sudoku_icon.jpg", "applicationId": "com.gimica.sudoku", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Sudoku"}, "infoTextInstallBottom": {"default": "Earn loyalty coins and relax with a classic"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.sudoku"}, {"id": 200064, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Make Ten"}, "subtitle": {"default": "Make ten for loyalty coins!"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/make_ten_icon.jpg", "applicationId": "com.gimica.maketen", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Make Ten"}, "infoTextInstallBottom": {"default": "Make ten for loyalty coins!"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.maketen"}, {"id": 200046, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Word Seeker"}, "subtitle": {"default": "Solve word puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/word_seeker_icon.jpg", "applicationId": "com.gimica.wordseeker", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Word Seeker"}, "infoTextInstallBottom": {"default": "Solve word puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.wordseeker"}, {"id": 200043, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "2048 Puzzle Fun"}, "subtitle": {"default": "Reach 2048 to get more loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/hexa_puzzle_fun.jpg", "applicationId": "com.gimica.hexapuzzlefun", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play 2048 Puzzle Fun"}, "infoTextInstallBottom": {"default": "Reach 2048 to get more loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.hexapuzzlefun"}, {"id": 200059, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Brickdoku"}, "subtitle": {"default": "Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/brickdoku_icon.jpg", "applicationId": "com.gimica.brickdoku", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Brickdoku"}, "infoTextInstallBottom": {"default": "Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.brickdoku"}, {"id": 200061, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Word Kitchen"}, "subtitle": {"default": "Take on the heat of the Word Kitchen and discover words for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/word_kitchen_icon.jpg", "applicationId": "com.gimica.wordkitchen", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Word Kitchen"}, "infoTextInstallBottom": {"default": "Take on the heat of the Word Kitchen and discover words for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.wordkitchen"}, {"id": 200063, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Color Logic"}, "subtitle": {"default": "Test your logic for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/color_logic_icon.jpg", "applicationId": "com.gimica.colorlogic", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Color Logic"}, "infoTextInstallBottom": {"default": "Test your logic for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.colorlogic"}, {"id": 200067, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Spiral Drop"}, "subtitle": {"default": "Survive the drop for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/spiral_drop_icon.jpg", "applicationId": "com.gimica.helixdash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Spiral Drop"}, "infoTextInstallBottom": {"default": "Survive the drop for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.helixdash"}, {"id": 200058, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Mayan <PERSON>"}, "subtitle": {"default": "Survive Aztec trials for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/marble_madness_icon.jpg", "applicationId": "com.gimica.marblemadness", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON>n <PERSON>"}, "infoTextInstallBottom": {"default": "Survive Aztec trials for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.marblemadness"}, {"id": 200069, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Space Connect"}, "subtitle": {"default": "Connect for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/space_connect_icon.jpg", "applicationId": "com.gimica.spaceconnect", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Space Connect"}, "infoTextInstallBottom": {"default": "Connect for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.spaceconnect"}, {"id": 200050, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Hex Match"}, "subtitle": {"default": "Solve hexagonal puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/hex_match-icon.jpg", "applicationId": "com.gimica.hexmatch", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Hex Match"}, "infoTextInstallBottom": {"default": "Solve hexagonal puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.hexmatch"}, {"id": 200062, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Water Sorter"}, "subtitle": {"default": "Sort all the colors for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/water_sorter_icon.jpg", "applicationId": "com.gimica.watersorter", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Water Sorter"}, "infoTextInstallBottom": {"default": "Sort all the colors for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.watersorter"}, {"id": 200068, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Tile Match Pro"}, "subtitle": {"default": "Clear tiles from the board for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/tile_match_pro_icon.jpg", "applicationId": "com.gimica.tilematchpro", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Tile Match Pro"}, "infoTextInstallBottom": {"default": "Clear tiles from the board for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.tilematchpro"}, {"id": 200072, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Tangram Heaven"}, "subtitle": {"default": "Complete Tangram puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/tangram_heaven_icon.jpg", "applicationId": "com.gimica.tangram", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Tangram Heaven"}, "infoTextInstallBottom": {"default": "Complete Tangram puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.tangram"}]}, {"market": "us-test", "statusMessage": {"default": "Service is temporarily unavailable", "en-US": "Service is temporarily unavailable"}, "playGamesTitle": {"default": "You still can play our awesome games. All our scientists are already working to resolve the issue", "en-US": "You still can play our awesome games. All our scientists are already working to resolve the issue"}, "items": [{"id": 200039, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Treasure Master 🤸‍♂️"}, "subtitle": {"default": "Defeat monsters for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/treasure_master.png", "applicationId": "com.gimica.treasuremaster", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Treasure Master"}, "infoTextInstallBottom": {"default": "Defeat monsters for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.treasuremaster"}, {"id": 200044, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Solitaire Verse"}, "subtitle": {"default": "The more games you win the more loyalty coins you get"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/solitaire_verse.jpg", "applicationId": "com.gimica.solitaireverse", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Solitaire Verse"}, "infoTextInstallBottom": {"default": "The more games you win the more loyalty coins you get"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.solitaireverse"}, {"id": 200052, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Sugar Rush Adventure"}, "subtitle": {"default": "Match 3 to make sweet treats for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/sugar_rush_icon.jpg", "applicationId": "com.gimica.sugarmatch", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Sugar Rush Adventure"}, "infoTextInstallBottom": {"default": "Match 3 to make sweet treats for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.sugarmatch"}, {"id": 200048, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON>"}, "subtitle": {"default": "Have a blast and merge blocks to survive as long as you can for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/merge_blast_icon.jpg", "applicationId": "com.gimica.mergeblast", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Merge Blast"}, "infoTextInstallBottom": {"default": "Have a blast and merge blocks to survive as long as you can for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mergeblast"}, {"id": 200042, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON>"}, "subtitle": {"default": "Destroy the blocks and collect pick ups for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/ball_bounce.jpg", "applicationId": "com.gimica.ballbounce", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Ball Bounce"}, "infoTextInstallBottom": {"default": "Destroy the blocks and collect pick ups for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.ballbounce"}, {"id": 200051, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON>zle <PERSON>"}, "subtitle": {"default": "Enter a state of bliss and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/wooden_puzzle_bliss_icon.jpg", "applicationId": "com.gimica.zentiles", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON><PERSON> Puzzle Bliss"}, "infoTextInstallBottom": {"default": "Enter a state of bliss and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.zentiles"}, {"id": 200041, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON>"}, "subtitle": {"default": "Smash through level after level for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/mad_smash.jpg", "applicationId": "com.gimica.madsmash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Mad Smash"}, "infoTextInstallBottom": {"default": "Smash through level after level for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.madsmash"}, {"id": 200045, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Puzzle Pop Blaster"}, "subtitle": {"default": "Match colored blocks to make loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/puzzle_pop_blaster.jpg", "applicationId": "com.gimica.puzzlepopblaster", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Puzzle Pop Blaster"}, "infoTextInstallBottom": {"default": "Match colored blocks to make loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.puzzlepopblaster"}, {"id": 200057, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Bubble Pop: Wild Rescue"}, "subtitle": {"default": "Solve puzzles and rescue the animals for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/bubble_pop_icon.jpg", "applicationId": "com.gimica.bubblepop", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Bubble Pop: Wild Rescue"}, "infoTextInstallBottom": {"default": "Solve puzzles and rescue the animals for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.bubblepop"}, {"id": 200049, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Emoji <PERSON>lickers"}, "subtitle": {"default": "Tap and bounce your way to more loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/emoji_clickers_icon.jpg", "applicationId": "com.gimica.emojiclickers", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Emoji <PERSON>"}, "infoTextInstallBottom": {"default": "Tap and bounce your way to more loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.emojiclickers"}, {"id": 200040, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Mix Blox"}, "subtitle": {"default": "Make number matches for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/mix_blox_icon.jpg", "applicationId": "com.gimica.mixblox", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Mix Blox"}, "infoTextInstallBottom": {"default": "Make number matches for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mixblox"}, {"id": 200047, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Trivia Madness"}, "subtitle": {"default": "Put your general knowledge to the test and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/trivia_madness_icon.jpg", "applicationId": "com.gimica.triviamadness", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Trivia Madness"}, "infoTextInstallBottom": {"default": "Put your general knowledge to the test and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.triviamadness"}, {"id": 200065, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Crystal Crush"}, "subtitle": {"default": "Mine precious gems and earn yourself some loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/crystal_crush_icon.jpg", "applicationId": "com.gimica.crystalcrush", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Crystal Crush"}, "infoTextInstallBottom": {"default": "Mine precious gems and earn yourself some loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.crystalcrush"}, {"id": 200053, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Cars Merge and Defend"}, "subtitle": {"default": "Merge and build your team to defeat your enemies for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/carsmerge_icon.jpg", "applicationId": "com.gimica.carsmerge", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Cars Merge and Defend"}, "infoTextInstallBottom": {"default": "Merge and build your team to defeat your enemies for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.carsmerge"}, {"id": 200054, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Block Hole Clash"}, "subtitle": {"default": "Remove the correct blocks for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/blockhole_icon.jpg", "applicationId": "com.gimica.blockholeclash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Block Hole Clash"}, "infoTextInstallBottom": {"default": "Remove the correct blocks for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.blockholeclash"}, {"id": 200055, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Idle Merge <PERSON>"}, "subtitle": {"default": "Merge your way to improved production and loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/idlemerge_icon.jpg", "applicationId": "com.gimica.idlemergefun", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Idle Merge Fun"}, "infoTextInstallBottom": {"default": "Merge your way to improved production and loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.idlemergefun"}, {"id": 200056, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Brick Slider"}, "subtitle": {"default": "Survive the wall by making complete rows and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/block_slider_icon.jpg", "applicationId": "com.gimica.blockslider", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON> Slider"}, "infoTextInstallBottom": {"default": "Survive the wall by making complete rows and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.blockslider"}, {"id": 200060, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON><PERSON>"}, "subtitle": {"default": "Earn loyalty coins and relax with a classic"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/sudoku_icon.jpg", "applicationId": "com.gimica.sudoku", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Sudoku"}, "infoTextInstallBottom": {"default": "Earn loyalty coins and relax with a classic"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.sudoku"}, {"id": 200064, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Make Ten"}, "subtitle": {"default": "Make ten for loyalty coins!"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/make_ten_icon.jpg", "applicationId": "com.gimica.maketen", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Make Ten"}, "infoTextInstallBottom": {"default": "Make ten for loyalty coins!"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.maketen"}, {"id": 200046, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Word Seeker"}, "subtitle": {"default": "Solve word puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/word_seeker_icon.jpg", "applicationId": "com.gimica.wordseeker", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Word Seeker"}, "infoTextInstallBottom": {"default": "Solve word puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.wordseeker"}, {"id": 200043, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "2048 Puzzle Fun"}, "subtitle": {"default": "Reach 2048 to get more loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/hexa_puzzle_fun.jpg", "applicationId": "com.gimica.hexapuzzlefun", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play 2048 Puzzle Fun"}, "infoTextInstallBottom": {"default": "Reach 2048 to get more loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.hexapuzzlefun"}, {"id": 200059, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Brickdoku"}, "subtitle": {"default": "Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/brickdoku_icon.jpg", "applicationId": "com.gimica.brickdoku", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Brickdoku"}, "infoTextInstallBottom": {"default": "Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.brickdoku"}, {"id": 200061, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Word Kitchen"}, "subtitle": {"default": "Take on the heat of the Word Kitchen and discover words for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/word_kitchen_icon.jpg", "applicationId": "com.gimica.wordkitchen", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Word Kitchen"}, "infoTextInstallBottom": {"default": "Take on the heat of the Word Kitchen and discover words for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.wordkitchen"}, {"id": 200063, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Color Logic"}, "subtitle": {"default": "Test your logic for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/color_logic_icon.jpg", "applicationId": "com.gimica.colorlogic", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Color Logic"}, "infoTextInstallBottom": {"default": "Test your logic for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.colorlogic"}, {"id": 200067, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Spiral Drop"}, "subtitle": {"default": "Survive the drop for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/spiral_drop_icon.jpg", "applicationId": "com.gimica.helixdash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Spiral Drop"}, "infoTextInstallBottom": {"default": "Survive the drop for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.helixdash"}, {"id": 200058, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Mayan <PERSON>"}, "subtitle": {"default": "Survive Aztec trials for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/marble_madness_icon.jpg", "applicationId": "com.gimica.marblemadness", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON>n <PERSON>"}, "infoTextInstallBottom": {"default": "Survive Aztec trials for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.marblemadness"}, {"id": 200069, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Space Connect"}, "subtitle": {"default": "Connect for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/space_connect_icon.jpg", "applicationId": "com.gimica.spaceconnect", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Space Connect"}, "infoTextInstallBottom": {"default": "Connect for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.spaceconnect"}, {"id": 200050, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Hex Match"}, "subtitle": {"default": "Solve hexagonal puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/hex_match-icon.jpg", "applicationId": "com.gimica.hexmatch", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Hex Match"}, "infoTextInstallBottom": {"default": "Solve hexagonal puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.hexmatch"}, {"id": 200062, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Water Sorter"}, "subtitle": {"default": "Sort all the colors for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/water_sorter_icon.jpg", "applicationId": "com.gimica.watersorter", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Water Sorter"}, "infoTextInstallBottom": {"default": "Sort all the colors for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.watersorter"}, {"id": 200068, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Tile Match Pro"}, "subtitle": {"default": "Clear tiles from the board for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/tile_match_pro_icon.jpg", "applicationId": "com.gimica.tilematchpro", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Tile Match Pro"}, "infoTextInstallBottom": {"default": "Clear tiles from the board for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.tilematchpro"}, {"id": 200072, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Tangram Heaven"}, "subtitle": {"default": "Complete Tangram puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/tangram_heaven_icon.jpg", "applicationId": "com.gimica.tangram", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Tangram Heaven"}, "infoTextInstallBottom": {"default": "Complete Tangram puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.tangram"}]}, {"market": "us-staging", "statusMessage": {"default": "Service is temporarily unavailable", "en-US": "Service is temporarily unavailable"}, "playGamesTitle": {"default": "You still can play our awesome games. All our scientists are already working to resolve the issue", "en-US": "You still can play our awesome games. All our scientists are already working to resolve the issue"}, "items": [{"id": 200039, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Treasure Master 🤸‍♂️"}, "subtitle": {"default": "Defeat monsters for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/treasure_master.png", "applicationId": "com.gimica.treasuremaster", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Treasure Master"}, "infoTextInstallBottom": {"default": "Defeat monsters for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.treasuremaster"}, {"id": 200044, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Solitaire Verse"}, "subtitle": {"default": "The more games you win the more loyalty coins you get"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/solitaire_verse.jpg", "applicationId": "com.gimica.solitaireverse", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Solitaire Verse"}, "infoTextInstallBottom": {"default": "The more games you win the more loyalty coins you get"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.solitaireverse"}, {"id": 200052, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Sugar Rush Adventure"}, "subtitle": {"default": "Match 3 to make sweet treats for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/sugar_rush_icon.jpg", "applicationId": "com.gimica.sugarmatch", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Sugar Rush Adventure"}, "infoTextInstallBottom": {"default": "Match 3 to make sweet treats for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.sugarmatch"}, {"id": 200048, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON>"}, "subtitle": {"default": "Have a blast and merge blocks to survive as long as you can for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/merge_blast_icon.jpg", "applicationId": "com.gimica.mergeblast", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Merge Blast"}, "infoTextInstallBottom": {"default": "Have a blast and merge blocks to survive as long as you can for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mergeblast"}, {"id": 200042, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON>"}, "subtitle": {"default": "Destroy the blocks and collect pick ups for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/ball_bounce.jpg", "applicationId": "com.gimica.ballbounce", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Ball Bounce"}, "infoTextInstallBottom": {"default": "Destroy the blocks and collect pick ups for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.ballbounce"}, {"id": 200051, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON>zle <PERSON>"}, "subtitle": {"default": "Enter a state of bliss and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/wooden_puzzle_bliss_icon.jpg", "applicationId": "com.gimica.zentiles", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON><PERSON> Puzzle Bliss"}, "infoTextInstallBottom": {"default": "Enter a state of bliss and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.zentiles"}, {"id": 200041, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON>"}, "subtitle": {"default": "Smash through level after level for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/mad_smash.jpg", "applicationId": "com.gimica.madsmash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Mad Smash"}, "infoTextInstallBottom": {"default": "Smash through level after level for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.madsmash"}, {"id": 200045, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Puzzle Pop Blaster"}, "subtitle": {"default": "Match colored blocks to make loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/puzzle_pop_blaster.jpg", "applicationId": "com.gimica.puzzlepopblaster", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Puzzle Pop Blaster"}, "infoTextInstallBottom": {"default": "Match colored blocks to make loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.puzzlepopblaster"}, {"id": 200057, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Bubble Pop: Wild Rescue"}, "subtitle": {"default": "Solve puzzles and rescue the animals for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/bubble_pop_icon.jpg", "applicationId": "com.gimica.bubblepop", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Bubble Pop: Wild Rescue"}, "infoTextInstallBottom": {"default": "Solve puzzles and rescue the animals for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.bubblepop"}, {"id": 200049, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Emoji <PERSON>lickers"}, "subtitle": {"default": "Tap and bounce your way to more loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/emoji_clickers_icon.jpg", "applicationId": "com.gimica.emojiclickers", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Emoji <PERSON>"}, "infoTextInstallBottom": {"default": "Tap and bounce your way to more loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.emojiclickers"}, {"id": 200040, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Mix Blox"}, "subtitle": {"default": "Make number matches for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/mix_blox_icon.jpg", "applicationId": "com.gimica.mixblox", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Mix Blox"}, "infoTextInstallBottom": {"default": "Make number matches for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mixblox"}, {"id": 200047, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Trivia Madness"}, "subtitle": {"default": "Put your general knowledge to the test and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/trivia_madness_icon.jpg", "applicationId": "com.gimica.triviamadness", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Trivia Madness"}, "infoTextInstallBottom": {"default": "Put your general knowledge to the test and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.triviamadness"}, {"id": 200065, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Crystal Crush"}, "subtitle": {"default": "Mine precious gems and earn yourself some loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/crystal_crush_icon.jpg", "applicationId": "com.gimica.crystalcrush", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Crystal Crush"}, "infoTextInstallBottom": {"default": "Mine precious gems and earn yourself some loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.crystalcrush"}, {"id": 200053, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Cars Merge and Defend"}, "subtitle": {"default": "Merge and build your team to defeat your enemies for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/carsmerge_icon.jpg", "applicationId": "com.gimica.carsmerge", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Cars Merge and Defend"}, "infoTextInstallBottom": {"default": "Merge and build your team to defeat your enemies for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.carsmerge"}, {"id": 200054, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Block Hole Clash"}, "subtitle": {"default": "Remove the correct blocks for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/blockhole_icon.jpg", "applicationId": "com.gimica.blockholeclash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Block Hole Clash"}, "infoTextInstallBottom": {"default": "Remove the correct blocks for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.blockholeclash"}, {"id": 200055, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Idle Merge <PERSON>"}, "subtitle": {"default": "Merge your way to improved production and loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/idlemerge_icon.jpg", "applicationId": "com.gimica.idlemergefun", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Idle Merge Fun"}, "infoTextInstallBottom": {"default": "Merge your way to improved production and loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.idlemergefun"}, {"id": 200056, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Brick Slider"}, "subtitle": {"default": "Survive the wall by making complete rows and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/block_slider_icon.jpg", "applicationId": "com.gimica.blockslider", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON> Slider"}, "infoTextInstallBottom": {"default": "Survive the wall by making complete rows and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.blockslider"}, {"id": 200060, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON><PERSON>"}, "subtitle": {"default": "Earn loyalty coins and relax with a classic"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/sudoku_icon.jpg", "applicationId": "com.gimica.sudoku", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Sudoku"}, "infoTextInstallBottom": {"default": "Earn loyalty coins and relax with a classic"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.sudoku"}, {"id": 200064, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Make Ten"}, "subtitle": {"default": "Make ten for loyalty coins!"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/make_ten_icon.jpg", "applicationId": "com.gimica.maketen", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Make Ten"}, "infoTextInstallBottom": {"default": "Make ten for loyalty coins!"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.maketen"}, {"id": 200046, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Word Seeker"}, "subtitle": {"default": "Solve word puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/word_seeker_icon.jpg", "applicationId": "com.gimica.wordseeker", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Word Seeker"}, "infoTextInstallBottom": {"default": "Solve word puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.wordseeker"}, {"id": 200043, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "2048 Puzzle Fun"}, "subtitle": {"default": "Reach 2048 to get more loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/hexa_puzzle_fun.jpg", "applicationId": "com.gimica.hexapuzzlefun", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play 2048 Puzzle Fun"}, "infoTextInstallBottom": {"default": "Reach 2048 to get more loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.hexapuzzlefun"}, {"id": 200059, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Brickdoku"}, "subtitle": {"default": "Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/brickdoku_icon.jpg", "applicationId": "com.gimica.brickdoku", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Brickdoku"}, "infoTextInstallBottom": {"default": "Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.brickdoku"}, {"id": 200061, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Word Kitchen"}, "subtitle": {"default": "Take on the heat of the Word Kitchen and discover words for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/word_kitchen_icon.jpg", "applicationId": "com.gimica.wordkitchen", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Word Kitchen"}, "infoTextInstallBottom": {"default": "Take on the heat of the Word Kitchen and discover words for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.wordkitchen"}, {"id": 200063, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Color Logic"}, "subtitle": {"default": "Test your logic for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/color_logic_icon.jpg", "applicationId": "com.gimica.colorlogic", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Color Logic"}, "infoTextInstallBottom": {"default": "Test your logic for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.colorlogic"}, {"id": 200067, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Spiral Drop"}, "subtitle": {"default": "Survive the drop for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/spiral_drop_icon.jpg", "applicationId": "com.gimica.helixdash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Spiral Drop"}, "infoTextInstallBottom": {"default": "Survive the drop for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.helixdash"}, {"id": 200058, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Mayan <PERSON>"}, "subtitle": {"default": "Survive Aztec trials for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/marble_madness_icon.jpg", "applicationId": "com.gimica.marblemadness", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON>n <PERSON>"}, "infoTextInstallBottom": {"default": "Survive Aztec trials for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.marblemadness"}, {"id": 200069, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Space Connect"}, "subtitle": {"default": "Connect for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/space_connect_icon.jpg", "applicationId": "com.gimica.spaceconnect", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Space Connect"}, "infoTextInstallBottom": {"default": "Connect for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.spaceconnect"}, {"id": 200050, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Hex Match"}, "subtitle": {"default": "Solve hexagonal puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/hex_match-icon.jpg", "applicationId": "com.gimica.hexmatch", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Hex Match"}, "infoTextInstallBottom": {"default": "Solve hexagonal puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.hexmatch"}, {"id": 200062, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Water Sorter"}, "subtitle": {"default": "Sort all the colors for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/water_sorter_icon.jpg", "applicationId": "com.gimica.watersorter", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Water Sorter"}, "infoTextInstallBottom": {"default": "Sort all the colors for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.watersorter"}, {"id": 200068, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Tile Match Pro"}, "subtitle": {"default": "Clear tiles from the board for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/tile_match_pro_icon.jpg", "applicationId": "com.gimica.tilematchpro", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Tile Match Pro"}, "infoTextInstallBottom": {"default": "Clear tiles from the board for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.tilematchpro"}, {"id": 200072, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Tangram Heaven"}, "subtitle": {"default": "Complete Tangram puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/tangram_heaven_icon.jpg", "applicationId": "com.gimica.tangram", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Tangram Heaven"}, "infoTextInstallBottom": {"default": "Complete Tangram puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.tangram"}]}, {"market": "us-staging-test", "statusMessage": {"default": "Service is temporarily unavailable", "en-US": "Service is temporarily unavailable"}, "playGamesTitle": {"default": "You still can play our awesome games. All our scientists are already working to resolve the issue", "en-US": "You still can play our awesome games. All our scientists are already working to resolve the issue"}, "items": [{"id": 200039, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Treasure Master 🤸‍♂️"}, "subtitle": {"default": "Defeat monsters for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/treasure_master.png", "applicationId": "com.gimica.treasuremaster", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Treasure Master"}, "infoTextInstallBottom": {"default": "Defeat monsters for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.treasuremaster"}, {"id": 200044, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Solitaire Verse"}, "subtitle": {"default": "The more games you win the more loyalty coins you get"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/solitaire_verse.jpg", "applicationId": "com.gimica.solitaireverse", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Solitaire Verse"}, "infoTextInstallBottom": {"default": "The more games you win the more loyalty coins you get"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.solitaireverse"}, {"id": 200052, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Sugar Rush Adventure"}, "subtitle": {"default": "Match 3 to make sweet treats for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/sugar_rush_icon.jpg", "applicationId": "com.gimica.sugarmatch", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Sugar Rush Adventure"}, "infoTextInstallBottom": {"default": "Match 3 to make sweet treats for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.sugarmatch"}, {"id": 200048, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON>"}, "subtitle": {"default": "Have a blast and merge blocks to survive as long as you can for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/merge_blast_icon.jpg", "applicationId": "com.gimica.mergeblast", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Merge Blast"}, "infoTextInstallBottom": {"default": "Have a blast and merge blocks to survive as long as you can for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mergeblast"}, {"id": 200042, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON>"}, "subtitle": {"default": "Destroy the blocks and collect pick ups for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/ball_bounce.jpg", "applicationId": "com.gimica.ballbounce", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Ball Bounce"}, "infoTextInstallBottom": {"default": "Destroy the blocks and collect pick ups for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.ballbounce"}, {"id": 200051, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON>zle <PERSON>"}, "subtitle": {"default": "Enter a state of bliss and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/wooden_puzzle_bliss_icon.jpg", "applicationId": "com.gimica.zentiles", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON><PERSON> Puzzle Bliss"}, "infoTextInstallBottom": {"default": "Enter a state of bliss and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.zentiles"}, {"id": 200041, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON>"}, "subtitle": {"default": "Smash through level after level for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/mad_smash.jpg", "applicationId": "com.gimica.madsmash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Mad Smash"}, "infoTextInstallBottom": {"default": "Smash through level after level for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.madsmash"}, {"id": 200045, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Puzzle Pop Blaster"}, "subtitle": {"default": "Match colored blocks to make loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/puzzle_pop_blaster.jpg", "applicationId": "com.gimica.puzzlepopblaster", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Puzzle Pop Blaster"}, "infoTextInstallBottom": {"default": "Match colored blocks to make loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.puzzlepopblaster"}, {"id": 200057, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Bubble Pop: Wild Rescue"}, "subtitle": {"default": "Solve puzzles and rescue the animals for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/bubble_pop_icon.jpg", "applicationId": "com.gimica.bubblepop", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Bubble Pop: Wild Rescue"}, "infoTextInstallBottom": {"default": "Solve puzzles and rescue the animals for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.bubblepop"}, {"id": 200049, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Emoji <PERSON>lickers"}, "subtitle": {"default": "Tap and bounce your way to more loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/emoji_clickers_icon.jpg", "applicationId": "com.gimica.emojiclickers", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Emoji <PERSON>"}, "infoTextInstallBottom": {"default": "Tap and bounce your way to more loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.emojiclickers"}, {"id": 200040, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Mix Blox"}, "subtitle": {"default": "Make number matches for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/mix_blox_icon.jpg", "applicationId": "com.gimica.mixblox", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Mix Blox"}, "infoTextInstallBottom": {"default": "Make number matches for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mixblox"}, {"id": 200047, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Trivia Madness"}, "subtitle": {"default": "Put your general knowledge to the test and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/trivia_madness_icon.jpg", "applicationId": "com.gimica.triviamadness", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Trivia Madness"}, "infoTextInstallBottom": {"default": "Put your general knowledge to the test and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.triviamadness"}, {"id": 200065, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Crystal Crush"}, "subtitle": {"default": "Mine precious gems and earn yourself some loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/crystal_crush_icon.jpg", "applicationId": "com.gimica.crystalcrush", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Crystal Crush"}, "infoTextInstallBottom": {"default": "Mine precious gems and earn yourself some loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.crystalcrush"}, {"id": 200053, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Cars Merge and Defend"}, "subtitle": {"default": "Merge and build your team to defeat your enemies for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/carsmerge_icon.jpg", "applicationId": "com.gimica.carsmerge", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Cars Merge and Defend"}, "infoTextInstallBottom": {"default": "Merge and build your team to defeat your enemies for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.carsmerge"}, {"id": 200054, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Block Hole Clash"}, "subtitle": {"default": "Remove the correct blocks for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/blockhole_icon.jpg", "applicationId": "com.gimica.blockholeclash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Block Hole Clash"}, "infoTextInstallBottom": {"default": "Remove the correct blocks for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.blockholeclash"}, {"id": 200055, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Idle Merge <PERSON>"}, "subtitle": {"default": "Merge your way to improved production and loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/idlemerge_icon.jpg", "applicationId": "com.gimica.idlemergefun", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Idle Merge Fun"}, "infoTextInstallBottom": {"default": "Merge your way to improved production and loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.idlemergefun"}, {"id": 200056, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Brick Slider"}, "subtitle": {"default": "Survive the wall by making complete rows and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/block_slider_icon.jpg", "applicationId": "com.gimica.blockslider", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON> Slider"}, "infoTextInstallBottom": {"default": "Survive the wall by making complete rows and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.blockslider"}, {"id": 200060, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON><PERSON>"}, "subtitle": {"default": "Earn loyalty coins and relax with a classic"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/sudoku_icon.jpg", "applicationId": "com.gimica.sudoku", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Sudoku"}, "infoTextInstallBottom": {"default": "Earn loyalty coins and relax with a classic"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.sudoku"}, {"id": 200064, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Make Ten"}, "subtitle": {"default": "Make ten for loyalty coins!"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/make_ten_icon.jpg", "applicationId": "com.gimica.maketen", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Make Ten"}, "infoTextInstallBottom": {"default": "Make ten for loyalty coins!"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.maketen"}, {"id": 200046, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Word Seeker"}, "subtitle": {"default": "Solve word puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/word_seeker_icon.jpg", "applicationId": "com.gimica.wordseeker", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Word Seeker"}, "infoTextInstallBottom": {"default": "Solve word puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.wordseeker"}, {"id": 200043, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "2048 Puzzle Fun"}, "subtitle": {"default": "Reach 2048 to get more loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/hexa_puzzle_fun.jpg", "applicationId": "com.gimica.hexapuzzlefun", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play 2048 Puzzle Fun"}, "infoTextInstallBottom": {"default": "Reach 2048 to get more loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.hexapuzzlefun"}, {"id": 200059, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Brickdoku"}, "subtitle": {"default": "Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/brickdoku_icon.jpg", "applicationId": "com.gimica.brickdoku", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Brickdoku"}, "infoTextInstallBottom": {"default": "Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.brickdoku"}, {"id": 200061, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Word Kitchen"}, "subtitle": {"default": "Take on the heat of the Word Kitchen and discover words for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/word_kitchen_icon.jpg", "applicationId": "com.gimica.wordkitchen", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Word Kitchen"}, "infoTextInstallBottom": {"default": "Take on the heat of the Word Kitchen and discover words for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.wordkitchen"}, {"id": 200063, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Color Logic"}, "subtitle": {"default": "Test your logic for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/color_logic_icon.jpg", "applicationId": "com.gimica.colorlogic", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Color Logic"}, "infoTextInstallBottom": {"default": "Test your logic for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.colorlogic"}, {"id": 200067, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Spiral Drop"}, "subtitle": {"default": "Survive the drop for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/spiral_drop_icon.jpg", "applicationId": "com.gimica.helixdash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Spiral Drop"}, "infoTextInstallBottom": {"default": "Survive the drop for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.helixdash"}, {"id": 200058, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Mayan <PERSON>"}, "subtitle": {"default": "Survive Aztec trials for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/marble_madness_icon.jpg", "applicationId": "com.gimica.marblemadness", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON>n <PERSON>"}, "infoTextInstallBottom": {"default": "Survive Aztec trials for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.marblemadness"}, {"id": 200069, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Space Connect"}, "subtitle": {"default": "Connect for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/space_connect_icon.jpg", "applicationId": "com.gimica.spaceconnect", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Space Connect"}, "infoTextInstallBottom": {"default": "Connect for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.spaceconnect"}, {"id": 200050, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Hex Match"}, "subtitle": {"default": "Solve hexagonal puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/hex_match-icon.jpg", "applicationId": "com.gimica.hexmatch", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Hex Match"}, "infoTextInstallBottom": {"default": "Solve hexagonal puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.hexmatch"}, {"id": 200062, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Water Sorter"}, "subtitle": {"default": "Sort all the colors for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/water_sorter_icon.jpg", "applicationId": "com.gimica.watersorter", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Water Sorter"}, "infoTextInstallBottom": {"default": "Sort all the colors for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.watersorter"}, {"id": 200068, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Tile Match Pro"}, "subtitle": {"default": "Clear tiles from the board for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/tile_match_pro_icon.jpg", "applicationId": "com.gimica.tilematchpro", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Tile Match Pro"}, "infoTextInstallBottom": {"default": "Clear tiles from the board for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.tilematchpro"}, {"id": 200072, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Tangram Heaven"}, "subtitle": {"default": "Complete Tangram puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/tangram_heaven_icon.jpg", "applicationId": "com.gimica.tangram", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Tangram Heaven"}, "infoTextInstallBottom": {"default": "Complete Tangram puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.tangram"}]}, {"market": "gb", "statusMessage": {"default": "Service is temporarily unavailable", "en-US": "Service is temporarily unavailable"}, "playGamesTitle": {"default": "You still can play our awesome games. All our scientists are already working to resolve the issue", "en-US": "You still can play our awesome games. All our scientists are already working to resolve the issue"}, "items": [{"id": 200039, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Treasure Master 🤸‍♂️"}, "subtitle": {"default": "Defeat monsters for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/treasure_master.png", "applicationId": "com.gimica.treasuremaster", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Treasure Master"}, "infoTextInstallBottom": {"default": "Defeat monsters for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.treasuremaster"}, {"id": 200044, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Solitaire Verse"}, "subtitle": {"default": "The more games you win the more loyalty coins you get"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/solitaire_verse.jpg", "applicationId": "com.gimica.solitaireverse", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Solitaire Verse"}, "infoTextInstallBottom": {"default": "The more games you win the more loyalty coins you get"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.solitaireverse"}, {"id": 200052, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Sugar Rush Adventure"}, "subtitle": {"default": "Match 3 to make sweet treats for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/sugar_rush_icon.jpg", "applicationId": "com.gimica.sugarmatch", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Sugar Rush Adventure"}, "infoTextInstallBottom": {"default": "Match 3 to make sweet treats for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.sugarmatch"}, {"id": 200048, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON>"}, "subtitle": {"default": "Have a blast and merge blocks to survive as long as you can for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/merge_blast_icon.jpg", "applicationId": "com.gimica.mergeblast", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Merge Blast"}, "infoTextInstallBottom": {"default": "Have a blast and merge blocks to survive as long as you can for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mergeblast"}, {"id": 200042, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON>"}, "subtitle": {"default": "Destroy the blocks and collect pick ups for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/ball_bounce.jpg", "applicationId": "com.gimica.ballbounce", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Ball Bounce"}, "infoTextInstallBottom": {"default": "Destroy the blocks and collect pick ups for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.ballbounce"}, {"id": 200051, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON>zle <PERSON>"}, "subtitle": {"default": "Enter a state of bliss and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/wooden_puzzle_bliss_icon.jpg", "applicationId": "com.gimica.zentiles", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON><PERSON> Puzzle Bliss"}, "infoTextInstallBottom": {"default": "Enter a state of bliss and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.zentiles"}, {"id": 200041, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON>"}, "subtitle": {"default": "Smash through level after level for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/mad_smash.jpg", "applicationId": "com.gimica.madsmash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Mad Smash"}, "infoTextInstallBottom": {"default": "Smash through level after level for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.madsmash"}, {"id": 200045, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Puzzle Pop Blaster"}, "subtitle": {"default": "Match colored blocks to make loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/puzzle_pop_blaster.jpg", "applicationId": "com.gimica.puzzlepopblaster", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Puzzle Pop Blaster"}, "infoTextInstallBottom": {"default": "Match colored blocks to make loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.puzzlepopblaster"}, {"id": 200057, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Bubble Pop: Wild Rescue"}, "subtitle": {"default": "Solve puzzles and rescue the animals for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/bubble_pop_icon.jpg", "applicationId": "com.gimica.bubblepop", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Bubble Pop: Wild Rescue"}, "infoTextInstallBottom": {"default": "Solve puzzles and rescue the animals for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.bubblepop"}, {"id": 200049, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Emoji <PERSON>lickers"}, "subtitle": {"default": "Tap and bounce your way to more loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/emoji_clickers_icon.jpg", "applicationId": "com.gimica.emojiclickers", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Emoji <PERSON>"}, "infoTextInstallBottom": {"default": "Tap and bounce your way to more loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.emojiclickers"}, {"id": 200040, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Mix Blox"}, "subtitle": {"default": "Make number matches for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/mix_blox_icon.jpg", "applicationId": "com.gimica.mixblox", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Mix Blox"}, "infoTextInstallBottom": {"default": "Make number matches for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mixblox"}, {"id": 200047, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Trivia Madness"}, "subtitle": {"default": "Put your general knowledge to the test and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/trivia_madness_icon.jpg", "applicationId": "com.gimica.triviamadness", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Trivia Madness"}, "infoTextInstallBottom": {"default": "Put your general knowledge to the test and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.triviamadness"}, {"id": 200065, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Crystal Crush"}, "subtitle": {"default": "Mine precious gems and earn yourself some loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/crystal_crush_icon.jpg", "applicationId": "com.gimica.crystalcrush", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Crystal Crush"}, "infoTextInstallBottom": {"default": "Mine precious gems and earn yourself some loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.crystalcrush"}, {"id": 200053, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Cars Merge and Defend"}, "subtitle": {"default": "Merge and build your team to defeat your enemies for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/carsmerge_icon.jpg", "applicationId": "com.gimica.carsmerge", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Cars Merge and Defend"}, "infoTextInstallBottom": {"default": "Merge and build your team to defeat your enemies for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.carsmerge"}, {"id": 200054, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Block Hole Clash"}, "subtitle": {"default": "Remove the correct blocks for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/blockhole_icon.jpg", "applicationId": "com.gimica.blockholeclash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Block Hole Clash"}, "infoTextInstallBottom": {"default": "Remove the correct blocks for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.blockholeclash"}, {"id": 200055, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Idle Merge <PERSON>"}, "subtitle": {"default": "Merge your way to improved production and loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/idlemerge_icon.jpg", "applicationId": "com.gimica.idlemergefun", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Idle Merge Fun"}, "infoTextInstallBottom": {"default": "Merge your way to improved production and loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.idlemergefun"}, {"id": 200056, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Brick Slider"}, "subtitle": {"default": "Survive the wall by making complete rows and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/block_slider_icon.jpg", "applicationId": "com.gimica.blockslider", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON> Slider"}, "infoTextInstallBottom": {"default": "Survive the wall by making complete rows and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.blockslider"}, {"id": 200060, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON><PERSON>"}, "subtitle": {"default": "Earn loyalty coins and relax with a classic"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/sudoku_icon.jpg", "applicationId": "com.gimica.sudoku", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Sudoku"}, "infoTextInstallBottom": {"default": "Earn loyalty coins and relax with a classic"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.sudoku"}, {"id": 200064, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Make Ten"}, "subtitle": {"default": "Make ten for loyalty coins!"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/make_ten_icon.jpg", "applicationId": "com.gimica.maketen", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Make Ten"}, "infoTextInstallBottom": {"default": "Make ten for loyalty coins!"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.maketen"}, {"id": 200046, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Word Seeker"}, "subtitle": {"default": "Solve word puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/word_seeker_icon.jpg", "applicationId": "com.gimica.wordseeker", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Word Seeker"}, "infoTextInstallBottom": {"default": "Solve word puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.wordseeker"}, {"id": 200043, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "2048 Puzzle Fun"}, "subtitle": {"default": "Reach 2048 to get more loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/hexa_puzzle_fun.jpg", "applicationId": "com.gimica.hexapuzzlefun", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play 2048 Puzzle Fun"}, "infoTextInstallBottom": {"default": "Reach 2048 to get more loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.hexapuzzlefun"}, {"id": 200059, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Brickdoku"}, "subtitle": {"default": "Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/brickdoku_icon.jpg", "applicationId": "com.gimica.brickdoku", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Brickdoku"}, "infoTextInstallBottom": {"default": "Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.brickdoku"}, {"id": 200061, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Word Kitchen"}, "subtitle": {"default": "Take on the heat of the Word Kitchen and discover words for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/word_kitchen_icon.jpg", "applicationId": "com.gimica.wordkitchen", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Word Kitchen"}, "infoTextInstallBottom": {"default": "Take on the heat of the Word Kitchen and discover words for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.wordkitchen"}, {"id": 200063, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Color Logic"}, "subtitle": {"default": "Test your logic for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/color_logic_icon.jpg", "applicationId": "com.gimica.colorlogic", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Color Logic"}, "infoTextInstallBottom": {"default": "Test your logic for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.colorlogic"}, {"id": 200067, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Spiral Drop"}, "subtitle": {"default": "Survive the drop for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/spiral_drop_icon.jpg", "applicationId": "com.gimica.helixdash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Spiral Drop"}, "infoTextInstallBottom": {"default": "Survive the drop for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.helixdash"}, {"id": 200058, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Mayan <PERSON>"}, "subtitle": {"default": "Survive Aztec trials for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/marble_madness_icon.jpg", "applicationId": "com.gimica.marblemadness", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON>n <PERSON>"}, "infoTextInstallBottom": {"default": "Survive Aztec trials for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.marblemadness"}, {"id": 200069, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Space Connect"}, "subtitle": {"default": "Connect for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/space_connect_icon.jpg", "applicationId": "com.gimica.spaceconnect", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Space Connect"}, "infoTextInstallBottom": {"default": "Connect for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.spaceconnect"}, {"id": 200050, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Hex Match"}, "subtitle": {"default": "Solve hexagonal puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/hex_match-icon.jpg", "applicationId": "com.gimica.hexmatch", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Hex Match"}, "infoTextInstallBottom": {"default": "Solve hexagonal puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.hexmatch"}, {"id": 200062, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Water Sorter"}, "subtitle": {"default": "Sort all the colors for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/water_sorter_icon.jpg", "applicationId": "com.gimica.watersorter", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Water Sorter"}, "infoTextInstallBottom": {"default": "Sort all the colors for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.watersorter"}, {"id": 200068, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Tile Match Pro"}, "subtitle": {"default": "Clear tiles from the board for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/tile_match_pro_icon.jpg", "applicationId": "com.gimica.tilematchpro", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Tile Match Pro"}, "infoTextInstallBottom": {"default": "Clear tiles from the board for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.tilematchpro"}, {"id": 200072, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Tangram Heaven"}, "subtitle": {"default": "Complete Tangram puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/tangram_heaven_icon.jpg", "applicationId": "com.gimica.tangram", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Tangram Heaven"}, "infoTextInstallBottom": {"default": "Complete Tangram puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.tangram"}]}, {"market": "gb-test", "statusMessage": {"default": "Service is temporarily unavailable", "en-US": "Service is temporarily unavailable"}, "playGamesTitle": {"default": "You still can play our awesome games. All our scientists are already working to resolve the issue", "en-US": "You still can play our awesome games. All our scientists are already working to resolve the issue"}, "items": [{"id": 200039, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Treasure Master 🤸‍♂️"}, "subtitle": {"default": "Defeat monsters for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/treasure_master.png", "applicationId": "com.gimica.treasuremaster", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Treasure Master"}, "infoTextInstallBottom": {"default": "Defeat monsters for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.treasuremaster"}, {"id": 200044, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Solitaire Verse"}, "subtitle": {"default": "The more games you win the more loyalty coins you get"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/solitaire_verse.jpg", "applicationId": "com.gimica.solitaireverse", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Solitaire Verse"}, "infoTextInstallBottom": {"default": "The more games you win the more loyalty coins you get"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.solitaireverse"}, {"id": 200052, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Sugar Rush Adventure"}, "subtitle": {"default": "Match 3 to make sweet treats for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/sugar_rush_icon.jpg", "applicationId": "com.gimica.sugarmatch", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Sugar Rush Adventure"}, "infoTextInstallBottom": {"default": "Match 3 to make sweet treats for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.sugarmatch"}, {"id": 200048, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON>"}, "subtitle": {"default": "Have a blast and merge blocks to survive as long as you can for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/merge_blast_icon.jpg", "applicationId": "com.gimica.mergeblast", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Merge Blast"}, "infoTextInstallBottom": {"default": "Have a blast and merge blocks to survive as long as you can for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mergeblast"}, {"id": 200042, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON>"}, "subtitle": {"default": "Destroy the blocks and collect pick ups for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/ball_bounce.jpg", "applicationId": "com.gimica.ballbounce", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Ball Bounce"}, "infoTextInstallBottom": {"default": "Destroy the blocks and collect pick ups for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.ballbounce"}, {"id": 200051, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON>zle <PERSON>"}, "subtitle": {"default": "Enter a state of bliss and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/wooden_puzzle_bliss_icon.jpg", "applicationId": "com.gimica.zentiles", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON><PERSON> Puzzle Bliss"}, "infoTextInstallBottom": {"default": "Enter a state of bliss and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.zentiles"}, {"id": 200041, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON>"}, "subtitle": {"default": "Smash through level after level for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/mad_smash.jpg", "applicationId": "com.gimica.madsmash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Mad Smash"}, "infoTextInstallBottom": {"default": "Smash through level after level for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.madsmash"}, {"id": 200045, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Puzzle Pop Blaster"}, "subtitle": {"default": "Match colored blocks to make loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/puzzle_pop_blaster.jpg", "applicationId": "com.gimica.puzzlepopblaster", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Puzzle Pop Blaster"}, "infoTextInstallBottom": {"default": "Match colored blocks to make loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.puzzlepopblaster"}, {"id": 200057, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Bubble Pop: Wild Rescue"}, "subtitle": {"default": "Solve puzzles and rescue the animals for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/bubble_pop_icon.jpg", "applicationId": "com.gimica.bubblepop", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Bubble Pop: Wild Rescue"}, "infoTextInstallBottom": {"default": "Solve puzzles and rescue the animals for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.bubblepop"}, {"id": 200049, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Emoji <PERSON>lickers"}, "subtitle": {"default": "Tap and bounce your way to more loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/emoji_clickers_icon.jpg", "applicationId": "com.gimica.emojiclickers", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Emoji <PERSON>"}, "infoTextInstallBottom": {"default": "Tap and bounce your way to more loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.emojiclickers"}, {"id": 200040, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Mix Blox"}, "subtitle": {"default": "Make number matches for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/mix_blox_icon.jpg", "applicationId": "com.gimica.mixblox", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Mix Blox"}, "infoTextInstallBottom": {"default": "Make number matches for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mixblox"}, {"id": 200047, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Trivia Madness"}, "subtitle": {"default": "Put your general knowledge to the test and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/trivia_madness_icon.jpg", "applicationId": "com.gimica.triviamadness", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Trivia Madness"}, "infoTextInstallBottom": {"default": "Put your general knowledge to the test and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.triviamadness"}, {"id": 200065, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Crystal Crush"}, "subtitle": {"default": "Mine precious gems and earn yourself some loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/crystal_crush_icon.jpg", "applicationId": "com.gimica.crystalcrush", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Crystal Crush"}, "infoTextInstallBottom": {"default": "Mine precious gems and earn yourself some loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.crystalcrush"}, {"id": 200053, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Cars Merge and Defend"}, "subtitle": {"default": "Merge and build your team to defeat your enemies for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/carsmerge_icon.jpg", "applicationId": "com.gimica.carsmerge", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Cars Merge and Defend"}, "infoTextInstallBottom": {"default": "Merge and build your team to defeat your enemies for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.carsmerge"}, {"id": 200054, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Block Hole Clash"}, "subtitle": {"default": "Remove the correct blocks for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/blockhole_icon.jpg", "applicationId": "com.gimica.blockholeclash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Block Hole Clash"}, "infoTextInstallBottom": {"default": "Remove the correct blocks for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.blockholeclash"}, {"id": 200055, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Idle Merge <PERSON>"}, "subtitle": {"default": "Merge your way to improved production and loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/idlemerge_icon.jpg", "applicationId": "com.gimica.idlemergefun", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Idle Merge Fun"}, "infoTextInstallBottom": {"default": "Merge your way to improved production and loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.idlemergefun"}, {"id": 200056, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Brick Slider"}, "subtitle": {"default": "Survive the wall by making complete rows and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/block_slider_icon.jpg", "applicationId": "com.gimica.blockslider", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON> Slider"}, "infoTextInstallBottom": {"default": "Survive the wall by making complete rows and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.blockslider"}, {"id": 200060, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON><PERSON>"}, "subtitle": {"default": "Earn loyalty coins and relax with a classic"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/sudoku_icon.jpg", "applicationId": "com.gimica.sudoku", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Sudoku"}, "infoTextInstallBottom": {"default": "Earn loyalty coins and relax with a classic"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.sudoku"}, {"id": 200064, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Make Ten"}, "subtitle": {"default": "Make ten for loyalty coins!"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/make_ten_icon.jpg", "applicationId": "com.gimica.maketen", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Make Ten"}, "infoTextInstallBottom": {"default": "Make ten for loyalty coins!"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.maketen"}, {"id": 200046, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Word Seeker"}, "subtitle": {"default": "Solve word puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/word_seeker_icon.jpg", "applicationId": "com.gimica.wordseeker", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Word Seeker"}, "infoTextInstallBottom": {"default": "Solve word puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.wordseeker"}, {"id": 200043, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "2048 Puzzle Fun"}, "subtitle": {"default": "Reach 2048 to get more loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/hexa_puzzle_fun.jpg", "applicationId": "com.gimica.hexapuzzlefun", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play 2048 Puzzle Fun"}, "infoTextInstallBottom": {"default": "Reach 2048 to get more loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.hexapuzzlefun"}, {"id": 200059, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Brickdoku"}, "subtitle": {"default": "Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/brickdoku_icon.jpg", "applicationId": "com.gimica.brickdoku", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Brickdoku"}, "infoTextInstallBottom": {"default": "Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.brickdoku"}, {"id": 200061, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Word Kitchen"}, "subtitle": {"default": "Take on the heat of the Word Kitchen and discover words for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/word_kitchen_icon.jpg", "applicationId": "com.gimica.wordkitchen", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Word Kitchen"}, "infoTextInstallBottom": {"default": "Take on the heat of the Word Kitchen and discover words for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.wordkitchen"}, {"id": 200063, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Color Logic"}, "subtitle": {"default": "Test your logic for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/color_logic_icon.jpg", "applicationId": "com.gimica.colorlogic", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Color Logic"}, "infoTextInstallBottom": {"default": "Test your logic for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.colorlogic"}, {"id": 200067, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Spiral Drop"}, "subtitle": {"default": "Survive the drop for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/spiral_drop_icon.jpg", "applicationId": "com.gimica.helixdash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Spiral Drop"}, "infoTextInstallBottom": {"default": "Survive the drop for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.helixdash"}, {"id": 200058, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Mayan <PERSON>"}, "subtitle": {"default": "Survive Aztec trials for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/marble_madness_icon.jpg", "applicationId": "com.gimica.marblemadness", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON>n <PERSON>"}, "infoTextInstallBottom": {"default": "Survive Aztec trials for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.marblemadness"}, {"id": 200069, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Space Connect"}, "subtitle": {"default": "Connect for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/space_connect_icon.jpg", "applicationId": "com.gimica.spaceconnect", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Space Connect"}, "infoTextInstallBottom": {"default": "Connect for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.spaceconnect"}, {"id": 200050, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Hex Match"}, "subtitle": {"default": "Solve hexagonal puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/hex_match-icon.jpg", "applicationId": "com.gimica.hexmatch", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Hex Match"}, "infoTextInstallBottom": {"default": "Solve hexagonal puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.hexmatch"}, {"id": 200062, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Water Sorter"}, "subtitle": {"default": "Sort all the colors for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/water_sorter_icon.jpg", "applicationId": "com.gimica.watersorter", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Water Sorter"}, "infoTextInstallBottom": {"default": "Sort all the colors for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.watersorter"}, {"id": 200068, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Tile Match Pro"}, "subtitle": {"default": "Clear tiles from the board for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/tile_match_pro_icon.jpg", "applicationId": "com.gimica.tilematchpro", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Tile Match Pro"}, "infoTextInstallBottom": {"default": "Clear tiles from the board for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.tilematchpro"}, {"id": 200072, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Tangram Heaven"}, "subtitle": {"default": "Complete Tangram puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/tangram_heaven_icon.jpg", "applicationId": "com.gimica.tangram", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Tangram Heaven"}, "infoTextInstallBottom": {"default": "Complete Tangram puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.tangram"}]}, {"market": "au", "statusMessage": {"default": "Service is temporarily unavailable", "en-US": "Service is temporarily unavailable"}, "playGamesTitle": {"default": "You still can play our awesome games. All our scientists are already working to resolve the issue", "en-US": "You still can play our awesome games. All our scientists are already working to resolve the issue"}, "items": [{"id": 200039, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Treasure Master 🤸‍♂️"}, "subtitle": {"default": "Defeat monsters for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/treasure_master.png", "applicationId": "com.gimica.treasuremaster", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Treasure Master"}, "infoTextInstallBottom": {"default": "Defeat monsters for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.treasuremaster"}, {"id": 200044, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Solitaire Verse"}, "subtitle": {"default": "The more games you win the more loyalty coins you get"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/solitaire_verse.jpg", "applicationId": "com.gimica.solitaireverse", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Solitaire Verse"}, "infoTextInstallBottom": {"default": "The more games you win the more loyalty coins you get"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.solitaireverse"}, {"id": 200052, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Sugar Rush Adventure"}, "subtitle": {"default": "Match 3 to make sweet treats for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/sugar_rush_icon.jpg", "applicationId": "com.gimica.sugarmatch", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Sugar Rush Adventure"}, "infoTextInstallBottom": {"default": "Match 3 to make sweet treats for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.sugarmatch"}, {"id": 200048, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON>"}, "subtitle": {"default": "Have a blast and merge blocks to survive as long as you can for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/merge_blast_icon.jpg", "applicationId": "com.gimica.mergeblast", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Merge Blast"}, "infoTextInstallBottom": {"default": "Have a blast and merge blocks to survive as long as you can for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mergeblast"}, {"id": 200042, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON>"}, "subtitle": {"default": "Destroy the blocks and collect pick ups for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/ball_bounce.jpg", "applicationId": "com.gimica.ballbounce", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Ball Bounce"}, "infoTextInstallBottom": {"default": "Destroy the blocks and collect pick ups for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.ballbounce"}, {"id": 200051, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON>zle <PERSON>"}, "subtitle": {"default": "Enter a state of bliss and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/wooden_puzzle_bliss_icon.jpg", "applicationId": "com.gimica.zentiles", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON><PERSON> Puzzle Bliss"}, "infoTextInstallBottom": {"default": "Enter a state of bliss and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.zentiles"}, {"id": 200041, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON>"}, "subtitle": {"default": "Smash through level after level for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/mad_smash.jpg", "applicationId": "com.gimica.madsmash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Mad Smash"}, "infoTextInstallBottom": {"default": "Smash through level after level for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.madsmash"}, {"id": 200045, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Puzzle Pop Blaster"}, "subtitle": {"default": "Match colored blocks to make loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/puzzle_pop_blaster.jpg", "applicationId": "com.gimica.puzzlepopblaster", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Puzzle Pop Blaster"}, "infoTextInstallBottom": {"default": "Match colored blocks to make loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.puzzlepopblaster"}, {"id": 200057, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Bubble Pop: Wild Rescue"}, "subtitle": {"default": "Solve puzzles and rescue the animals for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/bubble_pop_icon.jpg", "applicationId": "com.gimica.bubblepop", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Bubble Pop: Wild Rescue"}, "infoTextInstallBottom": {"default": "Solve puzzles and rescue the animals for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.bubblepop"}, {"id": 200049, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Emoji <PERSON>lickers"}, "subtitle": {"default": "Tap and bounce your way to more loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/emoji_clickers_icon.jpg", "applicationId": "com.gimica.emojiclickers", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Emoji <PERSON>"}, "infoTextInstallBottom": {"default": "Tap and bounce your way to more loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.emojiclickers"}, {"id": 200040, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Mix Blox"}, "subtitle": {"default": "Make number matches for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/mix_blox_icon.jpg", "applicationId": "com.gimica.mixblox", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Mix Blox"}, "infoTextInstallBottom": {"default": "Make number matches for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mixblox"}, {"id": 200047, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Trivia Madness"}, "subtitle": {"default": "Put your general knowledge to the test and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/trivia_madness_icon.jpg", "applicationId": "com.gimica.triviamadness", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Trivia Madness"}, "infoTextInstallBottom": {"default": "Put your general knowledge to the test and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.triviamadness"}, {"id": 200065, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Crystal Crush"}, "subtitle": {"default": "Mine precious gems and earn yourself some loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/crystal_crush_icon.jpg", "applicationId": "com.gimica.crystalcrush", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Crystal Crush"}, "infoTextInstallBottom": {"default": "Mine precious gems and earn yourself some loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.crystalcrush"}, {"id": 200053, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Cars Merge and Defend"}, "subtitle": {"default": "Merge and build your team to defeat your enemies for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/carsmerge_icon.jpg", "applicationId": "com.gimica.carsmerge", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Cars Merge and Defend"}, "infoTextInstallBottom": {"default": "Merge and build your team to defeat your enemies for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.carsmerge"}, {"id": 200054, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Block Hole Clash"}, "subtitle": {"default": "Remove the correct blocks for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/blockhole_icon.jpg", "applicationId": "com.gimica.blockholeclash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Block Hole Clash"}, "infoTextInstallBottom": {"default": "Remove the correct blocks for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.blockholeclash"}, {"id": 200055, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Idle Merge <PERSON>"}, "subtitle": {"default": "Merge your way to improved production and loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/idlemerge_icon.jpg", "applicationId": "com.gimica.idlemergefun", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Idle Merge Fun"}, "infoTextInstallBottom": {"default": "Merge your way to improved production and loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.idlemergefun"}, {"id": 200056, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Brick Slider"}, "subtitle": {"default": "Survive the wall by making complete rows and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/block_slider_icon.jpg", "applicationId": "com.gimica.blockslider", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON> Slider"}, "infoTextInstallBottom": {"default": "Survive the wall by making complete rows and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.blockslider"}, {"id": 200060, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON><PERSON>"}, "subtitle": {"default": "Earn loyalty coins and relax with a classic"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/sudoku_icon.jpg", "applicationId": "com.gimica.sudoku", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Sudoku"}, "infoTextInstallBottom": {"default": "Earn loyalty coins and relax with a classic"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.sudoku"}, {"id": 200064, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Make Ten"}, "subtitle": {"default": "Make ten for loyalty coins!"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/make_ten_icon.jpg", "applicationId": "com.gimica.maketen", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Make Ten"}, "infoTextInstallBottom": {"default": "Make ten for loyalty coins!"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.maketen"}, {"id": 200046, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Word Seeker"}, "subtitle": {"default": "Solve word puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/word_seeker_icon.jpg", "applicationId": "com.gimica.wordseeker", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Word Seeker"}, "infoTextInstallBottom": {"default": "Solve word puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.wordseeker"}, {"id": 200043, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "2048 Puzzle Fun"}, "subtitle": {"default": "Reach 2048 to get more loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/hexa_puzzle_fun.jpg", "applicationId": "com.gimica.hexapuzzlefun", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play 2048 Puzzle Fun"}, "infoTextInstallBottom": {"default": "Reach 2048 to get more loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.hexapuzzlefun"}, {"id": 200059, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Brickdoku"}, "subtitle": {"default": "Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/brickdoku_icon.jpg", "applicationId": "com.gimica.brickdoku", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Brickdoku"}, "infoTextInstallBottom": {"default": "Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.brickdoku"}, {"id": 200061, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Word Kitchen"}, "subtitle": {"default": "Take on the heat of the Word Kitchen and discover words for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/word_kitchen_icon.jpg", "applicationId": "com.gimica.wordkitchen", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Word Kitchen"}, "infoTextInstallBottom": {"default": "Take on the heat of the Word Kitchen and discover words for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.wordkitchen"}, {"id": 200063, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Color Logic"}, "subtitle": {"default": "Test your logic for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/color_logic_icon.jpg", "applicationId": "com.gimica.colorlogic", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Color Logic"}, "infoTextInstallBottom": {"default": "Test your logic for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.colorlogic"}, {"id": 200067, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Spiral Drop"}, "subtitle": {"default": "Survive the drop for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/spiral_drop_icon.jpg", "applicationId": "com.gimica.helixdash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Spiral Drop"}, "infoTextInstallBottom": {"default": "Survive the drop for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.helixdash"}, {"id": 200058, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Mayan <PERSON>"}, "subtitle": {"default": "Survive Aztec trials for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/marble_madness_icon.jpg", "applicationId": "com.gimica.marblemadness", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON>n <PERSON>"}, "infoTextInstallBottom": {"default": "Survive Aztec trials for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.marblemadness"}, {"id": 200069, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Space Connect"}, "subtitle": {"default": "Connect for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/space_connect_icon.jpg", "applicationId": "com.gimica.spaceconnect", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Space Connect"}, "infoTextInstallBottom": {"default": "Connect for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.spaceconnect"}, {"id": 200050, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Hex Match"}, "subtitle": {"default": "Solve hexagonal puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/hex_match-icon.jpg", "applicationId": "com.gimica.hexmatch", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Hex Match"}, "infoTextInstallBottom": {"default": "Solve hexagonal puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.hexmatch"}, {"id": 200062, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Water Sorter"}, "subtitle": {"default": "Sort all the colors for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/water_sorter_icon.jpg", "applicationId": "com.gimica.watersorter", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Water Sorter"}, "infoTextInstallBottom": {"default": "Sort all the colors for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.watersorter"}, {"id": 200068, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Tile Match Pro"}, "subtitle": {"default": "Clear tiles from the board for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/tile_match_pro_icon.jpg", "applicationId": "com.gimica.tilematchpro", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Tile Match Pro"}, "infoTextInstallBottom": {"default": "Clear tiles from the board for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.tilematchpro"}, {"id": 200072, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Tangram Heaven"}, "subtitle": {"default": "Complete Tangram puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/tangram_heaven_icon.jpg", "applicationId": "com.gimica.tangram", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Tangram Heaven"}, "infoTextInstallBottom": {"default": "Complete Tangram puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.tangram"}]}, {"market": "au-test", "statusMessage": {"default": "Service is temporarily unavailable", "en-US": "Service is temporarily unavailable"}, "playGamesTitle": {"default": "You still can play our awesome games. All our scientists are already working to resolve the issue", "en-US": "You still can play our awesome games. All our scientists are already working to resolve the issue"}, "items": [{"id": 200039, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Treasure Master 🤸‍♂️"}, "subtitle": {"default": "Defeat monsters for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/treasure_master.png", "applicationId": "com.gimica.treasuremaster", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Treasure Master"}, "infoTextInstallBottom": {"default": "Defeat monsters for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.treasuremaster"}, {"id": 200044, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Solitaire Verse"}, "subtitle": {"default": "The more games you win the more loyalty coins you get"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/solitaire_verse.jpg", "applicationId": "com.gimica.solitaireverse", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Solitaire Verse"}, "infoTextInstallBottom": {"default": "The more games you win the more loyalty coins you get"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.solitaireverse"}, {"id": 200052, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Sugar Rush Adventure"}, "subtitle": {"default": "Match 3 to make sweet treats for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/sugar_rush_icon.jpg", "applicationId": "com.gimica.sugarmatch", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Sugar Rush Adventure"}, "infoTextInstallBottom": {"default": "Match 3 to make sweet treats for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.sugarmatch"}, {"id": 200048, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON>"}, "subtitle": {"default": "Have a blast and merge blocks to survive as long as you can for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/merge_blast_icon.jpg", "applicationId": "com.gimica.mergeblast", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Merge Blast"}, "infoTextInstallBottom": {"default": "Have a blast and merge blocks to survive as long as you can for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mergeblast"}, {"id": 200042, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON>"}, "subtitle": {"default": "Destroy the blocks and collect pick ups for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/ball_bounce.jpg", "applicationId": "com.gimica.ballbounce", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Ball Bounce"}, "infoTextInstallBottom": {"default": "Destroy the blocks and collect pick ups for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.ballbounce"}, {"id": 200051, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON>zle <PERSON>"}, "subtitle": {"default": "Enter a state of bliss and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/wooden_puzzle_bliss_icon.jpg", "applicationId": "com.gimica.zentiles", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON><PERSON> Puzzle Bliss"}, "infoTextInstallBottom": {"default": "Enter a state of bliss and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.zentiles"}, {"id": 200041, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON>"}, "subtitle": {"default": "Smash through level after level for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/mad_smash.jpg", "applicationId": "com.gimica.madsmash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Mad Smash"}, "infoTextInstallBottom": {"default": "Smash through level after level for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.madsmash"}, {"id": 200045, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Puzzle Pop Blaster"}, "subtitle": {"default": "Match colored blocks to make loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/puzzle_pop_blaster.jpg", "applicationId": "com.gimica.puzzlepopblaster", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Puzzle Pop Blaster"}, "infoTextInstallBottom": {"default": "Match colored blocks to make loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.puzzlepopblaster"}, {"id": 200057, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Bubble Pop: Wild Rescue"}, "subtitle": {"default": "Solve puzzles and rescue the animals for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/bubble_pop_icon.jpg", "applicationId": "com.gimica.bubblepop", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Bubble Pop: Wild Rescue"}, "infoTextInstallBottom": {"default": "Solve puzzles and rescue the animals for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.bubblepop"}, {"id": 200049, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Emoji <PERSON>lickers"}, "subtitle": {"default": "Tap and bounce your way to more loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/emoji_clickers_icon.jpg", "applicationId": "com.gimica.emojiclickers", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Emoji <PERSON>"}, "infoTextInstallBottom": {"default": "Tap and bounce your way to more loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.emojiclickers"}, {"id": 200040, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Mix Blox"}, "subtitle": {"default": "Make number matches for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/mix_blox_icon.jpg", "applicationId": "com.gimica.mixblox", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Mix Blox"}, "infoTextInstallBottom": {"default": "Make number matches for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mixblox"}, {"id": 200047, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Trivia Madness"}, "subtitle": {"default": "Put your general knowledge to the test and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/trivia_madness_icon.jpg", "applicationId": "com.gimica.triviamadness", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Trivia Madness"}, "infoTextInstallBottom": {"default": "Put your general knowledge to the test and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.triviamadness"}, {"id": 200065, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Crystal Crush"}, "subtitle": {"default": "Mine precious gems and earn yourself some loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/crystal_crush_icon.jpg", "applicationId": "com.gimica.crystalcrush", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Crystal Crush"}, "infoTextInstallBottom": {"default": "Mine precious gems and earn yourself some loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.crystalcrush"}, {"id": 200053, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Cars Merge and Defend"}, "subtitle": {"default": "Merge and build your team to defeat your enemies for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/carsmerge_icon.jpg", "applicationId": "com.gimica.carsmerge", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Cars Merge and Defend"}, "infoTextInstallBottom": {"default": "Merge and build your team to defeat your enemies for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.carsmerge"}, {"id": 200054, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Block Hole Clash"}, "subtitle": {"default": "Remove the correct blocks for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/blockhole_icon.jpg", "applicationId": "com.gimica.blockholeclash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Block Hole Clash"}, "infoTextInstallBottom": {"default": "Remove the correct blocks for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.blockholeclash"}, {"id": 200055, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Idle Merge <PERSON>"}, "subtitle": {"default": "Merge your way to improved production and loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/idlemerge_icon.jpg", "applicationId": "com.gimica.idlemergefun", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Idle Merge Fun"}, "infoTextInstallBottom": {"default": "Merge your way to improved production and loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.idlemergefun"}, {"id": 200056, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Brick Slider"}, "subtitle": {"default": "Survive the wall by making complete rows and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/block_slider_icon.jpg", "applicationId": "com.gimica.blockslider", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON> Slider"}, "infoTextInstallBottom": {"default": "Survive the wall by making complete rows and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.blockslider"}, {"id": 200060, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON><PERSON>"}, "subtitle": {"default": "Earn loyalty coins and relax with a classic"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/sudoku_icon.jpg", "applicationId": "com.gimica.sudoku", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Sudoku"}, "infoTextInstallBottom": {"default": "Earn loyalty coins and relax with a classic"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.sudoku"}, {"id": 200064, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Make Ten"}, "subtitle": {"default": "Make ten for loyalty coins!"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/make_ten_icon.jpg", "applicationId": "com.gimica.maketen", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Make Ten"}, "infoTextInstallBottom": {"default": "Make ten for loyalty coins!"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.maketen"}, {"id": 200046, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Word Seeker"}, "subtitle": {"default": "Solve word puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/word_seeker_icon.jpg", "applicationId": "com.gimica.wordseeker", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Word Seeker"}, "infoTextInstallBottom": {"default": "Solve word puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.wordseeker"}, {"id": 200043, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "2048 Puzzle Fun"}, "subtitle": {"default": "Reach 2048 to get more loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/hexa_puzzle_fun.jpg", "applicationId": "com.gimica.hexapuzzlefun", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play 2048 Puzzle Fun"}, "infoTextInstallBottom": {"default": "Reach 2048 to get more loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.hexapuzzlefun"}, {"id": 200059, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Brickdoku"}, "subtitle": {"default": "Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/brickdoku_icon.jpg", "applicationId": "com.gimica.brickdoku", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Brickdoku"}, "infoTextInstallBottom": {"default": "Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.brickdoku"}, {"id": 200061, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Word Kitchen"}, "subtitle": {"default": "Take on the heat of the Word Kitchen and discover words for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/word_kitchen_icon.jpg", "applicationId": "com.gimica.wordkitchen", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Word Kitchen"}, "infoTextInstallBottom": {"default": "Take on the heat of the Word Kitchen and discover words for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.wordkitchen"}, {"id": 200063, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Color Logic"}, "subtitle": {"default": "Test your logic for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/color_logic_icon.jpg", "applicationId": "com.gimica.colorlogic", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Color Logic"}, "infoTextInstallBottom": {"default": "Test your logic for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.colorlogic"}, {"id": 200067, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Spiral Drop"}, "subtitle": {"default": "Survive the drop for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/spiral_drop_icon.jpg", "applicationId": "com.gimica.helixdash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Spiral Drop"}, "infoTextInstallBottom": {"default": "Survive the drop for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.helixdash"}, {"id": 200058, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Mayan <PERSON>"}, "subtitle": {"default": "Survive Aztec trials for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/marble_madness_icon.jpg", "applicationId": "com.gimica.marblemadness", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON>n <PERSON>"}, "infoTextInstallBottom": {"default": "Survive Aztec trials for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.marblemadness"}, {"id": 200069, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Space Connect"}, "subtitle": {"default": "Connect for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/space_connect_icon.jpg", "applicationId": "com.gimica.spaceconnect", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Space Connect"}, "infoTextInstallBottom": {"default": "Connect for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.spaceconnect"}, {"id": 200050, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Hex Match"}, "subtitle": {"default": "Solve hexagonal puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/hex_match-icon.jpg", "applicationId": "com.gimica.hexmatch", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Hex Match"}, "infoTextInstallBottom": {"default": "Solve hexagonal puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.hexmatch"}, {"id": 200062, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Water Sorter"}, "subtitle": {"default": "Sort all the colors for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/water_sorter_icon.jpg", "applicationId": "com.gimica.watersorter", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Water Sorter"}, "infoTextInstallBottom": {"default": "Sort all the colors for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.watersorter"}, {"id": 200068, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Tile Match Pro"}, "subtitle": {"default": "Clear tiles from the board for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/tile_match_pro_icon.jpg", "applicationId": "com.gimica.tilematchpro", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Tile Match Pro"}, "infoTextInstallBottom": {"default": "Clear tiles from the board for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.tilematchpro"}, {"id": 200072, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Tangram Heaven"}, "subtitle": {"default": "Complete Tangram puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/tangram_heaven_icon.jpg", "applicationId": "com.gimica.tangram", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Tangram Heaven"}, "infoTextInstallBottom": {"default": "Complete Tangram puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.tangram"}]}, {"market": "asia", "statusMessage": {"default": "Service is temporarily unavailable", "en-US": "Service is temporarily unavailable"}, "playGamesTitle": {"default": "You still can play our awesome games. All our scientists are already working to resolve the issue", "en-US": "You still can play our awesome games. All our scientists are already working to resolve the issue"}, "items": [{"id": 200039, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Treasure Master 🤸‍♂️"}, "subtitle": {"default": "Defeat monsters for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/treasure_master.png", "applicationId": "com.gimica.treasuremaster", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Treasure Master"}, "infoTextInstallBottom": {"default": "Defeat monsters for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.treasuremaster"}, {"id": 200044, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Solitaire Verse"}, "subtitle": {"default": "The more games you win the more loyalty coins you get"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/solitaire_verse.jpg", "applicationId": "com.gimica.solitaireverse", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Solitaire Verse"}, "infoTextInstallBottom": {"default": "The more games you win the more loyalty coins you get"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.solitaireverse"}, {"id": 200052, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Sugar Rush Adventure"}, "subtitle": {"default": "Match 3 to make sweet treats for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/sugar_rush_icon.jpg", "applicationId": "com.gimica.sugarmatch", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Sugar Rush Adventure"}, "infoTextInstallBottom": {"default": "Match 3 to make sweet treats for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.sugarmatch"}, {"id": 200048, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON>"}, "subtitle": {"default": "Have a blast and merge blocks to survive as long as you can for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/merge_blast_icon.jpg", "applicationId": "com.gimica.mergeblast", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Merge Blast"}, "infoTextInstallBottom": {"default": "Have a blast and merge blocks to survive as long as you can for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mergeblast"}, {"id": 200042, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON>"}, "subtitle": {"default": "Destroy the blocks and collect pick ups for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/ball_bounce.jpg", "applicationId": "com.gimica.ballbounce", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Ball Bounce"}, "infoTextInstallBottom": {"default": "Destroy the blocks and collect pick ups for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.ballbounce"}, {"id": 200051, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON>zle <PERSON>"}, "subtitle": {"default": "Enter a state of bliss and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/wooden_puzzle_bliss_icon.jpg", "applicationId": "com.gimica.zentiles", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON><PERSON> Puzzle Bliss"}, "infoTextInstallBottom": {"default": "Enter a state of bliss and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.zentiles"}, {"id": 200041, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON>"}, "subtitle": {"default": "Smash through level after level for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/mad_smash.jpg", "applicationId": "com.gimica.madsmash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Mad Smash"}, "infoTextInstallBottom": {"default": "Smash through level after level for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.madsmash"}, {"id": 200045, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Puzzle Pop Blaster"}, "subtitle": {"default": "Match colored blocks to make loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/puzzle_pop_blaster.jpg", "applicationId": "com.gimica.puzzlepopblaster", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Puzzle Pop Blaster"}, "infoTextInstallBottom": {"default": "Match colored blocks to make loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.puzzlepopblaster"}, {"id": 200057, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Bubble Pop: Wild Rescue"}, "subtitle": {"default": "Solve puzzles and rescue the animals for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/bubble_pop_icon.jpg", "applicationId": "com.gimica.bubblepop", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Bubble Pop: Wild Rescue"}, "infoTextInstallBottom": {"default": "Solve puzzles and rescue the animals for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.bubblepop"}, {"id": 200049, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Emoji <PERSON>lickers"}, "subtitle": {"default": "Tap and bounce your way to more loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/emoji_clickers_icon.jpg", "applicationId": "com.gimica.emojiclickers", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Emoji <PERSON>"}, "infoTextInstallBottom": {"default": "Tap and bounce your way to more loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.emojiclickers"}, {"id": 200040, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Mix Blox"}, "subtitle": {"default": "Make number matches for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/mix_blox_icon.jpg", "applicationId": "com.gimica.mixblox", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Mix Blox"}, "infoTextInstallBottom": {"default": "Make number matches for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mixblox"}, {"id": 200047, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Trivia Madness"}, "subtitle": {"default": "Put your general knowledge to the test and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/trivia_madness_icon.jpg", "applicationId": "com.gimica.triviamadness", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Trivia Madness"}, "infoTextInstallBottom": {"default": "Put your general knowledge to the test and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.triviamadness"}, {"id": 200065, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Crystal Crush"}, "subtitle": {"default": "Mine precious gems and earn yourself some loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/crystal_crush_icon.jpg", "applicationId": "com.gimica.crystalcrush", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Crystal Crush"}, "infoTextInstallBottom": {"default": "Mine precious gems and earn yourself some loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.crystalcrush"}, {"id": 200053, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Cars Merge and Defend"}, "subtitle": {"default": "Merge and build your team to defeat your enemies for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/carsmerge_icon.jpg", "applicationId": "com.gimica.carsmerge", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Cars Merge and Defend"}, "infoTextInstallBottom": {"default": "Merge and build your team to defeat your enemies for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.carsmerge"}, {"id": 200054, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Block Hole Clash"}, "subtitle": {"default": "Remove the correct blocks for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/blockhole_icon.jpg", "applicationId": "com.gimica.blockholeclash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Block Hole Clash"}, "infoTextInstallBottom": {"default": "Remove the correct blocks for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.blockholeclash"}, {"id": 200055, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Idle Merge <PERSON>"}, "subtitle": {"default": "Merge your way to improved production and loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/idlemerge_icon.jpg", "applicationId": "com.gimica.idlemergefun", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Idle Merge Fun"}, "infoTextInstallBottom": {"default": "Merge your way to improved production and loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.idlemergefun"}, {"id": 200056, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Brick Slider"}, "subtitle": {"default": "Survive the wall by making complete rows and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/block_slider_icon.jpg", "applicationId": "com.gimica.blockslider", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON> Slider"}, "infoTextInstallBottom": {"default": "Survive the wall by making complete rows and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.blockslider"}, {"id": 200060, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON><PERSON>"}, "subtitle": {"default": "Earn loyalty coins and relax with a classic"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/sudoku_icon.jpg", "applicationId": "com.gimica.sudoku", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Sudoku"}, "infoTextInstallBottom": {"default": "Earn loyalty coins and relax with a classic"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.sudoku"}, {"id": 200064, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Make Ten"}, "subtitle": {"default": "Make ten for loyalty coins!"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/make_ten_icon.jpg", "applicationId": "com.gimica.maketen", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Make Ten"}, "infoTextInstallBottom": {"default": "Make ten for loyalty coins!"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.maketen"}, {"id": 200046, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Word Seeker"}, "subtitle": {"default": "Solve word puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/word_seeker_icon.jpg", "applicationId": "com.gimica.wordseeker", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Word Seeker"}, "infoTextInstallBottom": {"default": "Solve word puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.wordseeker"}, {"id": 200043, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "2048 Puzzle Fun"}, "subtitle": {"default": "Reach 2048 to get more loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/hexa_puzzle_fun.jpg", "applicationId": "com.gimica.hexapuzzlefun", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play 2048 Puzzle Fun"}, "infoTextInstallBottom": {"default": "Reach 2048 to get more loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.hexapuzzlefun"}, {"id": 200059, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Brickdoku"}, "subtitle": {"default": "Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/brickdoku_icon.jpg", "applicationId": "com.gimica.brickdoku", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Brickdoku"}, "infoTextInstallBottom": {"default": "Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.brickdoku"}, {"id": 200061, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Word Kitchen"}, "subtitle": {"default": "Take on the heat of the Word Kitchen and discover words for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/word_kitchen_icon.jpg", "applicationId": "com.gimica.wordkitchen", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Word Kitchen"}, "infoTextInstallBottom": {"default": "Take on the heat of the Word Kitchen and discover words for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.wordkitchen"}, {"id": 200063, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Color Logic"}, "subtitle": {"default": "Test your logic for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/color_logic_icon.jpg", "applicationId": "com.gimica.colorlogic", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Color Logic"}, "infoTextInstallBottom": {"default": "Test your logic for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.colorlogic"}, {"id": 200067, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Spiral Drop"}, "subtitle": {"default": "Survive the drop for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/spiral_drop_icon.jpg", "applicationId": "com.gimica.helixdash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Spiral Drop"}, "infoTextInstallBottom": {"default": "Survive the drop for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.helixdash"}, {"id": 200058, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Mayan <PERSON>"}, "subtitle": {"default": "Survive Aztec trials for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/marble_madness_icon.jpg", "applicationId": "com.gimica.marblemadness", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON>n <PERSON>"}, "infoTextInstallBottom": {"default": "Survive Aztec trials for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.marblemadness"}, {"id": 200069, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Space Connect"}, "subtitle": {"default": "Connect for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/space_connect_icon.jpg", "applicationId": "com.gimica.spaceconnect", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Space Connect"}, "infoTextInstallBottom": {"default": "Connect for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.spaceconnect"}, {"id": 200050, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Hex Match"}, "subtitle": {"default": "Solve hexagonal puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/hex_match-icon.jpg", "applicationId": "com.gimica.hexmatch", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Hex Match"}, "infoTextInstallBottom": {"default": "Solve hexagonal puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.hexmatch"}, {"id": 200062, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Water Sorter"}, "subtitle": {"default": "Sort all the colors for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/water_sorter_icon.jpg", "applicationId": "com.gimica.watersorter", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Water Sorter"}, "infoTextInstallBottom": {"default": "Sort all the colors for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.watersorter"}, {"id": 200068, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Tile Match Pro"}, "subtitle": {"default": "Clear tiles from the board for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/tile_match_pro_icon.jpg", "applicationId": "com.gimica.tilematchpro", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Tile Match Pro"}, "infoTextInstallBottom": {"default": "Clear tiles from the board for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.tilematchpro"}, {"id": 200072, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Tangram Heaven"}, "subtitle": {"default": "Complete Tangram puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/tangram_heaven_icon.jpg", "applicationId": "com.gimica.tangram", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Tangram Heaven"}, "infoTextInstallBottom": {"default": "Complete Tangram puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.tangram"}]}, {"market": "asia-test", "statusMessage": {"default": "Service is temporarily unavailable", "en-US": "Service is temporarily unavailable"}, "playGamesTitle": {"default": "You still can play our awesome games. All our scientists are already working to resolve the issue", "en-US": "You still can play our awesome games. All our scientists are already working to resolve the issue"}, "items": [{"id": 200039, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Treasure Master 🤸‍♂️"}, "subtitle": {"default": "Defeat monsters for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/treasure_master.png", "applicationId": "com.gimica.treasuremaster", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Treasure Master"}, "infoTextInstallBottom": {"default": "Defeat monsters for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.treasuremaster"}, {"id": 200044, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Solitaire Verse"}, "subtitle": {"default": "The more games you win the more loyalty coins you get"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/solitaire_verse.jpg", "applicationId": "com.gimica.solitaireverse", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Solitaire Verse"}, "infoTextInstallBottom": {"default": "The more games you win the more loyalty coins you get"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.solitaireverse"}, {"id": 200052, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Sugar Rush Adventure"}, "subtitle": {"default": "Match 3 to make sweet treats for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/sugar_rush_icon.jpg", "applicationId": "com.gimica.sugarmatch", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Sugar Rush Adventure"}, "infoTextInstallBottom": {"default": "Match 3 to make sweet treats for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.sugarmatch"}, {"id": 200048, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON>"}, "subtitle": {"default": "Have a blast and merge blocks to survive as long as you can for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/merge_blast_icon.jpg", "applicationId": "com.gimica.mergeblast", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Merge Blast"}, "infoTextInstallBottom": {"default": "Have a blast and merge blocks to survive as long as you can for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mergeblast"}, {"id": 200042, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON>"}, "subtitle": {"default": "Destroy the blocks and collect pick ups for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/ball_bounce.jpg", "applicationId": "com.gimica.ballbounce", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Ball Bounce"}, "infoTextInstallBottom": {"default": "Destroy the blocks and collect pick ups for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.ballbounce"}, {"id": 200051, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON>zle <PERSON>"}, "subtitle": {"default": "Enter a state of bliss and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/wooden_puzzle_bliss_icon.jpg", "applicationId": "com.gimica.zentiles", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON><PERSON> Puzzle Bliss"}, "infoTextInstallBottom": {"default": "Enter a state of bliss and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.zentiles"}, {"id": 200041, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON>"}, "subtitle": {"default": "Smash through level after level for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/mad_smash.jpg", "applicationId": "com.gimica.madsmash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Mad Smash"}, "infoTextInstallBottom": {"default": "Smash through level after level for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.madsmash"}, {"id": 200045, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Puzzle Pop Blaster"}, "subtitle": {"default": "Match colored blocks to make loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/puzzle_pop_blaster.jpg", "applicationId": "com.gimica.puzzlepopblaster", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Puzzle Pop Blaster"}, "infoTextInstallBottom": {"default": "Match colored blocks to make loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.puzzlepopblaster"}, {"id": 200057, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Bubble Pop: Wild Rescue"}, "subtitle": {"default": "Solve puzzles and rescue the animals for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/bubble_pop_icon.jpg", "applicationId": "com.gimica.bubblepop", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Bubble Pop: Wild Rescue"}, "infoTextInstallBottom": {"default": "Solve puzzles and rescue the animals for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.bubblepop"}, {"id": 200049, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Emoji <PERSON>lickers"}, "subtitle": {"default": "Tap and bounce your way to more loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/emoji_clickers_icon.jpg", "applicationId": "com.gimica.emojiclickers", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Emoji <PERSON>"}, "infoTextInstallBottom": {"default": "Tap and bounce your way to more loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.emojiclickers"}, {"id": 200040, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Mix Blox"}, "subtitle": {"default": "Make number matches for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/mix_blox_icon.jpg", "applicationId": "com.gimica.mixblox", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Mix Blox"}, "infoTextInstallBottom": {"default": "Make number matches for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mixblox"}, {"id": 200047, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Trivia Madness"}, "subtitle": {"default": "Put your general knowledge to the test and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/trivia_madness_icon.jpg", "applicationId": "com.gimica.triviamadness", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Trivia Madness"}, "infoTextInstallBottom": {"default": "Put your general knowledge to the test and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.triviamadness"}, {"id": 200065, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Crystal Crush"}, "subtitle": {"default": "Mine precious gems and earn yourself some loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/crystal_crush_icon.jpg", "applicationId": "com.gimica.crystalcrush", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Crystal Crush"}, "infoTextInstallBottom": {"default": "Mine precious gems and earn yourself some loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.crystalcrush"}, {"id": 200053, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Cars Merge and Defend"}, "subtitle": {"default": "Merge and build your team to defeat your enemies for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/carsmerge_icon.jpg", "applicationId": "com.gimica.carsmerge", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Cars Merge and Defend"}, "infoTextInstallBottom": {"default": "Merge and build your team to defeat your enemies for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.carsmerge"}, {"id": 200054, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Block Hole Clash"}, "subtitle": {"default": "Remove the correct blocks for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/blockhole_icon.jpg", "applicationId": "com.gimica.blockholeclash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Block Hole Clash"}, "infoTextInstallBottom": {"default": "Remove the correct blocks for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.blockholeclash"}, {"id": 200055, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Idle Merge <PERSON>"}, "subtitle": {"default": "Merge your way to improved production and loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/idlemerge_icon.jpg", "applicationId": "com.gimica.idlemergefun", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Idle Merge Fun"}, "infoTextInstallBottom": {"default": "Merge your way to improved production and loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.idlemergefun"}, {"id": 200056, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Brick Slider"}, "subtitle": {"default": "Survive the wall by making complete rows and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/block_slider_icon.jpg", "applicationId": "com.gimica.blockslider", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON> Slider"}, "infoTextInstallBottom": {"default": "Survive the wall by making complete rows and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.blockslider"}, {"id": 200060, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON><PERSON>"}, "subtitle": {"default": "Earn loyalty coins and relax with a classic"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/sudoku_icon.jpg", "applicationId": "com.gimica.sudoku", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Sudoku"}, "infoTextInstallBottom": {"default": "Earn loyalty coins and relax with a classic"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.sudoku"}, {"id": 200064, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Make Ten"}, "subtitle": {"default": "Make ten for loyalty coins!"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/make_ten_icon.jpg", "applicationId": "com.gimica.maketen", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Make Ten"}, "infoTextInstallBottom": {"default": "Make ten for loyalty coins!"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.maketen"}, {"id": 200046, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Word Seeker"}, "subtitle": {"default": "Solve word puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/word_seeker_icon.jpg", "applicationId": "com.gimica.wordseeker", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Word Seeker"}, "infoTextInstallBottom": {"default": "Solve word puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.wordseeker"}, {"id": 200043, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "2048 Puzzle Fun"}, "subtitle": {"default": "Reach 2048 to get more loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/hexa_puzzle_fun.jpg", "applicationId": "com.gimica.hexapuzzlefun", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play 2048 Puzzle Fun"}, "infoTextInstallBottom": {"default": "Reach 2048 to get more loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.hexapuzzlefun"}, {"id": 200059, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Brickdoku"}, "subtitle": {"default": "Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/brickdoku_icon.jpg", "applicationId": "com.gimica.brickdoku", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Brickdoku"}, "infoTextInstallBottom": {"default": "Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.brickdoku"}, {"id": 200061, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Word Kitchen"}, "subtitle": {"default": "Take on the heat of the Word Kitchen and discover words for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/word_kitchen_icon.jpg", "applicationId": "com.gimica.wordkitchen", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Word Kitchen"}, "infoTextInstallBottom": {"default": "Take on the heat of the Word Kitchen and discover words for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.wordkitchen"}, {"id": 200063, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Color Logic"}, "subtitle": {"default": "Test your logic for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/color_logic_icon.jpg", "applicationId": "com.gimica.colorlogic", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Color Logic"}, "infoTextInstallBottom": {"default": "Test your logic for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.colorlogic"}, {"id": 200067, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Spiral Drop"}, "subtitle": {"default": "Survive the drop for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/spiral_drop_icon.jpg", "applicationId": "com.gimica.helixdash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Spiral Drop"}, "infoTextInstallBottom": {"default": "Survive the drop for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.helixdash"}, {"id": 200058, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Mayan <PERSON>"}, "subtitle": {"default": "Survive Aztec trials for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/marble_madness_icon.jpg", "applicationId": "com.gimica.marblemadness", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON>n <PERSON>"}, "infoTextInstallBottom": {"default": "Survive Aztec trials for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.marblemadness"}, {"id": 200069, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Space Connect"}, "subtitle": {"default": "Connect for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/space_connect_icon.jpg", "applicationId": "com.gimica.spaceconnect", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Space Connect"}, "infoTextInstallBottom": {"default": "Connect for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.spaceconnect"}, {"id": 200050, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Hex Match"}, "subtitle": {"default": "Solve hexagonal puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/hex_match-icon.jpg", "applicationId": "com.gimica.hexmatch", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Hex Match"}, "infoTextInstallBottom": {"default": "Solve hexagonal puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.hexmatch"}, {"id": 200062, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Water Sorter"}, "subtitle": {"default": "Sort all the colors for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/water_sorter_icon.jpg", "applicationId": "com.gimica.watersorter", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Water Sorter"}, "infoTextInstallBottom": {"default": "Sort all the colors for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.watersorter"}, {"id": 200068, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Tile Match Pro"}, "subtitle": {"default": "Clear tiles from the board for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/tile_match_pro_icon.jpg", "applicationId": "com.gimica.tilematchpro", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Tile Match Pro"}, "infoTextInstallBottom": {"default": "Clear tiles from the board for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.tilematchpro"}, {"id": 200072, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Tangram Heaven"}, "subtitle": {"default": "Complete Tangram puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/tangram_heaven_icon.jpg", "applicationId": "com.gimica.tangram", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Tangram Heaven"}, "infoTextInstallBottom": {"default": "Complete Tangram puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.tangram"}]}, {"market": "latam", "statusMessage": {"default": "Service is temporarily unavailable", "en-US": "Service is temporarily unavailable"}, "playGamesTitle": {"default": "You still can play our awesome games. All our scientists are already working to resolve the issue", "en-US": "You still can play our awesome games. All our scientists are already working to resolve the issue"}, "items": [{"id": 200039, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Treasure Master 🤸‍♂️"}, "subtitle": {"default": "Defeat monsters for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/treasure_master.png", "applicationId": "com.gimica.treasuremaster", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Treasure Master"}, "infoTextInstallBottom": {"default": "Defeat monsters for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.treasuremaster"}, {"id": 200044, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Solitaire Verse"}, "subtitle": {"default": "The more games you win the more loyalty coins you get"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/solitaire_verse.jpg", "applicationId": "com.gimica.solitaireverse", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Solitaire Verse"}, "infoTextInstallBottom": {"default": "The more games you win the more loyalty coins you get"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.solitaireverse"}, {"id": 200052, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Sugar Rush Adventure"}, "subtitle": {"default": "Match 3 to make sweet treats for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/sugar_rush_icon.jpg", "applicationId": "com.gimica.sugarmatch", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Sugar Rush Adventure"}, "infoTextInstallBottom": {"default": "Match 3 to make sweet treats for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.sugarmatch"}, {"id": 200048, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON>"}, "subtitle": {"default": "Have a blast and merge blocks to survive as long as you can for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/merge_blast_icon.jpg", "applicationId": "com.gimica.mergeblast", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Merge Blast"}, "infoTextInstallBottom": {"default": "Have a blast and merge blocks to survive as long as you can for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mergeblast"}, {"id": 200042, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON>"}, "subtitle": {"default": "Destroy the blocks and collect pick ups for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/ball_bounce.jpg", "applicationId": "com.gimica.ballbounce", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Ball Bounce"}, "infoTextInstallBottom": {"default": "Destroy the blocks and collect pick ups for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.ballbounce"}, {"id": 200051, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON>zle <PERSON>"}, "subtitle": {"default": "Enter a state of bliss and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/wooden_puzzle_bliss_icon.jpg", "applicationId": "com.gimica.zentiles", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON><PERSON> Puzzle Bliss"}, "infoTextInstallBottom": {"default": "Enter a state of bliss and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.zentiles"}, {"id": 200041, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON>"}, "subtitle": {"default": "Smash through level after level for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/mad_smash.jpg", "applicationId": "com.gimica.madsmash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Mad Smash"}, "infoTextInstallBottom": {"default": "Smash through level after level for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.madsmash"}, {"id": 200045, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Puzzle Pop Blaster"}, "subtitle": {"default": "Match colored blocks to make loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/puzzle_pop_blaster.jpg", "applicationId": "com.gimica.puzzlepopblaster", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Puzzle Pop Blaster"}, "infoTextInstallBottom": {"default": "Match colored blocks to make loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.puzzlepopblaster"}, {"id": 200057, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Bubble Pop: Wild Rescue"}, "subtitle": {"default": "Solve puzzles and rescue the animals for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/bubble_pop_icon.jpg", "applicationId": "com.gimica.bubblepop", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Bubble Pop: Wild Rescue"}, "infoTextInstallBottom": {"default": "Solve puzzles and rescue the animals for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.bubblepop"}, {"id": 200049, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Emoji <PERSON>lickers"}, "subtitle": {"default": "Tap and bounce your way to more loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/emoji_clickers_icon.jpg", "applicationId": "com.gimica.emojiclickers", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Emoji <PERSON>"}, "infoTextInstallBottom": {"default": "Tap and bounce your way to more loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.emojiclickers"}, {"id": 200040, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Mix Blox"}, "subtitle": {"default": "Make number matches for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/mix_blox_icon.jpg", "applicationId": "com.gimica.mixblox", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Mix Blox"}, "infoTextInstallBottom": {"default": "Make number matches for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mixblox"}, {"id": 200047, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Trivia Madness"}, "subtitle": {"default": "Put your general knowledge to the test and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/trivia_madness_icon.jpg", "applicationId": "com.gimica.triviamadness", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Trivia Madness"}, "infoTextInstallBottom": {"default": "Put your general knowledge to the test and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.triviamadness"}, {"id": 200065, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Crystal Crush"}, "subtitle": {"default": "Mine precious gems and earn yourself some loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/crystal_crush_icon.jpg", "applicationId": "com.gimica.crystalcrush", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Crystal Crush"}, "infoTextInstallBottom": {"default": "Mine precious gems and earn yourself some loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.crystalcrush"}, {"id": 200053, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Cars Merge and Defend"}, "subtitle": {"default": "Merge and build your team to defeat your enemies for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/carsmerge_icon.jpg", "applicationId": "com.gimica.carsmerge", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Cars Merge and Defend"}, "infoTextInstallBottom": {"default": "Merge and build your team to defeat your enemies for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.carsmerge"}, {"id": 200054, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Block Hole Clash"}, "subtitle": {"default": "Remove the correct blocks for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/blockhole_icon.jpg", "applicationId": "com.gimica.blockholeclash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Block Hole Clash"}, "infoTextInstallBottom": {"default": "Remove the correct blocks for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.blockholeclash"}, {"id": 200055, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Idle Merge <PERSON>"}, "subtitle": {"default": "Merge your way to improved production and loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/idlemerge_icon.jpg", "applicationId": "com.gimica.idlemergefun", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Idle Merge Fun"}, "infoTextInstallBottom": {"default": "Merge your way to improved production and loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.idlemergefun"}, {"id": 200056, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Brick Slider"}, "subtitle": {"default": "Survive the wall by making complete rows and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/block_slider_icon.jpg", "applicationId": "com.gimica.blockslider", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON> Slider"}, "infoTextInstallBottom": {"default": "Survive the wall by making complete rows and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.blockslider"}, {"id": 200060, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON><PERSON>"}, "subtitle": {"default": "Earn loyalty coins and relax with a classic"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/sudoku_icon.jpg", "applicationId": "com.gimica.sudoku", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Sudoku"}, "infoTextInstallBottom": {"default": "Earn loyalty coins and relax with a classic"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.sudoku"}, {"id": 200064, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Make Ten"}, "subtitle": {"default": "Make ten for loyalty coins!"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/make_ten_icon.jpg", "applicationId": "com.gimica.maketen", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Make Ten"}, "infoTextInstallBottom": {"default": "Make ten for loyalty coins!"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.maketen"}, {"id": 200046, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Word Seeker"}, "subtitle": {"default": "Solve word puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/word_seeker_icon.jpg", "applicationId": "com.gimica.wordseeker", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Word Seeker"}, "infoTextInstallBottom": {"default": "Solve word puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.wordseeker"}, {"id": 200043, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "2048 Puzzle Fun"}, "subtitle": {"default": "Reach 2048 to get more loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/hexa_puzzle_fun.jpg", "applicationId": "com.gimica.hexapuzzlefun", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play 2048 Puzzle Fun"}, "infoTextInstallBottom": {"default": "Reach 2048 to get more loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.hexapuzzlefun"}, {"id": 200059, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Brickdoku"}, "subtitle": {"default": "Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/brickdoku_icon.jpg", "applicationId": "com.gimica.brickdoku", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Brickdoku"}, "infoTextInstallBottom": {"default": "Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.brickdoku"}, {"id": 200061, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Word Kitchen"}, "subtitle": {"default": "Take on the heat of the Word Kitchen and discover words for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/word_kitchen_icon.jpg", "applicationId": "com.gimica.wordkitchen", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Word Kitchen"}, "infoTextInstallBottom": {"default": "Take on the heat of the Word Kitchen and discover words for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.wordkitchen"}, {"id": 200063, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Color Logic"}, "subtitle": {"default": "Test your logic for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/color_logic_icon.jpg", "applicationId": "com.gimica.colorlogic", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Color Logic"}, "infoTextInstallBottom": {"default": "Test your logic for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.colorlogic"}, {"id": 200067, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Spiral Drop"}, "subtitle": {"default": "Survive the drop for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/spiral_drop_icon.jpg", "applicationId": "com.gimica.helixdash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Spiral Drop"}, "infoTextInstallBottom": {"default": "Survive the drop for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.helixdash"}, {"id": 200058, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Mayan <PERSON>"}, "subtitle": {"default": "Survive Aztec trials for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/marble_madness_icon.jpg", "applicationId": "com.gimica.marblemadness", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON>n <PERSON>"}, "infoTextInstallBottom": {"default": "Survive Aztec trials for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.marblemadness"}, {"id": 200069, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Space Connect"}, "subtitle": {"default": "Connect for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/space_connect_icon.jpg", "applicationId": "com.gimica.spaceconnect", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Space Connect"}, "infoTextInstallBottom": {"default": "Connect for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.spaceconnect"}, {"id": 200050, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Hex Match"}, "subtitle": {"default": "Solve hexagonal puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/hex_match-icon.jpg", "applicationId": "com.gimica.hexmatch", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Hex Match"}, "infoTextInstallBottom": {"default": "Solve hexagonal puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.hexmatch"}, {"id": 200062, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Water Sorter"}, "subtitle": {"default": "Sort all the colors for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/water_sorter_icon.jpg", "applicationId": "com.gimica.watersorter", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Water Sorter"}, "infoTextInstallBottom": {"default": "Sort all the colors for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.watersorter"}, {"id": 200068, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Tile Match Pro"}, "subtitle": {"default": "Clear tiles from the board for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/tile_match_pro_icon.jpg", "applicationId": "com.gimica.tilematchpro", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Tile Match Pro"}, "infoTextInstallBottom": {"default": "Clear tiles from the board for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.tilematchpro"}, {"id": 200072, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Tangram Heaven"}, "subtitle": {"default": "Complete Tangram puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/tangram_heaven_icon.jpg", "applicationId": "com.gimica.tangram", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Tangram Heaven"}, "infoTextInstallBottom": {"default": "Complete Tangram puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.tangram"}]}, {"market": "latam-test", "statusMessage": {"default": "Service is temporarily unavailable", "en-US": "Service is temporarily unavailable"}, "playGamesTitle": {"default": "You still can play our awesome games. All our scientists are already working to resolve the issue", "en-US": "You still can play our awesome games. All our scientists are already working to resolve the issue"}, "items": [{"id": 200039, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Treasure Master 🤸‍♂️"}, "subtitle": {"default": "Defeat monsters for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/treasure_master.png", "applicationId": "com.gimica.treasuremaster", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Treasure Master"}, "infoTextInstallBottom": {"default": "Defeat monsters for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.treasuremaster"}, {"id": 200044, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Solitaire Verse"}, "subtitle": {"default": "The more games you win the more loyalty coins you get"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/solitaire_verse.jpg", "applicationId": "com.gimica.solitaireverse", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Solitaire Verse"}, "infoTextInstallBottom": {"default": "The more games you win the more loyalty coins you get"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.solitaireverse"}, {"id": 200052, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Sugar Rush Adventure"}, "subtitle": {"default": "Match 3 to make sweet treats for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/sugar_rush_icon.jpg", "applicationId": "com.gimica.sugarmatch", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Sugar Rush Adventure"}, "infoTextInstallBottom": {"default": "Match 3 to make sweet treats for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.sugarmatch"}, {"id": 200048, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON>"}, "subtitle": {"default": "Have a blast and merge blocks to survive as long as you can for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/merge_blast_icon.jpg", "applicationId": "com.gimica.mergeblast", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Merge Blast"}, "infoTextInstallBottom": {"default": "Have a blast and merge blocks to survive as long as you can for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mergeblast"}, {"id": 200042, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON>"}, "subtitle": {"default": "Destroy the blocks and collect pick ups for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/ball_bounce.jpg", "applicationId": "com.gimica.ballbounce", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Ball Bounce"}, "infoTextInstallBottom": {"default": "Destroy the blocks and collect pick ups for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.ballbounce"}, {"id": 200051, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON>zle <PERSON>"}, "subtitle": {"default": "Enter a state of bliss and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/wooden_puzzle_bliss_icon.jpg", "applicationId": "com.gimica.zentiles", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON><PERSON> Puzzle Bliss"}, "infoTextInstallBottom": {"default": "Enter a state of bliss and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.zentiles"}, {"id": 200041, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON>"}, "subtitle": {"default": "Smash through level after level for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/mad_smash.jpg", "applicationId": "com.gimica.madsmash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Mad Smash"}, "infoTextInstallBottom": {"default": "Smash through level after level for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.madsmash"}, {"id": 200045, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Puzzle Pop Blaster"}, "subtitle": {"default": "Match colored blocks to make loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/puzzle_pop_blaster.jpg", "applicationId": "com.gimica.puzzlepopblaster", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Puzzle Pop Blaster"}, "infoTextInstallBottom": {"default": "Match colored blocks to make loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.puzzlepopblaster"}, {"id": 200057, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Bubble Pop: Wild Rescue"}, "subtitle": {"default": "Solve puzzles and rescue the animals for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/bubble_pop_icon.jpg", "applicationId": "com.gimica.bubblepop", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Bubble Pop: Wild Rescue"}, "infoTextInstallBottom": {"default": "Solve puzzles and rescue the animals for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.bubblepop"}, {"id": 200049, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Emoji <PERSON>lickers"}, "subtitle": {"default": "Tap and bounce your way to more loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/emoji_clickers_icon.jpg", "applicationId": "com.gimica.emojiclickers", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Emoji <PERSON>"}, "infoTextInstallBottom": {"default": "Tap and bounce your way to more loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.emojiclickers"}, {"id": 200040, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Mix Blox"}, "subtitle": {"default": "Make number matches for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/mix_blox_icon.jpg", "applicationId": "com.gimica.mixblox", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Mix Blox"}, "infoTextInstallBottom": {"default": "Make number matches for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mixblox"}, {"id": 200047, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Trivia Madness"}, "subtitle": {"default": "Put your general knowledge to the test and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/trivia_madness_icon.jpg", "applicationId": "com.gimica.triviamadness", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Trivia Madness"}, "infoTextInstallBottom": {"default": "Put your general knowledge to the test and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.triviamadness"}, {"id": 200065, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Crystal Crush"}, "subtitle": {"default": "Mine precious gems and earn yourself some loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/crystal_crush_icon.jpg", "applicationId": "com.gimica.crystalcrush", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Crystal Crush"}, "infoTextInstallBottom": {"default": "Mine precious gems and earn yourself some loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.crystalcrush"}, {"id": 200053, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Cars Merge and Defend"}, "subtitle": {"default": "Merge and build your team to defeat your enemies for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/carsmerge_icon.jpg", "applicationId": "com.gimica.carsmerge", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Cars Merge and Defend"}, "infoTextInstallBottom": {"default": "Merge and build your team to defeat your enemies for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.carsmerge"}, {"id": 200054, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Block Hole Clash"}, "subtitle": {"default": "Remove the correct blocks for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/blockhole_icon.jpg", "applicationId": "com.gimica.blockholeclash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Block Hole Clash"}, "infoTextInstallBottom": {"default": "Remove the correct blocks for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.blockholeclash"}, {"id": 200055, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Idle Merge <PERSON>"}, "subtitle": {"default": "Merge your way to improved production and loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/idlemerge_icon.jpg", "applicationId": "com.gimica.idlemergefun", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Idle Merge Fun"}, "infoTextInstallBottom": {"default": "Merge your way to improved production and loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.idlemergefun"}, {"id": 200056, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Brick Slider"}, "subtitle": {"default": "Survive the wall by making complete rows and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/block_slider_icon.jpg", "applicationId": "com.gimica.blockslider", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON> Slider"}, "infoTextInstallBottom": {"default": "Survive the wall by making complete rows and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.blockslider"}, {"id": 200060, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON><PERSON>"}, "subtitle": {"default": "Earn loyalty coins and relax with a classic"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/sudoku_icon.jpg", "applicationId": "com.gimica.sudoku", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Sudoku"}, "infoTextInstallBottom": {"default": "Earn loyalty coins and relax with a classic"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.sudoku"}, {"id": 200064, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Make Ten"}, "subtitle": {"default": "Make ten for loyalty coins!"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/make_ten_icon.jpg", "applicationId": "com.gimica.maketen", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Make Ten"}, "infoTextInstallBottom": {"default": "Make ten for loyalty coins!"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.maketen"}, {"id": 200046, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Word Seeker"}, "subtitle": {"default": "Solve word puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/word_seeker_icon.jpg", "applicationId": "com.gimica.wordseeker", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Word Seeker"}, "infoTextInstallBottom": {"default": "Solve word puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.wordseeker"}, {"id": 200043, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "2048 Puzzle Fun"}, "subtitle": {"default": "Reach 2048 to get more loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/hexa_puzzle_fun.jpg", "applicationId": "com.gimica.hexapuzzlefun", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play 2048 Puzzle Fun"}, "infoTextInstallBottom": {"default": "Reach 2048 to get more loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.hexapuzzlefun"}, {"id": 200059, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Brickdoku"}, "subtitle": {"default": "Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/brickdoku_icon.jpg", "applicationId": "com.gimica.brickdoku", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Brickdoku"}, "infoTextInstallBottom": {"default": "Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.brickdoku"}, {"id": 200061, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Word Kitchen"}, "subtitle": {"default": "Take on the heat of the Word Kitchen and discover words for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/word_kitchen_icon.jpg", "applicationId": "com.gimica.wordkitchen", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Word Kitchen"}, "infoTextInstallBottom": {"default": "Take on the heat of the Word Kitchen and discover words for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.wordkitchen"}, {"id": 200063, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Color Logic"}, "subtitle": {"default": "Test your logic for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/color_logic_icon.jpg", "applicationId": "com.gimica.colorlogic", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Color Logic"}, "infoTextInstallBottom": {"default": "Test your logic for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.colorlogic"}, {"id": 200067, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Spiral Drop"}, "subtitle": {"default": "Survive the drop for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/spiral_drop_icon.jpg", "applicationId": "com.gimica.helixdash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Spiral Drop"}, "infoTextInstallBottom": {"default": "Survive the drop for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.helixdash"}, {"id": 200058, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Mayan <PERSON>"}, "subtitle": {"default": "Survive Aztec trials for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/marble_madness_icon.jpg", "applicationId": "com.gimica.marblemadness", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON>n <PERSON>"}, "infoTextInstallBottom": {"default": "Survive Aztec trials for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.marblemadness"}, {"id": 200069, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Space Connect"}, "subtitle": {"default": "Connect for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/space_connect_icon.jpg", "applicationId": "com.gimica.spaceconnect", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Space Connect"}, "infoTextInstallBottom": {"default": "Connect for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.spaceconnect"}, {"id": 200050, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Hex Match"}, "subtitle": {"default": "Solve hexagonal puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/hex_match-icon.jpg", "applicationId": "com.gimica.hexmatch", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Hex Match"}, "infoTextInstallBottom": {"default": "Solve hexagonal puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.hexmatch"}, {"id": 200062, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Water Sorter"}, "subtitle": {"default": "Sort all the colors for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/water_sorter_icon.jpg", "applicationId": "com.gimica.watersorter", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Water Sorter"}, "infoTextInstallBottom": {"default": "Sort all the colors for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.watersorter"}, {"id": 200068, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Tile Match Pro"}, "subtitle": {"default": "Clear tiles from the board for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/tile_match_pro_icon.jpg", "applicationId": "com.gimica.tilematchpro", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Tile Match Pro"}, "infoTextInstallBottom": {"default": "Clear tiles from the board for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.tilematchpro"}, {"id": 200072, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Tangram Heaven"}, "subtitle": {"default": "Complete Tangram puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/tangram_heaven_icon.jpg", "applicationId": "com.gimica.tangram", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Tangram Heaven"}, "infoTextInstallBottom": {"default": "Complete Tangram puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.tangram"}]}, {"market": "ios-us", "statusMessage": {"default": "Service is temporarily unavailable", "en-US": "Service is temporarily unavailable"}, "playGamesTitle": {"default": "You still can play our awesome games. All our scientists are already working to resolve the issue", "en-US": "You still can play our awesome games. All our scientists are already working to resolve the issue"}, "items": [{"id": 200039, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Treasure Master 🤸‍♂️"}, "subtitle": {"default": "Defeat monsters for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/treasure_master.png", "applicationId": "com.gimica.treasuremaster", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Treasure Master"}, "infoTextInstallBottom": {"default": "Defeat monsters for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.treasuremaster"}, {"id": 200044, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Solitaire Verse"}, "subtitle": {"default": "The more games you win the more loyalty coins you get"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/solitaire_verse.jpg", "applicationId": "com.gimica.solitaireverse", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Solitaire Verse"}, "infoTextInstallBottom": {"default": "The more games you win the more loyalty coins you get"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.solitaireverse"}, {"id": 200052, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Sugar Rush Adventure"}, "subtitle": {"default": "Match 3 to make sweet treats for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/sugar_rush_icon.jpg", "applicationId": "com.gimica.sugarmatch", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Sugar Rush Adventure"}, "infoTextInstallBottom": {"default": "Match 3 to make sweet treats for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.sugarmatch"}, {"id": 200048, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON>"}, "subtitle": {"default": "Have a blast and merge blocks to survive as long as you can for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/merge_blast_icon.jpg", "applicationId": "com.gimica.mergeblast", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Merge Blast"}, "infoTextInstallBottom": {"default": "Have a blast and merge blocks to survive as long as you can for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mergeblast"}, {"id": 200042, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON>"}, "subtitle": {"default": "Destroy the blocks and collect pick ups for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/ball_bounce.jpg", "applicationId": "com.gimica.ballbounce", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Ball Bounce"}, "infoTextInstallBottom": {"default": "Destroy the blocks and collect pick ups for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.ballbounce"}, {"id": 200051, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON>zle <PERSON>"}, "subtitle": {"default": "Enter a state of bliss and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/wooden_puzzle_bliss_icon.jpg", "applicationId": "com.gimica.zentiles", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON><PERSON> Puzzle Bliss"}, "infoTextInstallBottom": {"default": "Enter a state of bliss and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.zentiles"}, {"id": 200041, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON>"}, "subtitle": {"default": "Smash through level after level for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/mad_smash.jpg", "applicationId": "com.gimica.madsmash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Mad Smash"}, "infoTextInstallBottom": {"default": "Smash through level after level for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.madsmash"}, {"id": 200045, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Puzzle Pop Blaster"}, "subtitle": {"default": "Match colored blocks to make loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/puzzle_pop_blaster.jpg", "applicationId": "com.gimica.puzzlepopblaster", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Puzzle Pop Blaster"}, "infoTextInstallBottom": {"default": "Match colored blocks to make loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.puzzlepopblaster"}, {"id": 200057, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Bubble Pop: Wild Rescue"}, "subtitle": {"default": "Solve puzzles and rescue the animals for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/bubble_pop_icon.jpg", "applicationId": "com.gimica.bubblepop", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Bubble Pop: Wild Rescue"}, "infoTextInstallBottom": {"default": "Solve puzzles and rescue the animals for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.bubblepop"}, {"id": 200049, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Emoji <PERSON>lickers"}, "subtitle": {"default": "Tap and bounce your way to more loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/emoji_clickers_icon.jpg", "applicationId": "com.gimica.emojiclickers", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Emoji <PERSON>"}, "infoTextInstallBottom": {"default": "Tap and bounce your way to more loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.emojiclickers"}, {"id": 200040, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Mix Blox"}, "subtitle": {"default": "Make number matches for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/mix_blox_icon.jpg", "applicationId": "com.gimica.mixblox", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Mix Blox"}, "infoTextInstallBottom": {"default": "Make number matches for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mixblox"}, {"id": 200047, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Trivia Madness"}, "subtitle": {"default": "Put your general knowledge to the test and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/trivia_madness_icon.jpg", "applicationId": "com.gimica.triviamadness", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Trivia Madness"}, "infoTextInstallBottom": {"default": "Put your general knowledge to the test and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.triviamadness"}, {"id": 200065, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Crystal Crush"}, "subtitle": {"default": "Mine precious gems and earn yourself some loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/crystal_crush_icon.jpg", "applicationId": "com.gimica.crystalcrush", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Crystal Crush"}, "infoTextInstallBottom": {"default": "Mine precious gems and earn yourself some loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.crystalcrush"}, {"id": 200053, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Cars Merge and Defend"}, "subtitle": {"default": "Merge and build your team to defeat your enemies for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/carsmerge_icon.jpg", "applicationId": "com.gimica.carsmerge", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Cars Merge and Defend"}, "infoTextInstallBottom": {"default": "Merge and build your team to defeat your enemies for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.carsmerge"}, {"id": 200054, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Block Hole Clash"}, "subtitle": {"default": "Remove the correct blocks for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/blockhole_icon.jpg", "applicationId": "com.gimica.blockholeclash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Block Hole Clash"}, "infoTextInstallBottom": {"default": "Remove the correct blocks for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.blockholeclash"}, {"id": 200055, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Idle Merge <PERSON>"}, "subtitle": {"default": "Merge your way to improved production and loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/idlemerge_icon.jpg", "applicationId": "com.gimica.idlemergefun", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Idle Merge Fun"}, "infoTextInstallBottom": {"default": "Merge your way to improved production and loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.idlemergefun"}, {"id": 200056, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Brick Slider"}, "subtitle": {"default": "Survive the wall by making complete rows and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/block_slider_icon.jpg", "applicationId": "com.gimica.blockslider", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON> Slider"}, "infoTextInstallBottom": {"default": "Survive the wall by making complete rows and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.blockslider"}, {"id": 200060, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON><PERSON>"}, "subtitle": {"default": "Earn loyalty coins and relax with a classic"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/sudoku_icon.jpg", "applicationId": "com.gimica.sudoku", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Sudoku"}, "infoTextInstallBottom": {"default": "Earn loyalty coins and relax with a classic"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.sudoku"}, {"id": 200064, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Make Ten"}, "subtitle": {"default": "Make ten for loyalty coins!"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/make_ten_icon.jpg", "applicationId": "com.gimica.maketen", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Make Ten"}, "infoTextInstallBottom": {"default": "Make ten for loyalty coins!"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.maketen"}, {"id": 200046, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Word Seeker"}, "subtitle": {"default": "Solve word puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/word_seeker_icon.jpg", "applicationId": "com.gimica.wordseeker", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Word Seeker"}, "infoTextInstallBottom": {"default": "Solve word puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.wordseeker"}, {"id": 200043, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "2048 Puzzle Fun"}, "subtitle": {"default": "Reach 2048 to get more loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/hexa_puzzle_fun.jpg", "applicationId": "com.gimica.hexapuzzlefun", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play 2048 Puzzle Fun"}, "infoTextInstallBottom": {"default": "Reach 2048 to get more loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.hexapuzzlefun"}, {"id": 200059, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Brickdoku"}, "subtitle": {"default": "Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/brickdoku_icon.jpg", "applicationId": "com.gimica.brickdoku", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Brickdoku"}, "infoTextInstallBottom": {"default": "Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.brickdoku"}, {"id": 200061, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Word Kitchen"}, "subtitle": {"default": "Take on the heat of the Word Kitchen and discover words for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/word_kitchen_icon.jpg", "applicationId": "com.gimica.wordkitchen", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Word Kitchen"}, "infoTextInstallBottom": {"default": "Take on the heat of the Word Kitchen and discover words for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.wordkitchen"}, {"id": 200063, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Color Logic"}, "subtitle": {"default": "Test your logic for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/color_logic_icon.jpg", "applicationId": "com.gimica.colorlogic", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Color Logic"}, "infoTextInstallBottom": {"default": "Test your logic for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.colorlogic"}, {"id": 200067, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Spiral Drop"}, "subtitle": {"default": "Survive the drop for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/spiral_drop_icon.jpg", "applicationId": "com.gimica.helixdash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Spiral Drop"}, "infoTextInstallBottom": {"default": "Survive the drop for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.helixdash"}, {"id": 200058, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Mayan <PERSON>"}, "subtitle": {"default": "Survive Aztec trials for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/marble_madness_icon.jpg", "applicationId": "com.gimica.marblemadness", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON>n <PERSON>"}, "infoTextInstallBottom": {"default": "Survive Aztec trials for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.marblemadness"}, {"id": 200069, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Space Connect"}, "subtitle": {"default": "Connect for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/space_connect_icon.jpg", "applicationId": "com.gimica.spaceconnect", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Space Connect"}, "infoTextInstallBottom": {"default": "Connect for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.spaceconnect"}, {"id": 200050, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Hex Match"}, "subtitle": {"default": "Solve hexagonal puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/hex_match-icon.jpg", "applicationId": "com.gimica.hexmatch", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Hex Match"}, "infoTextInstallBottom": {"default": "Solve hexagonal puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.hexmatch"}, {"id": 200062, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Water Sorter"}, "subtitle": {"default": "Sort all the colors for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/water_sorter_icon.jpg", "applicationId": "com.gimica.watersorter", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Water Sorter"}, "infoTextInstallBottom": {"default": "Sort all the colors for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.watersorter"}, {"id": 200068, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Tile Match Pro"}, "subtitle": {"default": "Clear tiles from the board for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/tile_match_pro_icon.jpg", "applicationId": "com.gimica.tilematchpro", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Tile Match Pro"}, "infoTextInstallBottom": {"default": "Clear tiles from the board for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.tilematchpro"}, {"id": 200072, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Tangram Heaven"}, "subtitle": {"default": "Complete Tangram puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/tangram_heaven_icon.jpg", "applicationId": "com.gimica.tangram", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Tangram Heaven"}, "infoTextInstallBottom": {"default": "Complete Tangram puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.tangram"}]}, {"market": "ios-us-test", "statusMessage": {"default": "Service is temporarily unavailable", "en-US": "Service is temporarily unavailable"}, "playGamesTitle": {"default": "You still can play our awesome games. All our scientists are already working to resolve the issue", "en-US": "You still can play our awesome games. All our scientists are already working to resolve the issue"}, "items": [{"id": 200039, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Treasure Master 🤸‍♂️"}, "subtitle": {"default": "Defeat monsters for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/treasure_master.png", "applicationId": "com.gimica.treasuremaster", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Treasure Master"}, "infoTextInstallBottom": {"default": "Defeat monsters for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.treasuremaster"}, {"id": 200044, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Solitaire Verse"}, "subtitle": {"default": "The more games you win the more loyalty coins you get"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/solitaire_verse.jpg", "applicationId": "com.gimica.solitaireverse", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Solitaire Verse"}, "infoTextInstallBottom": {"default": "The more games you win the more loyalty coins you get"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.solitaireverse"}, {"id": 200052, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Sugar Rush Adventure"}, "subtitle": {"default": "Match 3 to make sweet treats for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/sugar_rush_icon.jpg", "applicationId": "com.gimica.sugarmatch", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Sugar Rush Adventure"}, "infoTextInstallBottom": {"default": "Match 3 to make sweet treats for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.sugarmatch"}, {"id": 200048, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON>"}, "subtitle": {"default": "Have a blast and merge blocks to survive as long as you can for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/merge_blast_icon.jpg", "applicationId": "com.gimica.mergeblast", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Merge Blast"}, "infoTextInstallBottom": {"default": "Have a blast and merge blocks to survive as long as you can for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mergeblast"}, {"id": 200042, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON>"}, "subtitle": {"default": "Destroy the blocks and collect pick ups for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/ball_bounce.jpg", "applicationId": "com.gimica.ballbounce", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Ball Bounce"}, "infoTextInstallBottom": {"default": "Destroy the blocks and collect pick ups for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.ballbounce"}, {"id": 200051, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON>zle <PERSON>"}, "subtitle": {"default": "Enter a state of bliss and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/wooden_puzzle_bliss_icon.jpg", "applicationId": "com.gimica.zentiles", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON><PERSON> Puzzle Bliss"}, "infoTextInstallBottom": {"default": "Enter a state of bliss and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.zentiles"}, {"id": 200041, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON>"}, "subtitle": {"default": "Smash through level after level for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/mad_smash.jpg", "applicationId": "com.gimica.madsmash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Mad Smash"}, "infoTextInstallBottom": {"default": "Smash through level after level for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.madsmash"}, {"id": 200045, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Puzzle Pop Blaster"}, "subtitle": {"default": "Match colored blocks to make loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/puzzle_pop_blaster.jpg", "applicationId": "com.gimica.puzzlepopblaster", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Puzzle Pop Blaster"}, "infoTextInstallBottom": {"default": "Match colored blocks to make loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.puzzlepopblaster"}, {"id": 200057, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Bubble Pop: Wild Rescue"}, "subtitle": {"default": "Solve puzzles and rescue the animals for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/bubble_pop_icon.jpg", "applicationId": "com.gimica.bubblepop", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Bubble Pop: Wild Rescue"}, "infoTextInstallBottom": {"default": "Solve puzzles and rescue the animals for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.bubblepop"}, {"id": 200049, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Emoji <PERSON>lickers"}, "subtitle": {"default": "Tap and bounce your way to more loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/emoji_clickers_icon.jpg", "applicationId": "com.gimica.emojiclickers", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Emoji <PERSON>"}, "infoTextInstallBottom": {"default": "Tap and bounce your way to more loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.emojiclickers"}, {"id": 200040, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Mix Blox"}, "subtitle": {"default": "Make number matches for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/mix_blox_icon.jpg", "applicationId": "com.gimica.mixblox", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Mix Blox"}, "infoTextInstallBottom": {"default": "Make number matches for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.mixblox"}, {"id": 200047, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Trivia Madness"}, "subtitle": {"default": "Put your general knowledge to the test and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/trivia_madness_icon.jpg", "applicationId": "com.gimica.triviamadness", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Trivia Madness"}, "infoTextInstallBottom": {"default": "Put your general knowledge to the test and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.triviamadness"}, {"id": 200065, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Crystal Crush"}, "subtitle": {"default": "Mine precious gems and earn yourself some loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/crystal_crush_icon.jpg", "applicationId": "com.gimica.crystalcrush", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Crystal Crush"}, "infoTextInstallBottom": {"default": "Mine precious gems and earn yourself some loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.crystalcrush"}, {"id": 200053, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Cars Merge and Defend"}, "subtitle": {"default": "Merge and build your team to defeat your enemies for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/carsmerge_icon.jpg", "applicationId": "com.gimica.carsmerge", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Cars Merge and Defend"}, "infoTextInstallBottom": {"default": "Merge and build your team to defeat your enemies for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.carsmerge"}, {"id": 200054, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Block Hole Clash"}, "subtitle": {"default": "Remove the correct blocks for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/blockhole_icon.jpg", "applicationId": "com.gimica.blockholeclash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Block Hole Clash"}, "infoTextInstallBottom": {"default": "Remove the correct blocks for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.blockholeclash"}, {"id": 200055, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Idle Merge <PERSON>"}, "subtitle": {"default": "Merge your way to improved production and loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/idlemerge_icon.jpg", "applicationId": "com.gimica.idlemergefun", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Idle Merge Fun"}, "infoTextInstallBottom": {"default": "Merge your way to improved production and loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.idlemergefun"}, {"id": 200056, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Brick Slider"}, "subtitle": {"default": "Survive the wall by making complete rows and earn loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/block_slider_icon.jpg", "applicationId": "com.gimica.blockslider", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON> Slider"}, "infoTextInstallBottom": {"default": "Survive the wall by making complete rows and earn loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.blockslider"}, {"id": 200060, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "<PERSON><PERSON><PERSON>"}, "subtitle": {"default": "Earn loyalty coins and relax with a classic"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/sudoku_icon.jpg", "applicationId": "com.gimica.sudoku", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Sudoku"}, "infoTextInstallBottom": {"default": "Earn loyalty coins and relax with a classic"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.sudoku"}, {"id": 200064, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Make Ten"}, "subtitle": {"default": "Make ten for loyalty coins!"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/make_ten_icon.jpg", "applicationId": "com.gimica.maketen", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Make Ten"}, "infoTextInstallBottom": {"default": "Make ten for loyalty coins!"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.maketen"}, {"id": 200046, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Word Seeker"}, "subtitle": {"default": "Solve word puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/word_seeker_icon.jpg", "applicationId": "com.gimica.wordseeker", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Word Seeker"}, "infoTextInstallBottom": {"default": "Solve word puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.wordseeker"}, {"id": 200043, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "2048 Puzzle Fun"}, "subtitle": {"default": "Reach 2048 to get more loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/hexa_puzzle_fun.jpg", "applicationId": "com.gimica.hexapuzzlefun", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play 2048 Puzzle Fun"}, "infoTextInstallBottom": {"default": "Reach 2048 to get more loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.hexapuzzlefun"}, {"id": 200059, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Brickdoku"}, "subtitle": {"default": "Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/brickdoku_icon.jpg", "applicationId": "com.gimica.brickdoku", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Brickdoku"}, "infoTextInstallBottom": {"default": "Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.brickdoku"}, {"id": 200061, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Word Kitchen"}, "subtitle": {"default": "Take on the heat of the Word Kitchen and discover words for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/word_kitchen_icon.jpg", "applicationId": "com.gimica.wordkitchen", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Word Kitchen"}, "infoTextInstallBottom": {"default": "Take on the heat of the Word Kitchen and discover words for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.wordkitchen"}, {"id": 200063, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Color Logic"}, "subtitle": {"default": "Test your logic for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/color_logic_icon.jpg", "applicationId": "com.gimica.colorlogic", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Color Logic"}, "infoTextInstallBottom": {"default": "Test your logic for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.colorlogic"}, {"id": 200067, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Spiral Drop"}, "subtitle": {"default": "Survive the drop for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/spiral_drop_icon.jpg", "applicationId": "com.gimica.helixdash", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Spiral Drop"}, "infoTextInstallBottom": {"default": "Survive the drop for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.helixdash"}, {"id": 200058, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Mayan <PERSON>"}, "subtitle": {"default": "Survive Aztec trials for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/marble_madness_icon.jpg", "applicationId": "com.gimica.marblemadness", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play <PERSON>n <PERSON>"}, "infoTextInstallBottom": {"default": "Survive Aztec trials for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.marblemadness"}, {"id": 200069, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Space Connect"}, "subtitle": {"default": "Connect for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/space_connect_icon.jpg", "applicationId": "com.gimica.spaceconnect", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Space Connect"}, "infoTextInstallBottom": {"default": "Connect for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.spaceconnect"}, {"id": 200050, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Hex Match"}, "subtitle": {"default": "Solve hexagonal puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/hex_match-icon.jpg", "applicationId": "com.gimica.hexmatch", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Hex Match"}, "infoTextInstallBottom": {"default": "Solve hexagonal puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.hexmatch"}, {"id": 200062, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Water Sorter"}, "subtitle": {"default": "Sort all the colors for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/water_sorter_icon.jpg", "applicationId": "com.gimica.watersorter", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Water Sorter"}, "infoTextInstallBottom": {"default": "Sort all the colors for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.watersorter"}, {"id": 200068, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Tile Match Pro"}, "subtitle": {"default": "Clear tiles from the board for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/tile_match_pro_icon.jpg", "applicationId": "com.gimica.tilematchpro", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Tile Match Pro"}, "infoTextInstallBottom": {"default": "Clear tiles from the board for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.tilematchpro"}, {"id": 200072, "activityName": "com.unity3d.player.UnityPlayerActivity", "title": {"default": "Tangram Heaven"}, "subtitle": {"default": "Complete Tangram puzzles for loyalty coins"}, "iconUrl": "https://storage.googleapis.com/public-playtime/images/tangram_heaven_icon.jpg", "applicationId": "com.gimica.tangram", "infoTextPlay": {"default": ""}, "showInstallImage": true, "installImageUrl": "https://storage.googleapis.com/public-playtime/images/install_image_20230504.jpg", "infoTextInstallTop": {"default": "Play Tangram Heaven"}, "infoTextInstallBottom": {"default": "Complete Tangram puzzles for loyalty coins"}, "installationLink": "https://play.google.com/store/apps/details?id=com.gimica.tangram"}]}]}