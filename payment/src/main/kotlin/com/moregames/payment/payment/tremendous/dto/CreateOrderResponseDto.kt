package com.moregames.payment.payment.tremendous.dto

import kotlinx.serialization.Contextual
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.time.Instant

// This object doesn't contain all possible options. For full docs see: https://developers.tremendous.com/reference/core-orders-create
@Serializable
data class CreateOrderResponseDto(
  val order: OrderDto
) {
  @Serializable
  data class OrderDto(
    val id: String,
    val status: String,
    @Contextual @SerialName("created_at") val createdAt: Instant,
    val payment: PaymentDto,
    val rewards: List<RewardDto>
  ) {

    @Serializable
    data class PaymentDto(
      val subtotal: Double, // Total price of the order before fees (in USD)
      val total: Double, // Total price of the order including fees (in USD)
      val fees: Double // Fees for the order (in USD)
    )

    @Serializable
    data class RewardDto(
      val id: String,
      val delivery: DeliveryDto
    ) {
      @Serializable
      data class DeliveryDto(
        val link: String? = null
      )
    }
  }
}