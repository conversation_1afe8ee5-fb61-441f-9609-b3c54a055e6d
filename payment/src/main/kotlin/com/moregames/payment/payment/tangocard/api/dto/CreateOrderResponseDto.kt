package com.moregames.payment.payment.tangocard.api.dto

import kotlinx.serialization.Serializable

@Serializable
data class CreateOrderResponseDto(
  val referenceOrderID: String,
  val externalRefID: String? = null,
  val customerIdentifier: String? = null,
  val accountIdentifier: String? = null,
  val amountCharged: AmountDto? = null,
  val denomination: AmountDto? = null,
  val utid: String? = null,
  val ptid: String? = null,
  val rewardName: String? = null,
  val reward: RewardDto? = null,
  val sender: SenderDto? = null,
  val recipient: RecipientDto? = null,
  val emailSubject: String? = null,
  val message: String? = null,
  val sendEmail: Boolean? = null,
  val status: String? = null,
  val campaign: String? = null,
  val purchaseOrderNumber: String? = null,
  val createdAt: String? = null,
  val redemptionInstructions: String? = null
) {

  @Serializable
  data class AmountDto(
    val value: Double? = null,
    val currencyCode: String? = null,
    val exchangeRate: Double? = null,
    val fee: Double? = null,
    val total: Double? = null
  )

  @Serializable
  data class RewardDto(
    val credentials: Map<String, String>? = null,
    val credentialList: List<CredentialDto>? = null,
    val redemptionInstructions: String? = null
  ) {

    @Serializable
    data class CredentialDto(
      val label: String? = null,
      val value: String? = null,
      val type: String? = null,
      val credentialType: String? = null
    )
  }

  @Serializable
  data class SenderDto(
    val firstName: String? = null,
    val lastName: String? = null,
    val email: String? = null
  )

  @Serializable
  data class RecipientDto(
    val email: String? = null,
    val firstName: String? = null,
    val lastName: String? = null,
    val address: AddressDto? = null
  ) {

    @Serializable
    data class AddressDto(
      val streetLine1: String? = null,
      val streetLine2: String? = null,
      val city: String? = null,
      val stateOrProvince: String? = null,
      val postalCode: String? = null,
      val country: String? = null
    )
  }
}
