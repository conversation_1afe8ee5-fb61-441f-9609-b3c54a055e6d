<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <include file="/database/database-init.xml"/>
    <changeSet id="payment-object" author="vladimir.maksimenko"
               labels="https://app.asana.com/0/****************/1200591060700821/f">
        <createTable schemaName="payment" tableName="payments">
            <column name="cashout_transaction_id" type="VARCHAR(36)">
                <constraints nullable="false"
                             primaryKey="true"/>
            </column>
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"/>
            </column>
            <column name="amount" type="DECIMAL(6,2)">
                <constraints nullable="false"/>
            </column>
            <column name="provider" type="VARCHAR(45)">
                <constraints nullable="false"/>
            </column>
            <column name="recipient_name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="recipient_email" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="VARCHAR(30)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createIndex schemaName="payment" tableName="payments" indexName="idx_payments__user_id">
            <column name="user_id"/>
        </createIndex>
        <createIndex schemaName="payment" tableName="payments" indexName="idx_payments__created_at">
            <column name="created_at"/>
        </createIndex>
        <sql>
            ALTER TABLE payment.payments MODIFY COLUMN updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
        </sql>
    </changeSet>

    <changeSet id="payment-claim-object" author="vladimir.maksimenko"
               labels="https://app.asana.com/0/****************/1200591060700821/f">
        <createTable schemaName="payment" tableName="payments_claim">
            <column name="cashout_transaction_id" type="VARCHAR(36)">
                <constraints nullable="false"
                             primaryKey="true"/>
            </column>
            <column name="payment_external_transaction_id" type="VARCHAR(50)"/>
            <column name="claim" type="VARCHAR(255)"/>
            <column name="redeem_instructions" type="TEXT"/>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <sql>
            ALTER TABLE payment.payments_claim MODIFY COLUMN updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
        </sql>
    </changeSet>

    <changeSet id="endpoint-for-payments-checking" author="aleksey.romantsov"
               labels="https://app.asana.com/0/****************/1201016436859289/f">
        <addColumn schemaName="payment" tableName="payments">
            <column name="fee" type="DECIMAL(6,2)" defaultValue="NULL"/>
        </addColumn>
    </changeSet>

    <changeSet id="multi-currency-payments-support" author="vladimir.maksimenko"
               labels="https://app.asana.com/0/****************/1201236502401541/f">
        <addColumn schemaName="payment" tableName="payments">
            <column name="currency_code" type="VARCHAR(3)">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addColumn schemaName="payment" tableName="payments">
            <column name="country_code" type="VARCHAR(2)">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="propagate-claim-label-from-tango" author="alex.potolitcyn"
               labels="https://app.asana.com/0/****************/1201436998213156/f">
        <addColumn schemaName="payment" tableName="payments_claim">
            <column name="claim_label" type="varchar(100)" defaultValue="NULL"/>
        </addColumn>
    </changeSet>

    <changeSet id="store-withhold" author="vladimir.maksimenko"
               labels="https://app.asana.com/0/****************/1201664061323397/f">
        <addColumn schemaName="payment" tableName="payments">
            <column name="operational_withhold_amount" type="DECIMAL(6,2)"/>
        </addColumn>
    </changeSet>

    <changeSet id="store-paypal-transactions-data" author="aleksey.romantsov"
               labels="https://app.asana.com/0/****************/****************/f">
        <createTable schemaName="payment" tableName="paypal_transaction_data">
            <column name="id" type="VARCHAR(64)">
                <constraints nullable="false"
                             primaryKey="true"/>
            </column>
            <column name="transaction_id" type="VARCHAR(24)"/>
            <column name="reference_id" type="VARCHAR(24)"/>
            <column name="reference_id_type" type="VARCHAR(3)"/>
            <column name="event_code" type="VARCHAR(5)"/>
            <column name="initiation_date" type="TIMESTAMP"/>
            <column name="updated_date" type="TIMESTAMP"/>
            <column name="amount" type="DECIMAL(15,4)"/>
            <column name="currency_code" type="VARCHAR(3)"/>
            <column name="fee_amount" type="DECIMAL(15,4)"/>
            <column name="status" type="VARCHAR(1)"/>
            <column name="custom_field" type="VARCHAR(36)"/>
            <column name="user_account_id" type="VARCHAR(13)"/>
            <column name="user_email_address" type="VARCHAR(254)"/>
            <column name="user_given_name" type="VARCHAR(140)"/>
            <column name="user_surname" type="VARCHAR(140)"/>
            <column name="user_country_code" type="VARCHAR(2)"/>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createIndex schemaName="payment" tableName="paypal_transaction_data" indexName="idx_paypal_transaction_data__transaction_id">
            <column name="transaction_id"/>
        </createIndex>
        <createIndex schemaName="payment" tableName="paypal_transaction_data" indexName="idx_paypal_transaction_data__initiation_date">
            <column name="initiation_date"/>
        </createIndex>
        <sql>
            ALTER TABLE payment.paypal_transaction_data MODIFY COLUMN updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
        </sql>
        <createTable schemaName="payment" tableName="cron_temp_data">
            <column name="id" type="INT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="pk_cron_temp_data"/>
            </column>
            <column name="cron_job" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="paypal_transactions_loaded_until" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="processed_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <sql>
            INSERT INTO `payment`.`cron_temp_data` (`cron_job`, `paypal_transactions_loaded_until`, `processed_at`)
            VALUES ('loadPayPalTransactions', now(), now());
        </sql>
    </changeSet>

    <changeSet id="store-paypal-transactions-data-2" author="aleksey.romantsov"
               labels="https://app.asana.com/0/****************/****************/f">
        <addColumn schemaName="payment" tableName="paypal_transaction_data">
            <column name="transaction_subject" type="VARCHAR(256)"/>
        </addColumn>
    </changeSet>

    <changeSet id="store-paypal-transactions-data-3" author="aleksey.romantsov"
               labels="https://app.asana.com/0/****************/****************/f">
        <createTable schemaName="payment" tableName="tango_card_transaction_data">
            <column name="id" type="VARCHAR(100)">
                <constraints nullable="false"
                             primaryKey="true"/>
            </column>
            <column name="initiated_at" type="TIMESTAMP"/>
            <column name="account_number" type="VARCHAR(15)"/>
            <column name="currency_code" type="VARCHAR(3)"/>
            <column name="total" type="DECIMAL(15,4)"/>
            <column name="value" type="DECIMAL(15,4)"/>
            <column name="exchange_rate" type="DECIMAL(15,8)"/>
            <column name="utid" type="VARCHAR(15)"/>
            <column name="reward_name" type="VARCHAR(100)"/>
            <column name="recipient_email" type="VARCHAR(256)"/>
            <column name="recipient_first_name" type="VARCHAR(100)"/>
            <column name="recipient_last_name" type="VARCHAR(100)"/>
            <column name="status" type="VARCHAR(20)"/>
            <column name="sender_email" type="VARCHAR(100)"/>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createIndex schemaName="payment" tableName="tango_card_transaction_data" indexName="idx_tango_card_transaction_data__initiated_at">
            <column name="initiated_at"/>
        </createIndex>
        <sql>
            ALTER TABLE payment.tango_card_transaction_data MODIFY COLUMN updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
        </sql>
        <addColumn schemaName="payment" tableName="cron_temp_data">
            <column name="tango_card_transactions_loaded_until" type="TIMESTAMP"/>
        </addColumn>
        <sql>
            ALTER TABLE `payment`.`cron_temp_data` MODIFY COLUMN paypal_transactions_loaded_until timestamp NULL;
            INSERT INTO `payment`.`cron_temp_data` (`cron_job`, `tango_card_transactions_loaded_until`, `processed_at`)
            VALUES ('loadTangoCardTransactions', now(), now());
        </sql>
    </changeSet>

    <changeSet id="store-paypal-transactions-data-4" author="aleksey.romantsov"
               labels="https://app.asana.com/0/****************/****************/f">
        <addColumn schemaName="payment" tableName="cron_temp_data">
            <column name="processed_until" type="TIMESTAMP"/>
        </addColumn>
        <sql>
            UPDATE `payment`.`cron_temp_data` SET processed_until = COALESCE(paypal_transactions_loaded_until, tango_card_transactions_loaded_until)
        </sql>
    </changeSet>

    <changeSet id="account-sync-invoices-creation" author="aleksey.romantsov"
               labels="https://app.asana.com/0/****************/****************/f">
        <createTable schemaName="payment" tableName="invoices">
            <column name="id" type="INT" autoIncrement="true" startWith="*********">
                <constraints primaryKey="true" primaryKeyName="pk_invoices"/>
            </column>
            <column name="invoice_date" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="invoice_type" type="VARCHAR(10)">
                <constraints nullable="false"/>
            </column>
            <column name="recipient_full_name" type="VARCHAR(128)">
                <constraints nullable="false"/>
            </column>
            <column name="country_code" type="VARCHAR(2)">
                <constraints nullable="false"/>
            </column>
            <column name="email" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="address" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="provider" type="VARCHAR(16)">
                <constraints nullable="false"/>
            </column>
            <column name="cashout_transaction_id" type="VARCHAR(36)"/>
            <column name="external_transaction_id" type="VARCHAR(100)">
                <constraints nullable="false" unique="true" uniqueConstraintName="unique_invoices_external_transaction_id"/>
            </column>
            <column name="pdf_url" type="VARCHAR(250)"/>
            <column name="amount" type="DECIMAL(15,4)">
                <constraints nullable="false"/>
            </column>
            <column name="fee" type="DECIMAL(15,4)"/>
            <column name="total" type="DECIMAL(15,4)">
                <constraints nullable="false"/>
            </column>
            <column name="currency_code" type="VARCHAR(3)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="account-sync-invoices-creation-2" author="aleksey.romantsov"
               labels="https://app.asana.com/0/****************/****************/f">
        <dropColumn schemaName="payment" tableName="cron_temp_data" columnName="tango_card_transactions_loaded_until"/>
        <dropColumn schemaName="payment" tableName="cron_temp_data" columnName="paypal_transactions_loaded_until"/>
        <sql>
            INSERT INTO `payment`.`cron_temp_data` (`cron_job`, `processed_at`, `processed_until`)
            VALUES ('createTangoCardInvoices', now(), now());
            INSERT INTO `payment`.`cron_temp_data` (`cron_job`, `processed_at`, `processed_until`)
            VALUES ('createPayPalInvoices', now(), now());
        </sql>
        <addNotNullConstraint schemaName="payment" tableName="tango_card_transaction_data" columnName="initiated_at" columnDataType="TIMESTAMP"/>
    </changeSet>

    <changeSet id="account-sync-invoices-creation-3" author="aleksey.romantsov"
               labels="https://app.asana.com/0/****************/****************/f">
        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists schemaName="payment" tableName="tango_card_transactions" indexName="idx_tango_card_transactions__reference_id"/>
            </not>
        </preConditions>
        <createIndex schemaName="payment" tableName="tango_card_transactions" indexName="idx_tango_card_transactions__reference_id">
            <column name="reference_id"/>
        </createIndex>
    </changeSet>

    <changeSet id="account-sync-invoices-creation-4" author="aleksey.romantsov"
               labels="https://app.asana.com/0/****************/****************/f">
        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists schemaName="payment" tableName="paypal_transaction_data" indexName="idx_paypal_transaction_data__reference_id"/>
            </not>
        </preConditions>
        <createIndex schemaName="payment" tableName="paypal_transaction_data" indexName="idx_paypal_transaction_data__reference_id">
            <column name="reference_id"/>
        </createIndex>
    </changeSet>

    <changeSet id="account-sync-invoices-creation-5" author="aleksey.romantsov"
               labels="https://app.asana.com/0/****************/****************/f">
        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists schemaName="payment" tableName="paypal_transaction_data" indexName="idx_paypal_transaction_data__custom_field"/>
            </not>
        </preConditions>
        <createIndex schemaName="payment" tableName="paypal_transaction_data" indexName="idx_paypal_transaction_data__custom_field">
            <column name="custom_field"/>
        </createIndex>
    </changeSet>

    <changeSet id="account-sync-invoices-creation-6" author="aleksey.romantsov"
               labels="https://app.asana.com/0/****************/****************/f">
        <createIndex schemaName="payment" tableName="invoices" indexName="idx_invoices__cashout_transaction_id">
            <column name="cashout_transaction_id"/>
        </createIndex>
        <createIndex schemaName="payment" tableName="invoices" indexName="idx_invoices__invoice_date">
            <column name="invoice_date"/>
        </createIndex>
    </changeSet>

    <changeSet id="account-sync-credit-notes-pdfs" author="aleksey.romantsov"
               labels="https://app.asana.com/0/****************/****************/f">
        <createTable schemaName="payment" tableName="credit_notes_reports">
            <column name="id" type="INT" autoIncrement="true" startWith="1">
                <constraints primaryKey="true" primaryKeyName="pk_credit_notes_reports"/>
            </column>
            <column name="period" type="DATE"/>
            <column name="provider" type="VARCHAR(16)"/>
            <column name="pdf_url" type="VARCHAR(250)"/>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="store-addresses-in-payments" author="aleksey.romantsov"
               labels="https://app.asana.com/0/****************/****************/f">
        <addColumn schemaName="payment" tableName="payments">
            <column name="address" type="VARCHAR(250)" defaultValue="NULL"/>
        </addColumn>
    </changeSet>

    <changeSet id="remove-process-bonuses-and-process-earnings-tables" author="cip.malos"
               labels="https://app.asana.com/0/****************/1202028576842794">
        <dropTable schemaName="payment" tableName="process_bonuses"/>
        <dropTable schemaName="payment" tableName="process_earnings"/>
    </changeSet>

    <changeSet id="fix-updated-at" author="alexandr.potolitcyn"
               labels="https://app.asana.com/0/****************/1202108075987418/f">
        <sql>
            ALTER TABLE payment.invoices MODIFY COLUMN updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
            ALTER TABLE payment.credit_notes_reports MODIFY COLUMN updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
        </sql>
    </changeSet>

    <changeSet id="extending-email-column-size-1" author="andrei.khripushin"
               labels="https://app.asana.com/0/****************/****************/f">
        <sql>
            ALTER TABLE payment.tango_card_transaction_data MODIFY COLUMN sender_email VARCHAR(255);
        </sql>
    </changeSet>

    <changeSet id="extending-email-column-size-2" author="andrei.khripushin"
               labels="https://app.asana.com/0/****************/****************/f">
        <sql>
            ALTER TABLE payment.invoices MODIFY COLUMN email VARCHAR(255);
        </sql>
    </changeSet>

    <changeSet id="extending-email-column-size-3" author="andrei.khripushin"
               labels="https://app.asana.com/0/****************/****************/f">
        <sql>
            ALTER TABLE payment.payments MODIFY COLUMN recipient_email VARCHAR(255);
        </sql>
    </changeSet>

    <changeSet id="new-paypal-account-exp" author="aleksey.romantsov"
               labels="https://app.asana.com/0/****************/****************/f">
        <addColumn schemaName="payment" tableName="payments">
            <column name="account" type="VARCHAR(30)"/>
        </addColumn>
    </changeSet>

    <changeSet id="tremendous-payment-provider" author="vladimir.maksimenko"
               labels="https://app.asana.com/0/****************/****************/f">
        <createTable schemaName="payment" tableName="tremendous_transactions">
            <column name="transaction_id" type="VARCHAR(36)">
                <constraints nullable="false"
                             primaryKey="true"/>
            </column>
            <column name="reference_id" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="fee" type="DECIMAL(8,2)">
                <constraints nullable="false"/>
            </column>
            <column name="link" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="let-tremendous-reward-link-be-empty" author="vladimir.maksimenko"
               labels="https://app.asana.com/0/****************/1204469255863503/f">
        <sql>
            ALTER TABLE `payment`.`tremendous_transactions` MODIFY COLUMN link VARCHAR(255) NULL;
        </sql>
    </changeSet>

    <changeSet id="tremendous-balance-table" author="maksim.kalashnikov"
               labels="https://app.asana.com/0/1204050378849021/1204619499680019">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists schemaName="payment" tableName="tremendous_balance"/>
            </not>
        </preConditions>
        <createTable schemaName="payment" tableName="tremendous_balance">
            <column name="balance_cents" type="INT" />
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <sql>
            ALTER TABLE payment.tremendous_balance MODIFY COLUMN updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

            INSERT INTO payment.tremendous_balance VALUES (0, CURRENT_TIMESTAMP);

            ALTER TABLE payment.tremendous_balance MODIFY COLUMN balance_cents INT NOT NULL;
        </sql>
    </changeSet>

    <changeSet id="remove-unused-payment-tables" author="aleksey.romantsov"
               labels="https://app.asana.com/0/****************/****************/f">
        <sql>
            DROP TABLE IF EXISTS
            payment.credit_notes_reports,
            payment.cron_temp_data,
            payment.invoices,
            payment.paypal_transaction_data,
            payment.tango_card_transaction_data;
        </sql>
    </changeSet>

    <changeSet id="paypal-account-settings" author="cip.malos"
               labels="https://app.asana.com/0/****************/****************">
        <createTable schemaName="payment" tableName="settings">
            <column name="id" type="INT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="pk_settings"/>
            </column>
            <column name="percentage_of_jp_paypal_account_payments" type="DOUBLE"/>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="NOW()"/>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="NOW() ON UPDATE NOW()"/>
        </createTable>
        <sql>
            INSERT INTO payment.settings (percentage_of_jp_paypal_account_payments)
            VALUES (100.0);
        </sql>
    </changeSet>

    <changeSet id="paypal-us-config-columns" author="cip.malos"
               labels="https://app.asana.com/0/****************/****************">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists schemaName="payment" tableName="settings" columnName="minimum_amount_for_usd_paypal_account"/>
            </not>
        </preConditions>
        <addColumn schemaName="payment" tableName="settings">
            <column name="minimum_amount_for_usd_paypal_account" type="DECIMAL(6,2)"/>
        </addColumn>
        <sql>
            UPDATE payment.settings SET minimum_amount_for_usd_paypal_account = 100.00
        </sql>
    </changeSet>

    <changeSet id="currency-exchange-rate-tracking" author="cip.malos"
               labels="https://app.asana.com/0/****************/****************">
        <createTable schemaName="payment" tableName="currency_exchange_rate">
            <column name="currency_code" type="VARCHAR(3)">
                <constraints nullable="false"
                             primaryKey="true"/>
            </column>
            <column name="exchange_rate" type="DECIMAL(16,6)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <sql>
            ALTER TABLE payment.currency_exchange_rate MODIFY COLUMN updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
        </sql>

        <createTable schemaName="payment" tableName="currency_exchange_rate_audit">
            <column name="id" type="INT" autoIncrement="true">
                <constraints nullable="false"
                             primaryKey="true"/>
            </column>
            <column name="time_stamp" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="currency_code" type="VARCHAR(3)">
                <constraints nullable="false"/>
            </column>
            <column name="exchange_rate" type="DECIMAL(16,6)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="paypal-gmc-config-column" author="cip.malos"
               labels="https://app.asana.com/0/****************/****************">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists schemaName="payment" tableName="settings" columnName="percentage_of_gmc_paypal_account_payments"/>
            </not>
        </preConditions>
        <addColumn schemaName="payment" tableName="settings">
            <column name="percentage_of_gmc_paypal_account_payments" type="DOUBLE"/>
        </addColumn>
        <sql>
            UPDATE payment.settings SET percentage_of_gmc_paypal_account_payments = 0.0
        </sql>
    </changeSet>

    <changeSet id="add_encrypted_columns" author="maksim.kalashnikov"
               labels="https://app.asana.com/0/****************/****************">
        <sql>
            ALTER TABLE payment.payments ADD COLUMN encrypted_recipient_name TEXT;
            ALTER TABLE payment.payments ADD COLUMN encrypted_recipient_email TEXT;
            ALTER TABLE payment.payments ADD COLUMN encrypted_address TEXT;
        </sql>
    </changeSet>

    <changeSet id="paypal-venmo-handle-column" author="cip.malos"
               labels="https://app.asana.com/0/****************/****************">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists schemaName="payment" tableName="payments" columnName="recipient_handle"/>
            </not>
        </preConditions>
        <addColumn schemaName="payment" tableName="payments">
            <column name="recipient_handle" type="VARCHAR(30)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="encryption-cron-jobs" author="maksim.kalashnikov"
               labels="https://app.asana.com/0/****************/1207529802053398">
        <createTable schemaName="payment" tableName="cron_temp_data">
            <column name="id" type="INT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="pk_cron_temp_data"/>
            </column>
            <column name="cron_job" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="last_chunk_first_letter" type="VARCHAR(1)">
                <constraints nullable="false"/>
            </column>
            <column name="processed_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <sql>
            INSERT INTO `payment`.`cron_temp_data` (`cron_job`, `last_chunk_first_letter`, `processed_at`)
            VALUES ('fillPaymentsTableEncryptedFields', '0', now());
        </sql>
    </changeSet>

    <changeSet id="add-default-values-to-old-fields" author="maksim.kalashnikov"
               labels="https://app.asana.com/0/****************/1207529802053398">
        <sql>
            ALTER TABLE payment.payments MODIFY COLUMN recipient_name VARCHAR(100) NOT NULL DEFAULT '';
        </sql>
    </changeSet>

    <changeSet id="clear-personal-data-cron-jobs" author="maksim.kalashnikov"
               labels="https://app.asana.com/0/****************/1207529802053398">
        <sql>
            INSERT INTO `payment`.`cron_temp_data` (`cron_job`, `last_chunk_first_letter`, `processed_at`)
            VALUES ('clearPaymentsTablePersonalData', '0', now());
        </sql>
    </changeSet>

    <changeSet id="increase-amount-column-size-to-10-for-payment-table-asia" author="cip.malos"
               labels="https://app.asana.com/0/****************/1209039329363702"
               context="asia or asia-test or unit-test">
        <preConditions onFail="MARK_RAN">
            <not>
                <sqlCheck expectedResult="0">
                    SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_NAME = 'payments'
                    AND COLUMN_NAME = 'amount'
                    AND DATA_TYPE = 'decimal'
                    AND NUMERIC_PRECISION = 10
                    AND NUMERIC_SCALE = 2;
                </sqlCheck>
            </not>
        </preConditions>

        <modifyDataType tableName="payments" columnName="amount" newDataType="DECIMAL(10, 2)"/>
    </changeSet>

    <changeSet id="paypal-ios-payment-account-config-column" author="cip.malos"
               labels="INT-272">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists schemaName="payment" tableName="settings" columnName="ios_paypal_payments_account"/>
            </not>
        </preConditions>
        <addColumn schemaName="payment" tableName="settings">
            <column name="ios_paypal_payments_account" type="VARCHAR(20)" defaultValue="PAYPAL_GMC"/>
        </addColumn>
    </changeSet>

    <changeSet id="payments-table-platform-column" author="cip.malos"
               labels="INT-272">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists schemaName="payment" tableName="payments" columnName="platform"/>
            </not>
        </preConditions>
        <addColumn schemaName="payment" tableName="payments">
            <column name="platform" type="VARCHAR(30)"/>
        </addColumn>
    </changeSet>

</databaseChangeLog>
